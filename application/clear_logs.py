import os
import redis
from rq import Queue
from rq.job import Job
from rq.registry import FailedJobRegistry
from dotenv import load_dotenv

load_dotenv()

redis_host = os.getenv('REDIS_HOST', 'localhost')
redis_port = os.getenv('REDIS_PORT', 6379)
redis_pass = os.getenv('REDIS_PASS', 'admin')

conn = redis.Redis(
    host=redis_host,
    port=redis_port,
    password=redis_pass)

queue = Queue(connection=conn)

q = Queue('config_queue', connection=conn)
registry = FailedJobRegistry(queue=q)

for job_id in registry.get_job_ids():
    try:
        job = Job.fetch(job_id, connection=conn)
        job.delete()
        print(f"[config_queue] job {job_id} excluido")
    except Exception as e:
        continue

q = Queue('provision_queue', connection=conn)
registry = FailedJobRegistry(queue=q)

for job_id in registry.get_job_ids():
    try:
        job = Job.fetch(job_id, connection=conn)
        job.delete()
        print(f"[provision_queue] job {job_id} excluido")
    except Exception as e:
        continue

q = Queue('pre_provision_queue', connection=conn)
registry = FailedJobRegistry(queue=q)

for job_id in registry.get_job_ids():
    try:
        job = Job.fetch(job_id, connection=conn)
        job.delete()
        print(f"[pre_provision_queue] job {job_id} excluido")
    except Exception as e:
        continue

