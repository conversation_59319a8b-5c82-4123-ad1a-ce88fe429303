#!/usr/bin/python
# -*- coding: utf-8 -*-

import pexpect
from dotenv import load_dotenv
from database import *
import time
import sys
import traceback
from genieacs import *
from rq import Queue
from rq.job import Job
from worker_provision_queue import conn
from tasks_provision_test import *
import uuid
import datetime

def start_alarm():
  try:
    alarms = getalarms()
    onus = []

    for alarm in alarms:

      str = alarm['calarmexinfo']
      #str = 'physical ID=FHTT022c3ba8;SN:LOID=;SN:password=;|physical ID=ZTEGc8cbf6d7;SN:LOID=;SN:password=;|'
      str = str[:-1]
      rows = str.split('|')
      for row in rows:
        onu = {}
        row = row[:-1]
        fields = row.split(';') 
        for field in fields:
          value = field.split('=')
          if(value[0].lower() == 'physical id'):

            if(alarm['olt_ip'] in [m['ip'] for m in monitors()]):
              onu['source'] = 'ALARMS_RQ[************]'
              onu['serial'] = value[1]
              onu['olt'] = alarm['olt']
              onu['olt_ip'] = alarm['olt_ip']
              s = alarm['clocationinfo'] 
              onu['placa'] = int(s[s.find("[")+1:s.find("]")])
              onu['porta'] = int(alarm['porta'])
              onu['data'] = alarm['data']
              onu['mac'] = None
              onu['username'] = None 
              onu['modelo'] = None 
              onu['patrimonio'] = None 
              onu['login_ativo'] = None
              onu['data_comodato'] = None
              onu['id_comodato'] = None
              onu['status'] = 'WAITING'
              onu['olt_model'] = 'FBT'
              
              patrimonio = getpatrimonio_alocado(onu['serial'])  

              if(patrimonio):
                onu['mac'] = patrimonio['mac']
                onu['modelo'] = patrimonio['modelo']
                onu['patrimonio'] = patrimonio['patrimonio']
                onu['login_ativo'] = patrimonio['login_ativo']
                onu['data_comodato'] = patrimonio['data_comodato']
                onu['id_comodato'] = patrimonio['id_comodato']
                onu['username'] = patrimonio['login']  
                onu['nome'] = patrimonio['login']  
                onu['login_ativo'] = patrimonio['login_ativo']
                onu['plano'] = patrimonio['plano']

                if('ZTE' in onu['serial'].upper()):
                  onu['tr069'] = 1
                else:
                  onu['tr069'] = 0  

                print(onu["serial"])  
                if(onu["serial"].upper() != 'ZTEGD14E85A3'):
                  msg = 'ONU != Teste'
                  print(msg)
                else: 
                  #verifica se ja existe um provisionamento em andamento 
                  if(pending_provision(onu['serial'])):
                    print('{} Provisionamento pendente'.format(onu['serial'])) 
                  else:
                    #verifica se a onu atende os requisitos para o provisionamento
                    #missing_params = task_checkonu(onu)
                    #if(len(missing_params) == 0):
                    print('ONU sera provisionada no sistema novo') 
                    print(onu)
                    task_provision(onu)
                    '''
                    else:
                      current_provision= {
                        'id' : uuid.uuid4().hex, 
                        'serial': onu['serial'],
                        'username' : onu['username'],
                        'olt_ip': onu["olt_ip"],
                        'slot': onu['placa'],
                        'pon': onu['porta'],
                        'model': onu['modelo'],
                        'source': onu['source'],
                        'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
                        'exc_info': 'Faltando parametros: {}'.format(missing_params),
                        'enqueued_at': datetime.datetime.now()
                      }
                    '''  

                      #provision_id = update_provision(current_provision)
                      #print('{} Faltando parametros: {}'.format(onu['serial'], missing_params))
              else:
                current_provision= {
                        'id' : uuid.uuid4().hex, 
                        'serial': onu['serial'],
                        'username' : onu['username'],
                        'olt_ip': onu["olt_ip"],
                        'slot': onu['placa'],
                        'pon': onu['porta'],
                        'model': onu['modelo'],
                        'source': onu['source'],
                        'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
                        'exc_info': 'ONU sem patrimônio',
                        'enqueued_at': datetime.datetime.now()
                }
                provision_id = update_provision(current_provision)
                msg = 'ONU {} sem patrimonio'.format(onu["serial"])
                print(msg)        
    
  except:
    raise
  

start_alarm()
