import time
from redis import Redis
from worker_provision_queue import conn
from rq import Queue
#from rq.registry import StartedJobRegistry, DeferredJobRegistry
from dotenv import load_dotenv
from database import *
from rq.job import Job

'''
job = {
    'id' : '8553b23a-a968-4708-9f0e-b954f788447b'
}

update_job(job)
'''


load_dotenv()

queue = Queue('provision_queue_100unm', connection=conn)

job = Job.fetch('5846ecc1-bdee-42e5-b5c9-866aa255b735', connection=conn)

print('Status: %s' % job.get_status(refresh=True))
print('Meta: %s' % job.get_meta(refresh=True))
print('Origin: %s' % job.origin)
print('Func name: %s' % job.func_name)
#print('Args: %s' % job.args)
print('kwargs: %s' % job.kwargs)
print('job.enqueued_at: %s' % job.enqueued_at)
print('job.started_at: %s' % job.started_at)
print('job.ended_at: %s' % job.ended_at)
print('job.exc_info: %s' % job.exc_info)
print('job.last_heartbeat: %s' % job.last_heartbeat)
print('job.worker_name: %s' % job.worker_name)

'''

# get StartedJobRegistry by queue
#registry = StartedJobRegistry(queue=queue)
#registry = DeferredJobRegistry(queue=queue)




# sleep for a moment while job is taken off the queue
#time.sleep(0.1)

#print('Queue associated with the registry: %s' % registry.get_queue())
#print('Number of jobs in registry %s' % registry.count)

# get the list of ids for the jobs in the registry
#print('IDs in registry %s' % registry.get_job_ids())

# test if a job is in the registry using the job instance or job id
#print('Job in registry %s' % (job in registry))
#print('Job in registry %s' % (job.id in registry))
'''