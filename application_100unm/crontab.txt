* * * * *       /usr/bin/env bash -c 'cd /var/www/provision/application_100unm && source /var/www/provision/application_100unm/venv/bin/activate && python ./discovery_fh_rq_aux.py' > /dev/null 2>&1
* * * * *       ( sleep 30 ; /usr/bin/env bash -c 'cd /var/www/provision/application_100unm && source /var/www/provision/application_100unm/venv/bin/activate && python ./discovery_fh_rq_aux.py' > /dev/null 2>&1 )
*/2 * * * *     /usr/bin/env bash -c 'cd /var/www/provision/application_100unm && source /var/www/provision/application_100unm/venv/bin/activate && python ./discovery_fh.py' > /dev/null 2>&1
*/2 * * * *     /usr/bin/env bash -c 'cd /var/www/provision/application_100unm && source /var/www/provision/application_100unm/venv/bin/activate && python ./discovery_fh_rq.py' > /dev/null 2>&1
* * * * *       /usr/bin/env bash -c 'cd /var/www/provision/application_100unm && source /var/www/provision/application_100unm/venv/bin/activate && python ./discovery_zte.py' > /dev/null 2>&1
* * * * *     /usr/bin/env bash -c 'cd /var/www/provision/application_100unm && source /var/www/provision/application_100unm/venv/bin/activate && python ./discovery_zte_rq.py' > /dev/null 2>&1
*/10 * * * *     /usr/bin/env bash -c 'cd /var/www/provision/application_100unm && source /var/www/provision/application_100unm/venv/bin/activate && python ./finish_pending_provisions.py' > /dev/null 2>&1