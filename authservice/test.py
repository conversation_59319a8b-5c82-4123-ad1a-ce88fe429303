#!/usr/bin/python
# -*- coding: utf-8 -*-

from database import *
from config import *
from cli import *
from multiprocessing import Process
import time
import pexpect
import sys
from datetime import date
from genieacs import *


scriptname = 'AUTHSERVICE_DISCOVERY'

def discovery(olt, placa, porta):
  try:
    onus = []
    #print("Conectando via telnet na OLT {} ({})".format(olt['nome'], olt['ip']))
    child = pexpect.spawn('telnet {}'.format(olt['ip']), encoding='utf-8', codec_errors='ignore')
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', 5)
    child.sendline(CLI_USER)
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('User> ', 5)
    child.sendline("enable")
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('Admin# ', 30)
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', 5)
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if (rp1000):
      child.sendline('cd onu')
      expectstr = 'onu#'  
    else:
      child.sendline('cd gpononu')
      expectstr = 'gpononu#'

    child.expect(expectstr, 5)  
    unauthorized = []
    if(placa == None):
      slots = slotlist(olt['ip'])
    else:
      slots = [{'slot' : placa, 'pons' : 16}]
    
    for slot in slots:
      if(porta == None):
        pons = []
        for i in range(slot['pons']):
          pon = {}
          pon['placa'] = slot['slot']
          pon['porta'] = i+1
          pons.append(pon) 
      else:
        pons = []
        pon = {}
        pon['placa'] = placa
        pon['porta'] = porta
        pons.append(pon) 

      for pon in pons:
        #print("[{}] Capturando onus nao autorizadas na OLT {} ({}) Placa {} Porta {}".format(scriptname, olt['nome'], olt['ip'], pon['placa'], pon['porta']))
        ret = ""
        if (rp1000):
          child.sendline('show discovery slot {} pon {}'.format(pon['placa'], pon['porta']))
          child.expect(r'Admin\\onu# ', 5)
          ret += child.before
          child.sendline('clear')
          child.expect(r'Admin\\onu# ', 5)
        else:  
          child.sendline('show discovery slot {} link {}'.format(pon['placa'], pon['porta']))
          child.expect(r'Admin\\gpononu# ', 5)
          ret += child.before
          child.sendline('clear')
          child.expect(r'Admin\\gpononu# ', 5)

        data = ret.splitlines()
        return ret

        if (len(data) > 1):
          header = ((data[1].replace(' ', '').replace('-', '')).split(','))
          if (len(header) > 2):
            value = header[2].split('=')
            count = value[1]

            # caso tenha alguma onu na lista, os dados se iniciam no indice 7
            list = data[7:]
            # remove itens vazios
            while("" in list):
                list.remove("")

            for s in list:
              # normaliza a string deixando com apenas um espaco por valor
              s = ' '.join(s.split())
              # divide os valores em lista, separando cada valor por espaco
              s = s.split(' ')
              onu = {
                  #'olt': olt["nome"],
                  'olt': olt["descricao"],
                  'olt_ip': olt["ip"],
                  'placa': pon['placa'],
                  'porta': pon['porta'],
                  'serial': s[2],
                  'modelo': s[1]
              }
              onu['data'] = date.today()
              onu['mac'] = None
              onu['login'] = None 
              onu['modelo'] = None 
              onu['patrimonio'] = None 
              onu['login_ativo'] = None
              onu['data_comodato'] = None
              onu['id_comodato'] = None
              onu['status'] = 'WAITING'

              patrimonio = getpatrimonio_alocado(onu['serial']) 

              if(patrimonio):
                onu['mac'] = patrimonio['mac']
                onu['modelo'] = patrimonio['modelo']
                onu['patrimonio'] = patrimonio['patrimonio']
                onu['login_ativo'] = patrimonio['login_ativo']
                onu['data_comodato'] = patrimonio['data_comodato']
                onu['id_comodato'] = patrimonio['id_comodato']
                onu['username'] = patrimonio['login']  
                onu['login_ativo'] = patrimonio['login_ativo']
                onu['plano'] = patrimonio['plano']

                if('ZTE' in onu['serial'].upper()):
                  onu['tr069'] = 1
                  #onu['modelo'] = 'GPON CURRENCY SFU'
                else:
                  onu['tr069'] = 0  
              
              if(((len(ONUS_TESTE) > 0) and (onu['serial'].upper() in ONUS_TESTE) and (TEST_MODE)) or (TEST_MODE == False)): 
                if(IGNORE_LIST_ENABLED):
                  #if(checkignorelist(onu["serial"], 0)):
                  if(checkignorelist(serial=onu["serial"])):
                    msg = '[{}]  ONU {} esta na lista para ignorar e nao sera autorizada'.format(scriptname, onu["serial"])
                    print(msg)
                  else:  
                    onus.append(onu)  
                else:    
                  onus.append(onu)

    update_unauthorized(onus, scriptname)
    child.close()
    exit()
  except Exception as e:
    errorlog('Falha em discovery: '+ str(e)) 


if __name__ == '__main__':
  try:

    #olts = unm_oltlist()
    olts = olt_list('FBT')

    proccess = []

    for olt in olts:
      #if('PCS' in olt['nome'].upper()): # apenas olts de pocos
      p = Process(target=discovery, args=(olt,None, None,))
      p.start()
      proccess.append(p)
  except Exception as e:
    print('Ocorreu um erro em __main__: '+str(e))    


#print(discovery({'nome': 'OLT Pcs MariaAGEstrela_1', 'ip' : '***********'}, 11, 1))
print(discovery({'nome':'Pcs Manhattan CPD 1', 'ip' : '***********'}, 17, 12))
#discovery({'nome': 'Pcs Manhattan CPD 1', 'ip' : '***********'}, None, None)
#discovery({'nome': 'Ads Shopping CPD 1', 'ip' : '************'}, None, None)
