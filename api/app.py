from flask import Flask, request, jsonify, make_response, Blueprint
from ztemanager import ZTEManager
from db import olt_list, getdevice, removedevice, updatedevice
from functools import wraps
import jwt
from  werkzeug.security import generate_password_hash, check_password_hash
from dotenv import load_dotenv
import os
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta
from flask_cors import CORS
import logging

from rq import Queue, Retry
from rq.job import Job
from worker_config_queue import conn
from routes.ixc import ixc_route
from routes.vlans import vlan_route
from routes.signal import signal_route
from routes.auth import auth_route
from routes.provisions import provision_route
from routes.jobs import job_route
import json
import time

# obrigado, chatgpt!
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            # Customize the datetime format here
            return obj.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        return json.JSONEncoder.default(self, obj)


app = Flask(__name__)
app.json_encoder = CustomJSONEncoder
redis_host = os.getenv('REDIS_HOST', 'localhost')
redis_port = os.getenv('REDIS_PORT', 6379)
redis_user = os.getenv('REDIS_USER', 'admin')
redis_pass = os.getenv('REDIS_PASS', 'admin')

app.register_blueprint(ixc_route, url_prefix="/ixc")
app.register_blueprint(vlan_route, url_prefix="/onu")
app.register_blueprint(signal_route, url_prefix="/onu")
app.register_blueprint(auth_route, url_prefix="/auth")
app.register_blueprint(provision_route, url_prefix="/provision")
app.register_blueprint(job_route, url_prefix="/jobs")

load_dotenv()
app.config['SECRET_KEY'] = os.getenv('APP_SECRET')
app.config['CORS_HEADERS'] = 'Content-Type'
#CORS(app)
CORS(app, support_credentials=True)


logging.basicConfig(filename='api.log', level=logging.DEBUG, format=f'%(asctime)s %(levelname)s %(name)s %(threadName)s : %(message)s')

# Middleware para aguardar a conclusão de jobs
@app.before_request
def wait_for_jobs_middleware():
    # Verifica se o parâmetro wait_jobs está presente
    wait_jobs = None
    if request.method == 'POST':
        if request.form and 'wait_jobs' in request.form:
            wait_jobs = request.form.get('wait_jobs')
        elif request.data:
            try:
                data = json.loads(request.data.decode())
                if 'wait_jobs' in data:
                    wait_jobs = data.get('wait_jobs')
            except:
                pass
    
    # Se não houver jobs para aguardar, continua normalmente
    if not wait_jobs:
        return None
    
    # Converte para lista se for uma string
    job_ids = wait_jobs if isinstance(wait_jobs, list) else [wait_jobs]
    
    # Timeout de 2 minutos (120 segundos)
    timeout = 120
    start_time = time.time()
    
    # Aguarda a conclusão dos jobs
    for job_id in job_ids:
        try:
            job = Job.fetch(job_id, connection=conn)
            while job.get_status() not in ['finished', 'failed'] and (time.time() - start_time) < timeout:
                time.sleep(0.5)

                print('waiting for job {}'.format(job_id))
            
            # Se o timeout foi atingido
            if (time.time() - start_time) >= timeout:
                return jsonify({'status': 'ERROR', 'msg': f'Timeout waiting for job(s): {job_ids}'}), 408
            
            # Se o job falhou
            if job.get_status() == 'failed':
                return jsonify({'status': 'ERROR', 'msg': f'Job {job_id} failed'}), 500
        except Exception as e:
            return jsonify({'status': 'ERROR', 'msg': f'Error fetching job {job_id}: {str(e)}'}), 500
    
    # Todos os jobs foram concluídos com sucesso
    return None
 
@app.errorhandler(404)
def page_not_found(e):
     return jsonify({'message' : 'Method not found !!'}), 404

@app.route("/")

def hello():
    return app.response_class("PROVISION API v1.0", mimetype='text/plain')

@app.route('/login', methods =['POST'])
def login():
    auth = request.get_json()
  
    if not auth or not auth.get('user') or not auth.get('password'):
        return make_response(
            'Could not verify',
            401,
            {'WWW-Authenticate' : 'Basic realm ="Login required !!"'}
        )
  
    if auth.get('user') != os.getenv('API_USER'):
        return make_response(
            'Could not verify',
            401,
            {'WWW-Authenticate' : 'Basic realm ="User does not exist !!"'}
        )
  
    if auth.get('password') == os.getenv('API_PASSWORD'):
        token = jwt.encode({
            "iat": datetime.now(tz=timezone.utc),
            'exp' : datetime.utcnow() + relativedelta(years=999)}, 
            app.config['SECRET_KEY'], "HS256")
    
        return make_response(jsonify({'token' : token}), 201)
    # returns 403 if password is wrong
    return make_response(
        'Could not verify',
        403,
        {'WWW-Authenticate' : 'Basic realm ="Wrong Password !!"'}
    )

if __name__ == "__main__":
    try:
        app.run(port=8900, host='0.0.0.0', debug=True, ssl_context=('/etc/letsencrypt/live/provision.telemidia.net.br/cert.pem', '/etc/letsencrypt/live/provision.telemidia.net.br/privkey.pem'))
    except Exception as e:
        print(str(e))