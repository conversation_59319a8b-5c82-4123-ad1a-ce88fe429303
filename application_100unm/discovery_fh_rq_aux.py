﻿#!/usr/bin/python
# -*- coding: utf-8 -*-

import pexpect
from dotenv import load_dotenv
from database import *
import time
import sys
import traceback
from genieacs import *
from rq import Queue
from rq.job import Job
from worker_provision_queue import conn
from tasks_provision import *
import uuid
import datetime
from cli_fh import discovery
from multiprocessing import Process

def start_discovery(olt):
  try:

    onus = discovery(olt['ip'])
    
    for onu in onus:
      onu['source'] = 'DISCOVERY_FH_RQ[************]'
      onu['olt'] = olt['name']
      onu['olt_ip'] = olt['ip']
      onu['olt_model'] = 'FBT'
      onu['placa'] = onu['slot']
      onu['porta'] = onu['pon']
      onu['data'] = datetime.datetime.now()
      onu['mac'] = None
      onu['username'] = None 
      onu['modelo'] = None 
      onu['patrimonio'] = None 
      onu['login_ativo'] = None
      onu['data_comodato'] = None
      onu['id_comodato'] = None
      onu['status'] = 'WAITING'

      patrimonio = getpatrimonio_alocado(onu['serial'])  

      if(patrimonio):
        onu['mac'] = patrimonio['mac']
        onu['modelo'] = patrimonio['modelo']
        onu['patrimonio'] = patrimonio['patrimonio']
        onu['login_ativo'] = patrimonio['login_ativo']
        onu['data_comodato'] = patrimonio['data_comodato']
        onu['id_comodato'] = patrimonio['id_comodato']
        onu['username'] = patrimonio['login']  
        onu['nome'] = patrimonio['login']  
        onu['login_ativo'] = patrimonio['login_ativo']
        onu['plano'] = patrimonio['plano']

        if('ZTE' in onu['serial'].upper()):
            onu['tr069'] = 1
        else:
            onu['tr069'] = 0  

        if(checkignorelist(serial=onu["serial"].upper())):
            msg = 'ONU {} esta na lista para ignorar e nao sera autorizada'.format(onu["serial"])
            print(msg)
        else:
            #verifica se ja existe um provisionamento em andamento 
            if(pending_provision(onu['serial'])):
              print('{} Provisionamento pendente'.format(onu['serial'])) 
            else:
              #verifica se a onu atende os requisitos para o provisionamento
              missing_params = task_checkonu(onu)
              
              if(len(missing_params) == 0):
                print('ONU sera provisionada no sistema novo') 
                print(onu)
                task_provision(onu)
              else:
                current_provision= {
                  'id' : uuid.uuid4().hex, 
                  'serial': onu['serial'],
                  'username' : onu['username'],
                  'olt_ip': onu["olt_ip"],
                  'olt_model': 'FBT',
                  'slot': onu['placa'],
                  'pon': onu['porta'],
                  'model': onu['modelo'],
                  'source': onu['source'],
                  'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
                  'exc_info': 'Faltando parametros: {}'.format(missing_params),
                  'enqueued_at': datetime.datetime.now()
                }

                provision_id = update_provision(current_provision)
                print('{} Faltando parametros: {}'.format(onu['serial'], missing_params))
      else:
        current_provision= {
            'id' : uuid.uuid4().hex, 
            'serial': onu['serial'],
            'username' : onu['username'],
            'olt_ip': onu["olt_ip"],
            'olt_model': 'FBT',
            'slot': onu['placa'],
            'pon': onu['porta'],
            'model': onu['modelo'],
            'source': onu['source'],
            'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
            'exc_info': 'ONU sem patrimônio',
            'enqueued_at': datetime.datetime.now()
        }
        #provision_id = update_provision(current_provision)
        msg = 'ONU {} sem patrimonio'.format(onu["serial"])
        print(msg)      

          
    
  except:
    raise
  
if __name__ == '__main__':
  try:

    olts = monitors('FBT')

    proccess = []

    for olt in olts:
      p = Process(target=start_discovery, args=(olt,))
      p.start()
      proccess.append(p)
  except Exception as e:
    print('Ocorreu um erro em __main__: '+str(e))  

