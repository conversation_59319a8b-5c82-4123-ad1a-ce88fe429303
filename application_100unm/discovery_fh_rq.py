#!/usr/bin/python
# -*- coding: utf-8 -*-

from database import *
from dotenv import load_dotenv
import os
from cli import *
from multiprocessing import Process
import time
import pexpect
import sys
import datetime
from genieacs import *
from worker_provision_queue import conn
from tasks_provision import *

load_dotenv()

scriptname = 'DISCOVERY_FH_RQ[************]'

def discovery(olt, placa, porta):
#  try:
    onus = []
    child = pexpect.spawn('telnet {}'.format(olt['ip']), encoding='utf-8', codec_errors='ignore')
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_USER'))
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline("enable")
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    ret = ""
    ret += child.before
    
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if (rp1000):
      child.sendline('cd onu')
      expectstr = 'onu#'  
    else:
      child.sendline('cd gpononu')
      expectstr = 'gpononu#'

    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))  
    unauthorized = []
    if(placa == None):
      slots = slotlist(olt['ip'])
    else:
      slots = [{'slot' : placa, 'pons' : 16}]

    for slot in slots:
      if(porta == None):
        pons = []
        for i in range(slot['pons']):
          pon = {}
          pon['placa'] = slot['slot']
          pon['porta'] = i+1
          pons.append(pon) 
      else:
        pons = []
        pon = {}
        pon['placa'] = placa
        pon['porta'] = porta
        pons.append(pon) 

      for pon in pons:
        #print("[{}] Capturando onus nao autorizadas na OLT {} ({}) Placa {} Porta {}".format(scriptname, olt['nome'], olt['ip'], pon['placa'], pon['porta']))
        ret = ""
        if (rp1000):
          child.sendline('show discovery slot {} pon {}'.format(pon['placa'], pon['porta']))
          child.expect(r'Admin\\onu# ', int(os.getenv('PEXPECT_TIMEOUT')))
          ret += child.before 
          child.sendline('clear')
          child.expect(r'Admin\\onu# ', int(os.getenv('PEXPECT_TIMEOUT')))
        else:  
          child.sendline('show discovery slot {} link {}'.format(pon['placa'], pon['porta']))
          child.expect(r'Admin\\gpononu# ', int(os.getenv('PEXPECT_TIMEOUT')))
          ret += child.before
          child.sendline('clear')
          child.expect(r'Admin\\gpononu# ', int(os.getenv('PEXPECT_TIMEOUT')))
        
        if ('card' in ret or 'linecard' in ret):
          return []
          
        p = re.compile(r"([-+]?\d*\.\d+|\d+) (.*) (.*) (.*)")
        matchs = p.findall(ret)

        for match in matchs:
            s = " ".join(match[1].split())

            s = s.split(' ')
            onu = {
                  'olt': olt["name"],
                  'olt_model': 'FBT',
                  'olt_ip': olt["ip"],
                  'placa': pon['placa'],
                  'porta': pon['porta'],
                  'serial': s[1],
                  'modelo': s[0]
            }
               
            onu['data'] = datetime.datetime.now()
            onu['source'] = 'DISCOVERY_RQ[************]'
            onu['mac'] = None
            onu['username'] = None 
            onu['patrimonio'] = None 
            onu['login_ativo'] = None
            onu['data_comodato'] = None
            onu['id_comodato'] = None
            onu['status'] = 'WAITING'

            patrimonio = getpatrimonio_alocado(onu['serial'])  

            if(patrimonio):
                onu['mac'] = patrimonio['mac']
                onu['modelo'] = patrimonio['modelo']
                onu['patrimonio'] = patrimonio['patrimonio']
                onu['login_ativo'] = patrimonio['login_ativo']
                onu['data_comodato'] = patrimonio['data_comodato']
                onu['id_comodato'] = patrimonio['id_comodato']
                onu['username'] = patrimonio['login']  
                onu['nome'] = patrimonio['login']  
                onu['login_ativo'] = patrimonio['login_ativo']
                onu['plano'] = patrimonio['plano']
                
                if('ZTE' in onu['serial'].upper()):
                    onu['tr069'] = 1
                else:
                    onu['tr069'] = 0  

                if((os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None) and (checkignorelist(serial=onu["serial"].upper()))):
                    msg = 'ONU {} esta na lista para ignorar e nao sera autorizada'.format(onu["serial"])
                    print(msg)
                else:
                    #as olts que estao na tabela authservice.monitors, provisionam através do sistema de task queue (Python RQ)
                    if(onu['olt_ip'] in [m['ip'] for m in monitors()]): 
                    
                    #verifica se ja existe um provisionamento em andamento 
                        if(pending_provision(onu['serial'])):
                            print('{} Provisionamento pendente'.format(onu['serial'])) 
                        else:
                            #verifica se a onu atende os requisitos para o provisionamento
                            missing_params = task_checkonu(onu)
                            if(len(missing_params) == 0):
                                #se for informada uma onu de testes, provisiona apenas esta onu
                                if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                                    print('ONU {} sera provisionada'.format(onu['serial'].upper())) 
                                    print(onu)
                                    task_provision(onu)
                                else:
                                    current_provision= {
                                        'id' : uuid.uuid4().hex, 
                                        'serial': onu['serial'],
                                        'username' : onu['username'],
                                        'olt': onu['olt'],
                                        'olt_ip': onu["olt_ip"],
                                        'olt_model': 'FBT',
                                        'slot': onu['placa'],
                                        'pon': onu['porta'],
                                        'model': onu['modelo'],
                                        'source': onu['source'],
                                        'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
                                        'exc_info': 'Faltando parametros: {}'.format(missing_params),
                                        'enqueued_at': datetime.datetime.now()
                                    }
                                    if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                                        provision_id = update_provision(current_provision)
                                        print('{} Faltando parametros: {}'.format(onu['serial'], missing_params))
            else:
                current_provision= {
                        'id' : uuid.uuid4().hex, 
                        'serial': onu['serial'],
                        'username' : None,
                        'olt': onu['olt'],
                        'olt_ip': onu["olt_ip"],
                        'olt_model': 'FBT',
                        'slot': onu['placa'],
                        'pon': onu['porta'],
                        'model': None,
                        'source': onu['source'],
                        'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
                        'exc_info': 'ONU sem patrimônio',
                        'enqueued_at': datetime.datetime.now()
                }
                if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                    provision_id = update_provision(current_provision)
                    msg = 'ONU {} sem patrimonio'.format(onu["serial"])
                    print(msg)


    child.close()
    exit()

if __name__ == '__main__':
  try:

    olts = monitors('FBT')

    proccess = []

    for olt in olts:
      p = Process(target=discovery, args=(olt,None, None,))
      p.start()
      proccess.append(p)
  except Exception as e:
    print('Ocorreu um erro em __main__: '+str(e))    
