from geniedbclient import GenieDBClient
from dotenv import load_dotenv
import requests
import time
import os
import json


class GenieClient:
    def __init__(self):
        load_dotenv()

    def __post(self, endpoint, payload):
        try:

            url = f"http://{os.getenv('ACS_IP')}:7557/{endpoint}"
                        
            r = requests.post(url, data=json.dumps(payload))

        except Exception as e:
            raise

    def __delete(self, endpoint):
        try:
            url = f"http://{os.getenv('ACS_IP')}:7557/{endpoint}"
            
            r = requests.delete(url)

        except Exception as e:
            raise

    def registered(self, mac):
        try:
            mac = mac.replace(':', '')
            mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

            #consulta a onu no banco do genie
            geniedbclient = GenieDBClient(os.getenv('GENIEACS_MONGODB_CONNECTION_URL'), 'genieacs', 'devices')
            onu = geniedbclient.find_onu_id(mac.lower())
            geniedbclient.close()
        
            return onu if onu else False

        except Exception as e:
            raise 

    def reboot(self, id):
        try:
            payload = {'name':'reboot'}
            
            self.__post(f"devices/{id}/tasks?timeout=10&connection_request", payload)
            
            return True

        except Exception as e:
            raise

    def addtag(self, mac, tag):
        try:
            onu = self.registered(mac)
            if(onu):
                onuid = onu['_id']
                tags = onu['_tags']

                self.__post(f"devices/{onuid}/tags/{tag}", None)

            return True

        except Exception as e:
            raise

    def removetag(self, mac, tag):
        try:
            onu = self.registered(mac)
            if(onu):
                onuid = onu['_id']
                tags = onu['_tags']

                self.__delete(f"devices/{onuid}/tags/{tag}")

            return True

        except Exception as e:
            raise
       
    def removeacs(self, mac):
        try:
            onu = self.registered(mac)
            if(onu):
                onuid = onu['_id']
                tags = onu['_tags']

                if(not 'Reauth' in tags):
                    self.reboot(onuid)
                    self.__delete(f"devices/{onuid}")
                else:
                    self.removetag(mac, 'Reauth')

            return True

        except Exception as e:
            raise    
    
    def changepppoevlanid(self, mac, vlan):
        try:
            onu = self.registered(mac)
            if(onu):
                onuid = onu['_id']

                payload = {
                        "name": "setParameterValues",
                        "parameterValues": [
                            ["InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.2.X_ZTE-COM_VLANID", vlan]
                        ]}
                self.__post(f"devices/{onuid}/tasks?timeout=10&connection_request", payload)

            return True

        except Exception as e:
            raise

    def changeinformperiod(self, mac, period):
        try:
            onu = self.registered(mac)
            if(onu):
                onuid = onu['_id']

                payload = {
                        "name": "setParameterValues",
                        "parameterValues": [
                            ["InternetGatewayDevice.ManagementServer.PeriodicInformEnable", "true"],  
                            ["InternetGatewayDevice.ManagementServer.PeriodicInformInterval", period]
                        ]}
                self.__post(f"devices/{onuid}/tasks?timeout=10&connection_request", payload)

            return True

        except Exception as e:
            raise
