﻿#!/usr/bin/python
# -*- coding: utf-8 -*-

import pexpect
import sys
from database import *
from dotenv import load_dotenv
import os
import re
import time
from multiprocessing import Process
#from tl1 import *

load_dotenv()

def veip_addvlan(onu, serviceid, vlanid, profileid):
  if('onuid' in onu):
    child = pexpect.spawn('telnet {}'.format(onu['olt_ip']), encoding='utf-8')
    
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_USER'))
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline("enable")
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    ret = ""
    ret += child.before#.decode('utf-8')
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if(rp1000):
        child.sendline('cd onu/lan')
        child.expect('lan#', int(os.getenv('PEXPECT_TIMEOUT')))
        expectstr = 'lan#'
    else:
        child.sendline('cd epononu')
        child.expect('epononu# ', int(os.getenv('PEXPECT_TIMEOUT')))
        child.sendline('cd qinq')
        child.expect('qinq#', int(os.getenv('PEXPECT_TIMEOUT')))
        expectstr = 'qinq#'

    child.sendline(
            'set epon slot {} pon {} onu {} port 1 onuveip {} 33024 {} 65535 33024 65535 65535 33024 65535 65535 0 {} 65535'.format(onu['placa'], onu['porta'], onu['onuid'], serviceid, vlanid, profileid))
    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

    child.close()

def veip_addvlans(onu, vlans):
  if('onu_id' in onu):
    #onu = findauthorizedonu(serial)
    child = pexpect.spawn('telnet {}'.format(onu['olt_ip'], encoding='utf-8'))
    
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_USER'))
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline("enable")
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    ret = ""
    ret += child.before#.decode('utf-8')
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if(rp1000):
        child.sendline('cd onu/lan')
        child.expect('lan#', int(os.getenv('PEXPECT_TIMEOUT')))
        expectstr = 'lan#'
    else:
        child.sendline('cd epononu')
        child.expect('epononu# ', int(os.getenv('PEXPECT_TIMEOUT')))
        child.sendline('cd qinq')
        child.expect('qinq#', int(os.getenv('PEXPECT_TIMEOUT')))
        expectstr = 'qinq#'

    for vlan in vlans:
        child.sendline(
            'set epon slot {} pon {} onu {} port 1 onuveip {} 33024 {} 65535 33024 65535 65535 33024 65535 65535 0 {} 65535'.format(onu['placa'], onu['porta'], onu['onu_id'], vlan["serviceid"], vlan["vlanid"], vlan["profileid"]))
        child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

    child.close()

def check_veip_vlans(onu):

    child = pexpect.spawn('telnet {}'.format(onu['olt_ip']), encoding='utf-8')

    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_USER'))
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline("enable")
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if(rp1000):
        child.sendline('cd onu/lan')
        child.expect('lan#', int(os.getenv('PEXPECT_TIMEOUT')))
        expectstr = 'lan#'
    else:
        child.sendline('cd epononu')
        child.expect('epononu# ', int(os.getenv('PEXPECT_TIMEOUT')))
        child.sendline('cd qinq')
        child.expect('qinq#', int(os.getenv('PEXPECT_TIMEOUT')))
        expectstr = 'qinq#'

    child.sendline('show epon slot {} pon {} onu {} onuveip servindex'.format(
        onu['placa'], onu['porta'], onu['onuid']))
    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))    
    ret = ""
    ret += child.before
    child.close()
    #"cvlan 33024 (.*) 65535 tvlan 33024 65535 65535 svlan 33024 65535 65535 tls 0 servmode 2 svlan 65535  servdiff 65535")
    p = re.compile(
        "cvlan 33024 (.*) 65535 tvlan")
    result = map(int, p.findall(ret))
    if result:
        return list(result)
    else:
        return []    

def checkwhitelist(onu):
  try:   
    serial = onu["serial"]  
    child = pexpect.spawn('telnet {}'.format(onu["olt_ip"]), encoding='utf-8')
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_USER'))
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline("enable")
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
  # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if (rp1000):
      child.sendline('cd onu')
      expectstr = 'onu#'
    else:
      child.sendline('cd gpononu')
      expectstr = 'gpononu#'

    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
    
    #padrao physicid = quatro primeiros caracteres maiusculos e o restante minusculo
    serial = serial[:4].upper()+serial[4:].lower()
    child.sendline('show onu-authinfo phy-id {}'.format(serial))
    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
    data = child.before.replace(' ', '')

    if(data.find('Unknowncommand') != -1):
      #caso a olt não possua o comando onu-authinfo, executar o comando a seguir
      child.sendline('show whitelist phy_addr select address {}'.format(serial))
      child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
      data = child.before
      #11    15    32  AN5506-SFU     Auth   ZTEGcced73e9
      match = re.search(r"([-+]?\d*\.\d+|\d+) (.*) ([-+]?\d*\.\d+|\d+) (.*) (.*) (.*)", data)
      if match:
        info = {
            "placa" : int(match.group(1)),
            "porta" : int(match.group(2)),
            "onuid" : int(match.group(3))
        }
    else:
      p = re.compile("ONU:(.*)\r")
      result = p.search(data)
      info = {}
      if(result):
        info = result.group(1)
        info = info.split("-")
        placa = info[0]
        porta = info[1]
        onuid = info[2]
        info = {
            "placa" : placa,
            "porta" : porta,
            "onuid" : onuid
        }
    
    '''
    if(rp1000):
      if("Onu is not authcated" in data):
        return False
      else:
        #return "ONU:" in data
    else:  
      return "Auth-Status:Authed" in data
    '''
    return info  
  except:
    return False          


def removewhitelist(olt, serial):
  try:   
    child = pexpect.spawn('telnet {}'.format(olt), encoding='utf-8')
    #child = pexpect.spawn('telnet {}'.format(olt))
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_USER'))
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline("enable")
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if (rp1000):
      child.sendline('cd onu')
      expectstr = 'onu#'  
    else:
      child.sendline('cd gpononu')
      expectstr = 'gpononu#'

    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
 #   child.sendline('cd gpononu')
    
  #  child.expect('gpononu# ', 5)

    #padrao physicid = quatro primeiros caracteres maiusculos e o restante minusculo
    serial = serial[:4].upper()+serial[4:].lower()

    child.sendline('set whitelist phy_addr address {} password null action delete'.format(serial))
    #child.expect(r'Admin\\gpononu# ', 5)
    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
    data = child.before.replace(' ', '')
#    return "setonuwhitelistok" in data
    result = 'success' in data or 'setonuwhitelistok' in data
    return True
  except:
    return False  

def change_port_isolation(onu, option):
  child = pexpect.spawn('telnet {}'.format(onu['olt_ip']), encoding='utf-8')

  # Habilita o debug (retorno na tela)
  #child.logfile = sys.stdout
  child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_USER'))
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline("enable")
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))

  #child.sendline('cd gpononu')
  #child.expect('gpononu# ', 5)

  # verifica se e rp1000
  child.sendline('dir')
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
  ret = ""
  ret += child.before
  rp1000 = ret.find('<DIR>') != -1
  ######################
  if (rp1000):
    child.sendline('cd onu')
    expectstr = 'onu#'
    cmdstr = 'set port_separate slot {} pon {} onu {} separate {}'
  else:
    child.sendline('cd gpononu')
    expectstr = 'gpononu#'
    cmdstr = 'set port_separate slot {} link {} onu {} separate {}'

  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

  #child.sendline('set port_separate slot {} link {} onu {} separate {}'.format(onu['placa'], onu['porta'], onu['onuid'], option))
  #child.expect('gpononu#', 5)
  child.sendline(cmdstr.format(onu['placa'], onu['porta'], onu['onuid'], option))
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
  child.close()

def discovery(olt_ip):
  child = pexpect.spawn('telnet {}'.format(olt_ip), encoding='utf-8')

  #Habilita o debug (retorno na tela)
  #child.logfile = sys.stdout
  child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_USER'))
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline("enable")
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))

  # verifica se e rp1000
  child.sendline('dir')
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
  ret = ""
  ret += child.before
  rp1000 = ret.find('<DIR>') != -1
  ######################
  if (rp1000):
    child.sendline('cd onu')
    expectstr = 'onu#'  
  else:
    child.sendline('cd gpononu')
    expectstr = 'gpononu#'  

  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline('show unauth')
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
  data = child.before
  child.close()  

  #-----  ONU Unauth Table ,SLOT=1 PON=1 ,ITEM=0-----
  p = re.compile('SLOT=(.*) PON=(.*) ,ITEM=(.*)')
  
  onus = []
  for line in data.splitlines():
    line = line.replace('-', '')
    line = ' '.join(line.split())
    result = p.search(line)
    
    if(result):
        onu = {}
        if (int(result.group(3)) > 0):
            onu['slot'] = int(result.group(1))
            onu['pon'] = int(result.group(2))
    else:
       #01  AN5506-SFU      ZTEGcc4d3acc  GCC4D3ACC   123456789, 123456
       match = re.search(r"([-+]?\d*\.\d+|\d+) (.*) (.*) (.*) (.*), (.*)", line)
       if(match):
          onu['type'] = match.group(2)
          onu['serial'] = match.group(3)
          onus.append(onu)  
  return onus  


# retorna o proximo id livre para provisionar onu
def checkposition(olt, slot, pon):

  child = pexpect.spawn('telnet {}'.format(olt), encoding='utf-8')

  #Habilita o debug (retorno na tela)
  #child.logfile = sys.stdout
  child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_USER'))
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline("enable")
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))

  # verifica se e rp1000
  child.sendline('dir')
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
  ret = ""
  ret += child.before
  rp1000 = ret.find('<DIR>') != -1
  ######################
  if (rp1000):
    child.sendline('cd onu')
    expectstr = 'onu#'  
  else:
    child.sendline('cd gpononu')
    expectstr = 'gpononu#'  

  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline('show authorization slot {} link {}'.format(slot, pon))
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
  data = child.before

  data = ' '.join(data.split())
  #"12  16   1 AN5506-01-A1    A up  FHTT051728b8            , ")
  p = re.compile(r"([-+]?\d*\.\d+|\d+) ([-+]?\d*\.\d+|\d+) ([-+]?\d*\.\d+|\d+)")
  results = map(tuple, p.findall(data))

  # monta uma lista apenas com os ids
  ids = [int(d[2]) for d in results]
  ids.sort()
  start, end = ids[0], ids[-1]
  if(start > 1):
    next_id = 1
  else: 
    # atribui os ids faltantes a variavel d
    d = sorted(set(range(start, end + 1)).difference(ids))
    # atribui a variavel next_id o primeiro id faltante (caso exista) ou incrementa o end
    if(len(d) > 0):
      next_id = next(iter(d))
    else:
      next_id = end+1

  return next_id

def onuauthorize(onu):

  model = re.sub('AN|_|-| ', '', onu["modelo"])
  model_ref = {
    '550601A1' : '5506-01-a1',
    '550602B' : '5506-02-b',
    '550604A1' : '5506-04-a1g',
    '550604B2' : '5506-04-b2'
  }

  if(model in model_ref):
    model_auth = model_ref[model]
  else:
    model_auth = '5506-sfu'  
  
#def onuauthorize(olt, slot, pon, serial, onuid, type):
  child = pexpect.spawn('telnet {}'.format(onu["olt_ip"]), encoding='utf-8')

  #Habilita o debug (retorno na tela)
  #child.logfile = sys.stdout
  child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_USER'))
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline("enable")
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))

  # verifica se e rp1000
  child.sendline('dir')
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
  ret = ""
  ret += child.before
  rp1000 = ret.find('<DIR>') != -1
  ######################
  if (rp1000):
    child.sendline('cd onu')
    expectstr = 'onu#'  
  else:
    child.sendline('cd gpononu')
    expectstr = 'gpononu#'  

  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

  onuid = checkposition(onu["olt_ip"], onu["placa"], onu["porta"])

  child.sendline('set whitelist phy_addr address {} password null action add slot {} link {} onu {} type {}'.format(onu["serial"], onu["placa"], onu["porta"], onuid, model_auth))
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
  data = child.before
  if('ok' in data):
    return onuid
  else:
    raise Exception("Data: {}".format(data))

   


# Adiciona VLAN Unicast
#def onuaddvlanunicast(onu, lan, vlan):
#    try:
#      cmd = "CFG-LANPORTVLAN::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s,ONUPORT=NA-NA-NA-%s:CTAG::CVLAN=%s,CCOS=1;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], lan, vlan)  
#      ret = cmdexec(cmd, "boolean")
#      return ret
#    except Exception as e:
#      print(str(e))  


# Adiciona VLAN Multicast
def onuaddvlanmulticast(onu):
#def onuaddvlanmulticast(olt, slot, pon, onuid, port, vlan):
  child = pexpect.spawn('telnet {}'.format(onu["olt_ip"]), encoding='utf-8')

  #Habilita o debug (retorno na tela)
  #child.logfile = sys.stdout
  child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_USER'))
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline("enable")
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))

  # verifica se e rp1000
  child.sendline('dir')
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
  ret = ""
  ret += child.before
  rp1000 = ret.find('<DIR>') != -1
  ######################
  if(rp1000):
    child.sendline('cd onu/lan')
    child.expect('lan#', int(os.getenv('PEXPECT_TIMEOUT')))
    expectstr = 'lan#'
  else:
    child.sendline('cd epononu')
    child.expect('epononu# ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline('cd qinq')
    child.expect('qinq#', int(os.getenv('PEXPECT_TIMEOUT')))
    expectstr = 'qinq#'

  #adiciona um servico na porta
  #IMPORTANTE: todas as vlans serao excluidas antes de adicionar a nova vlan
  child.sendline(
          'set epon slot {} pon {} onu {} port {} service number 1'.format(onu["placa"], onu["porta"], onu['onuid'], onu['vlanport']))
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

  #configura o servico para o tipo multicast
  child.sendline(
          'set epon slot {} pon {} onu {} port {} service {} type multicast'.format(onu["placa"], onu["porta"], onu['onuid'], onu['vlanport'], 1))
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

  #adiciona a vlan
  child.sendline(
          'set epon slot {} pon {} onu {} port {} service {} vlan_mode tag 0 33024 {}'.format(onu["placa"], onu["porta"], onu['onuid'], onu['vlanport'], 1, onu['vlan']))
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

  return True  


# Adiciona VLAN Unicast
def onuaddvlanunicast(onu):
  child = pexpect.spawn('telnet {}'.format(onu['olt_ip']), encoding='utf-8')

  #Habilita o debug (retorno na tela)
  #child.logfile = sys.stdout
  child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_USER'))
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline("enable")
  child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
  child.sendline(os.getenv('CLI_PASS'))
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))

  # verifica se e rp1000
  child.sendline('dir')
  child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
  ret = ""
  ret += child.before
  rp1000 = ret.find('<DIR>') != -1
  ######################
  if(rp1000):
    child.sendline('cd onu/lan')
    child.expect('lan#', int(os.getenv('PEXPECT_TIMEOUT')))
    expectstr = 'lan#'
  else:
    child.sendline('cd epononu')
    child.expect('epononu# ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline('cd qinq')
    child.expect('qinq#', int(os.getenv('PEXPECT_TIMEOUT')))
    expectstr = 'qinq#'

  #adiciona um servico na porta
  #IMPORTANTE: todas as vlans serao excluidas antes de adicionar a nova vlan
  child.sendline(
          'set epon slot {} pon {} onu {} port {} service number 1'.format(onu["placa"], onu["porta"], onu['onuid'], onu['vlanport']))
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

  #configura o servico para o tipo unicast
  child.sendline(
          'set epon slot {} pon {} onu {} port {} service {} type unicast'.format(onu["placa"], onu["porta"], onu['onuid'], onu['vlanport'], 1))
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

  #adiciona a vlan
  child.sendline(
          'set epon slot {} pon {} onu {} port {} service {} vlan_mode tag 0 33024 {}'.format(onu["placa"], onu["porta"], onu['onuid'], onu['vlanport'], 1, onu['vlan']))
  child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

  return True 

'''
#discovery
olts = monitors('FBT')

proccess = []

for olt in olts:
    print(olt['ip'])
    p = Process(target=discovery, args=(olt['ip'],))
    p.start()
    proccess.append(p)
    #print(discovery(olt['ip']))
'''

'''    
#checkposition('***********', 12, 16)

#autorizacao
#set whitelist phy_addr address ZTEGcc4d3acc password null action add slot 12 link 16 onu 7 type 5506-sfu

#onuid = checkposition('***********', 12, 16)
#print(onuauthorize('***********', 12, 16, 'ZTEGcc4d3acc', onuid, '5506-sfu'))

#onuaddvlanmulticast('***********', 12, 16, 7,2,1,2)
#onuaddvlanunicast('***********', 12, 16, 7,2,11)
'''