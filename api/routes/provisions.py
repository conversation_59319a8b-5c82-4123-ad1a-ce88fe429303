from flask import request, jsonify, render_template, Blueprint
import json
from db import *
from dotenv import load_dotenv
import os
from models import token_required

provision_route = Blueprint('provision_route', __name__, template_folder='templates')

@provision_route.route('/list/<serial>', methods=['GET'])
@token_required
def list_provisions_serial(serial):
    try:
        if(request.form):
            params = request.form
        else:
            if(request.data):
                params = json.loads(request.data.decode())
            else:
                params = []
        return jsonify(provision_list(serial))        
    except Exception as e:
        return jsonify({'status' : 'ERROR', 'msg': "Error on list provisions: {}".format(str(e))})

@provision_route.route('/list/status/<status>', methods=['GET'])
@token_required
def list_provisions_status(status):
    try:
        if(request.form):
            params = request.form
        else:
            if(request.data):
                params = json.loads(request.data.decode())
            else:
                params = []
        return jsonify(provision_list(None, status))        
    except Exception as e:
        return jsonify({'status' : 'ERROR', 'msg': "Error on list provisions: {}".format(str(e))})

@provision_route.route('/list', methods=['GET'])
@token_required
def list_provisions():
    try:
        if(request.form):
            params = request.form
        else:
            if(request.data):
                params = json.loads(request.data.decode())
            else:
                params = []
        return jsonify(provision_list())        
    except Exception as e:
        return jsonify({'status' : 'ERROR', 'msg': "Error on list provisions: {}".format(str(e))})

@provision_route.route('/<id>/jobs', methods=['GET'])
@token_required
def list_jobs(id):
    try:
        return jsonify(job_list(id))        
    except Exception as e:
        return jsonify({'status' : 'ERROR', 'msg': "Error on list jobs: {}".format(str(e))})
