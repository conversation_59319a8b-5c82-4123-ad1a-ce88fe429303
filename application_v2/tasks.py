import os
import uuid
import urllib3
import redis
import json
from ztemanager import ZTEManager
from cli import removewhitelist
from ixc_ws import update_dados_tecnicos
from dotenv import load_dotenv
from database import get_provision_tasks, update_job, getdevice, updatedevice, removedevice, update_provision, getvlans, get_dados_tecnicos, findauthorizedonu
from collections import deque
from datetime import datetime
import time
from rq import Queue, Retry
from rq.job import Job
import heapq
import re
import operator
import ast

load_dotenv()

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


redis_host = os.getenv('REDIS_HOST', 'localhost')
redis_port = os.getenv('REDIS_PORT', 6379)
redis_pass = os.getenv('REDIS_PASS', 'admin')

conn = redis.Redis(
    host=redis_host,
    port=redis_port,
    password=redis_pass)


def job_to_json(job):
    job_result = Job.fetch(job.get_id(), connection=conn)
    job_json = {
        'id' : job_result.get_id(),
        'enqueued_at' : job_result.enqueued_at,
        'status' : job_result.get_status(refresh=True),
        'started_at' : job_result.started_at,
        'ended_at' : job_result.ended_at,
        'exc_info' : job_result.exc_info,
        'worker_name' : job_result.worker_name,
        'last_heartbeat' : job_result.last_heartbeat,
        'origin': job_result.origin,
        'func_name' : job_result.func_name,
        'kwargs': json.dumps(job_result.kwargs, indent=4, sort_keys=True, default=str)
    }
    return job_json

#Task 1: PROVISION
def task_provision(*args, **kwargs):

    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs

        device = {
            "olt": item["olt"],
            "olt_ip" : item["olt_ip"],
            "olt_modelo": "ZTE",
            "serial" : item["serial"],
            "type": item["modelo"],
            "name" : item["username"],
            "vlan_tr069" : item['vlan_dhcp'],
            "vlan_pppoe" : item['vlan_pppoe'],
            "vlan_iptv_unicast" : item['vlan_iptv'], 
            "vlan_iptv_multicast" : 2,
            "chassi": 1,
            "slot": item["placa"],
            "pon": item["porta"]
        }

        if 'mac' in item:
            device["mac"] = item["mac"]

        if 'patrimonio' in item:  
            device["patrimonio"] = item["patrimonio"]

        if 'modelo' in item:
            device["onu_modelo"] = item["modelo"]

        with ZTEManager(
            host=item['olt_ip']
        ) as manager:
            # Configuração inicial
            manager.disable_paging()
            response = manager.provision(chassi=device['chassi'], 
                                     slot=device['slot'], 
                                     pon=device['pon'], 
                                     serial=device['serial'], 
                                     type=device['type'], 
                                     name=device['name'], 
                                     vlan_tr069=device['vlan_tr069'], 
                                     vlan_pppoe=device['vlan_pppoe'], 
                                     vlan_iptv_unicast=device['vlan_iptv_unicast'], 
                                     vlan_iptv_multicast=device['vlan_iptv_multicast'], 
                                     bridge=False
            )
            manager.disconnect()
            del manager

        if response == True:

            update_job({
                'id': args[0], 
                'status' :'finished', 
                'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })      
            return True
        else:
            update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(response),
                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })       
            raise Exception("Erro em task_authorization: {}".format(str(response)))   
        
    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })       
        raise Exception("Erro ao autorizar a onu em {}".format(item["olt_ip"]))

#Task 2: INSERT DATABASE 
def task_insertdatabase(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs

        device = {
            "olt": item["olt"],
            "olt_ip" : item["olt_ip"],
            "olt_modelo": "ZTE",
            "serial" : item["serial"],
            "type": item["modelo"],
            "name" : item["username"],
            "vlan_tr069" : item['vlan_dhcp'],
            "vlan_pppoe" : item['vlan_pppoe'],
            "vlan_iptv_unicast" : item['vlan_iptv'], 
            "vlan_iptv_multicast" : 2,
            "chassi": 1,
            "slot": item["placa"],
            "pon": item["porta"]
        }

        if 'mac' in item:
            device["mac"] = item["mac"]

        if 'patrimonio' in item:  
            device["patrimonio"] = item["patrimonio"]

        if 'modelo' in item:
            device["onu_modelo"] = item["modelo"]
        
        with ZTEManager(
            host=item['olt_ip']
        ) as manager:
            manager.disable_paging()
            response = manager.list_auth(device['chassi'], device['slot'], device['pon'])
            manager.disconnect()
            del manager

        if(not 'onus' in response):
            update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': "Erro ao adicionar a onu no banco de dados. Impossivel verificar a lista de autorizadas",
                'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })       
            raise Exception("Erro ao adicionar a onu no banco de dados. Impossivel verificar a lista de autorizadas")

        encontrada = False
        for onu in response['onus']:
            if(onu['sn'] == item['serial']):
                encontrada = onu['id']

        if(not encontrada):
            update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': "Erro ao adicionar a onu no banco de dados. ONU não encontrada na lista de autorizadas na OLT.",
                'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })       
            raise Exception("Erro ao adicionar a onu no banco de dados. ONU não encontrada na lista de autorizadas na OLT.")

        device['onuid'] = encontrada
        updatedevice(device)
        update_job({
            'id': args[0], 
            'status' :'finished', 
            'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })      
  
    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })       
        raise Exception("Erro ao adicionar a onu no banco de dados")

#Task 3: UPDATE DADOS TECNICOS
def task_update_dados_tecnicos(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs
        payload = get_dados_tecnicos(item['serial'], item['olt_ip'], '0-0-{}-{}'.format(item["placa"], item["porta"]))
        if(payload):
            if('id' in payload and payload['id'] != None):
                if("mac" in payload):
                    mac = payload["mac"]
                    if re.match("[0-9a-f]{2}([-:]?)[0-9a-f]{2}(\\1[0-9a-f]{2}){4}$", mac.lower()):
                        response = update_dados_tecnicos(payload)
                        response = json.loads(response) 
                        if('type' in response):
                            if(response['type'] == 'error'):
                                update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "Erro API IXC: {}".format(response["message"].encode('utf-8')),
                                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                                })
                                raise Exception("Erro API IXC: {}".format(response["message"].encode('utf-8'))) 
                            else:
                                update_job({
                                    'id': args[0], 
                                    'status' :'finished', 
                                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                                })         
                                return True

                        else:
                            update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "Erro API IXC",
                                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                            })       
                            raise Exception("Erro API IXC")     
                    else:
                        update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "MAC Invalido",
                                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                        })       
                        raise Exception("MAC invalido") 
                else:
                    update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "MAC Nao Informado",
                                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                    })        
                    raise Exception("MAC nao informado") 
            else:
                update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "Dados Tecnicos Nao Localizados",
                                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                })
                raise Exception("Dados tecnicos nao localizados")         
        else:
            update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "Dados Tecnicos Nao Localizados",
                                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })        
            raise Exception("Dados tecnicos nao localizados")                 
    except Exception as e:
        update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': str(e),
                                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })        
        raise

#Task 4: DEAUTHORIZE FH
def task_deauthorizefh(*args, **kwargs):
    
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })

        # localiza a onu na tabela do unm
        onus = findauthorizedonu(serial=item["serial"])

        # remove da whitelist
        for onu in onus:
            removewhitelist(onu['olt_ip'], onu["serial"])
        
        update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro em deauthorizefh: {}".format(str(e))) 

#Task 5: DEAUTHORIZE ZTE
def task_deauthorizezte(*args, **kwargs):
    
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
       
        # verifica se a onu existe na tabela devices em outra olt, placa, porta diferentes
        filters = [
           ("serial", "=", item['serial']),
           ("OR", [
            [("slot", "!=", item['placa'])],
            [("pon", "!=", item['porta'])],
            [("olt_ip", "!=", item['olt_ip'])],
           ])
        ]
        previous_device = getdevice(filters)
        # desautoriza as onu encontrada 
        if len(previous_device) > 0:
            previous = previous_device[0] # Pega o primeiro resultado, se houver mais de um, pode ser necessário ajustar a lógica
            with ZTEManager(
                host=previous['olt_ip']
            ) as manager:
                manager.disable_paging()
                response = manager.deauth(None, previous['chassi'], previous['slot'], previous['pon'], previous['onuid'])
                manager.disconnect()
                del manager
            if(response==True):
                #remove da tabela devices
                removedevice(olt=previous['olt_ip'], chassi=previous['chassi'], slot=previous['slot'], pon=previous['pon'], onuid=previous['onuid'])

        update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro ao deauthorizezte: {}".format(str(e)))

# função que avalia a expressão da precondição
# Precondiçoes implementadas:
# - getdevice: verifica se a ONU já existe em outra OLT ZTE, placa ou porta 
# - findauthorizedonu: verifica se a ONU está autorizada em outra OLT Fiberhome, placa ou porta 
def evaluate_precondition(expr, context={}, functions={}):
    """
    Avalia uma precondição segura, como:
        - findauthorizedonu(serial) != []
        - len(findauthorizedonu(serial)) > 0
        - min_pon(olt_ip) >= 2

    Args:
        expr: str - expressão como string
        context: dict - valores para variáveis
        functions: dict - mapa de funções disponíveis
    
    Returns:
        bool
    """

    # Operadores suportados
    OPS = {
        ">": operator.gt,
        "<": operator.lt,
        ">=": operator.ge,
        "<=": operator.le,
        "==": operator.eq,
        "=": operator.eq,
        "!=": operator.ne,
    }

    expr = expr.strip()

    # Regex que suporta chamadas encadeadas como len(func(arg))
    pattern = r"^(len\((\w+)\((.*?)\)\)|(\w+)\((.*?)\))\s*([=!<>]=?|==)\s*(.+)$"
    match = re.match(pattern, expr)

    if not match:
        raise ValueError(f"Precondição inválida: '{expr}'")

    (
        full_func_call,
        inner_func_name_1,
        inner_args_1,
        outer_func_name,
        outer_args,
        operator_str,
        compare_literal
    ) = match.groups()

    # Determina a função principal e seus argumentos
    if full_func_call.startswith("len("):
        func_name = inner_func_name_1
        raw_args = inner_args_1
        use_len = True
    else:
        func_name = outer_func_name
        raw_args = outer_args
        use_len = False

    if raw_args:
        args = [context.get(arg.strip(), arg.strip().strip("'\"")) for arg in raw_args.split(",") if arg.strip()]
    else:
        args = []

    func = functions.get(func_name)
    if not func:
        raise ValueError(f"Função '{func_name}' não encontrada.")

    result = func(*args)

    if use_len:
        result = len(result)

    try:
        compare_value = ast.literal_eval(compare_literal)
    except Exception:
        compare_value = context.get(compare_literal.strip("'\""), compare_literal.strip("'\""))

    op = OPS.get(operator_str)
    if not op:
        raise ValueError(f"Operador inválido: {operator_str}")

    return op(result, compare_value)

def task_is_valid(task, item):
    precondition = task.get('precondition')
    
    if not precondition:
        return True
    
    if 'getdevice' in precondition:
        filters = [
            ("serial", "=", item['serial']),
            ("OR", [
                [("slot", "!=", item['placa'])],
                [("pon", "!=", item['porta'])],
                [("olt_ip", "!=", item['olt_ip'])],
            ])
        ]
        context = {"filters": filters}
        functions = {"getdevice": getdevice}
        return evaluate_precondition(precondition, context, functions)

    if 'findauthorizedonu' in precondition:
        context = {"serial": item['serial']}
        functions = {"findauthorizedonu": findauthorizedonu}
        return evaluate_precondition(precondition, context, functions)

    return True  # mantém tasks sem precondição


def create_provision(item):
    #Verifica as tarefas necessarias atraves do task_group da OLT
    tasks = get_provision_tasks(olt=item["olt_ip"])

    if not tasks:
        raise Exception(f"Nenhuma tarefa encontrada para a OLT {item['olt_ip']}")

    provision= {
        'id' : str(uuid.uuid4()),
        'serial': item['serial'],
        'username': item['username'],
        'olt_ip': item["olt_ip"],
        'olt': item["olt"],
        'olt_model': item["olt_model"],
        'slot': item['placa'],
        'pon': item['porta'],
        'model': item['modelo'],
        'source': item['source'],
        'status': 'queued' #queued, started, deferred, finished, stopped, scheduled, canceled, failed
    }

    provision_id = update_provision(provision)
    item['provision_id'] = provision_id
    item['vlan_dhcp'] = os.getenv('TR069_DHCP_VLANID') or 10
    vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])

    if not vlans:
        raise Exception("Erro ao obter vlans")

    item["vlan_pppoe"] = int(vlans['vlan_pppoe'])
    item["vlan_iptv"] = int(vlans['vlan_iptv'])

    provision['started_at'] = datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
    update_provision(provision)

    # Filtra as tarefas que possuem precondições válidas
    tasks = [task for task in tasks if task_is_valid(task, item)]

    # Criar um mapeamento de task name para task id para resolver dependências
    task_name_to_id = {task['task']: task['id'] for task in tasks}

    # Estruturas para ordenação topológica
    tasks_by_id = {task['id']: task for task in tasks}
    graph = {}
    in_degree = {task['id']: 0 for task in tasks}

    # Construir grafo de dependências
    for task in tasks:
        if task['depends_on'] is not None:
            # task['depends_on'] contém o nome da tarefa dependente, precisamos encontrar o ID
            parent_task_id = task_name_to_id.get(task['depends_on'])
            if parent_task_id and parent_task_id in tasks_by_id:
                graph.setdefault(parent_task_id, []).append(task['id'])
                in_degree[task['id']] += 1

    # Ordenação topológica com prioridade pela ordem
    heap = []
    for task_id, degree in in_degree.items():
        if degree == 0:
            heapq.heappush(heap, (tasks_by_id[task_id]['order'], task_id))

    sorted_tasks = []
    while heap:
        _, task_id = heapq.heappop(heap)
        sorted_tasks.append(task_id)

        for neighbor in graph.get(task_id, []):
            in_degree[neighbor] -= 1
            if in_degree[neighbor] == 0:
                heapq.heappush(heap, (tasks_by_id[neighbor]['order'], neighbor))

    jobs = []
    job_map = {}  # Mapeia task_id -> job object
    q = Queue(f"olt-{item['olt_ip']}", connection=conn)  # Criar fila única

    for task_id in sorted_tasks:
        task = tasks_by_id[task_id]

        # Obter o job object da tarefa pai (se existir)
        depends_on_job = None
        if task['depends_on']:
            parent_task_id = task_name_to_id.get(task['depends_on'])
            if parent_task_id:
                depends_on_job = job_map.get(parent_task_id)

        # Criar o job com dependência se necessário
        job = create_job(
            queue=q,
            task_id=str(task['id']),
            task_name=task['task'],
            item=item,
            retry=task['retries'],
            interval=task['interval'],
            depends_on=depends_on_job,  # Job object da tarefa pai
            provision_id=provision_id
        )

        job_id = job.get_id()
        job_map[task['id']] = job  # Armazenar o job object para dependências futuras

        # Construir dados do job para registro
        job_data = {
            'id': job_id,
            'status': 'queued',
            'depends_on': depends_on_job.get_id() if depends_on_job else None,
            'description': task['task'],
            'provision_id': provision_id
        }
        update_job(job_data)
        jobs.append(job_id)

    return jobs

def create_job(queue, task_id, task_name, item, retry, interval, depends_on, provision_id):
    task_handlers = {
        "1": task_provision,
        "2": task_insertdatabase,
        "3": task_update_dados_tecnicos,
        "4": task_deauthorizefh,
        "5": task_deauthorizezte
    }

    handler = task_handlers.get(task_id)
    if not handler:
        raise ValueError(f"Handler não encontrado para task_id {task_id}")

    # Criar argumentos para o job
    job_id = str(uuid.uuid4())
    job_args = [job_id]  # ID único para o job
    job_kwargs = item

    # Configurar parâmetros do job
    job_params = {
        'job_id': job_id,
        'retry': Retry(max=retry, interval=interval),
        'failure_ttl': 300
    }

    # Adicionar dependência se existir
    if depends_on:
        try:
            # Buscar o job pai usando o ID
            parent_job = Job.fetch(depends_on, connection=conn)
            if parent_job:
                job_params['depends_on'] = parent_job
            else:
                raise ValueError(f"Job pai não encontrado: {depends_on}")
        except Exception as e:
            # Se não conseguir encontrar o job pai, não adicionar dependência
            print(f"Erro ao buscar job pai {depends_on}: {e}")
            # Não adicionar depends_on se não conseguir encontrar o job pai

    # Enfileirar o job
    job = queue.enqueue(
        handler,
        *job_args,
        **job_kwargs,
        **job_params
    )

    return job



'''
def create_provision(item):
    
    #Verifica as tarefas necessarias atraves do task_group da OLT
    tasks = get_provision_tasks(olt=item["olt_ip"])

    if not tasks:
        raise Exception(f"Nenhuma tarefa encontrada para a OLT {item['olt_ip']}")

    provision= {
        'id' : str(uuid.uuid4()), 
        'serial': item['serial'],
        'username': item['username'],
        'olt_ip': item["olt_ip"],
        'olt': item["olt"],
        'olt_model': item["olt_model"],
        'slot': item['placa'],
        'pon': item['porta'],
        'model': item['modelo'],
        'source': item['source'],
        'status': 'queued' #queued, started, deferred, finished, stopped, scheduled, canceled, failed
    }

    provision_id = update_provision(provision)
    item['provision_id'] = provision_id
    item['vlan_dhcp'] = os.getenv('TR069_DHCP_VLANID') or 10
    vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])

    if not vlans:
        raise Exception("Erro ao obter vlans")
    
    item["vlan_pppoe"] = int(vlans['vlan_pppoe'])
    item["vlan_iptv"] = int(vlans['vlan_iptv'])
    
    provision['started_at'] = datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
    update_provision(provision)

    # Filtra as tarefas que possuem precondições válidas
    tasks = [task for task in tasks if task_is_valid(task, item)]

    # Estruturas para ordenação topológica
    tasks_by_id = {task['id']: task for task in tasks}
    graph = {}
    in_degree = {task['id']: 0 for task in tasks}
    
    # Construir grafo de dependências
    for task in tasks:
        if task['depends_on'] is not None:
            parent_id = task['depends_on']
            if parent_id in tasks_by_id:
                graph.setdefault(parent_id, []).append(task['id'])
                in_degree[task['id']] += 1

    # Ordenação topológica com prioridade pela ordem
    heap = []
    for task_id, degree in in_degree.items():
        if degree == 0:
            heapq.heappush(heap, (tasks_by_id[task_id]['order'], task_id))
    
    sorted_tasks = []
    while heap:
        _, task_id = heapq.heappop(heap)
        sorted_tasks.append(task_id)
        
        for neighbor in graph.get(task_id, []):
            in_degree[neighbor] -= 1
            if in_degree[neighbor] == 0:
                heapq.heappush(heap, (tasks_by_id[neighbor]['order'], neighbor))

    jobs = []

    # Criar jobs na ordem topológica
    job_map = {}  # Mapeia task_id para job criado
    for task_id in sorted_tasks:
        task = tasks_by_id[task_id]
        depends_on_job = job_map.get(task['depends_on'])
        depends_on_id = depends_on_job.id if depends_on_job else None
        
        job = create_job(
            queue_name=f"olt-{item['olt_ip']}",
            task_id=str(task['id']),
            task_name=task['task'],
            item=item,
            retry=task['retries'],
            interval=task['interval'],
            depends_on=depends_on_id,
            provision_id=provision_id
        )
        job_map[task_id] = job

        job_json = job_to_json(job)
        job_json['description'] = task['task']
        job_json['provision_id'] = provision_id
        update_job(job_json)
        jobs.append(job.get_id())


    return jobs

def create_job(queue_name, task_id, task_name, item, retry, interval, depends_on, provision_id):

    # Dicionario de mapeamento (nome tarefa: metodo)
    task_handlers = {
        "1": task_provision,
        "2": task_insertdatabase,
        "3": task_update_dados_tecnicos,
        "4": task_deauthorizefh,
        "5": task_deauthorizezte
    }

    q = Queue(queue_name, connection=conn)
    
    job_id = str(uuid.uuid4())
    handler = task_handlers.get(task_id)
    if handler:
        if(depends_on):
            job = q.enqueue(handler, args=[job_id], kwargs=item, retry=Retry(max=retry, interval=interval), job_id=job_id, depends_on=depends_on, failure_ttl=300) 
        else:
            job = q.enqueue(handler, args=[job_id], kwargs=item, retry=Retry(max=retry, interval=interval), job_id=job_id, failure_ttl=300) 

        job_json = job_to_json(job)
        job_json['description'] = task_name
        job_json['provision_id'] = provision_id
        update_job(job_json)

        return job
'''