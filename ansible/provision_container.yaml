---
- hosts: srv-provision
  remote_user: root
  vars:
      container: provision
      memory: 4g
      base: 
        image: docker-registry.telemidia.net.br/pnetbase7-4
        tag: latest
      eth0:
        device: eth0
        ipaddr: "************"
        gateway: "***********"
        prefix: 24
        macaddr: "02:46:34:ff:14:3e"
      eth1:
        device: eth1
        ipaddr: "************"
        gateway: "***********"
        prefix: 24
        macaddr: "02:35:ac:28:31:86"
      
  tasks:
  - name: <PERSON><PERSON>r as pastas conf e data em /var/docker/{{container}}
    shell: |
      mkdir -p /var/docker/{{container}}/conf
      mkdir -p /var/docker/{{container}}/data
      mkdir -p /var/www
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  #- name: Instalar pacotes necessarios
  #  ansible.builtin.yum:
  #    name:
  #      - git
  #      - vim
  #    state: present 
  #  become: yes
  #  become_user: root  
  - name: Gerar o arquivo ifcfg-eth0 em /var/docker/{{container}}/conf
    vars:
      base_path: $BASE_PATH
    shell: |
      cat > /var/docker/{{container}}/conf/ifcfg-eth0 << EOF 
      DEVICE={{eth0.device}}
      IPADDR={{eth0.ipaddr}}
      GATEWAY={{eth0.gateway}}
      PREFIX={{eth0.prefix}}
      MACADDR={{eth0.macaddr}}
      ONBOOT=yes
      EOF
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Gerar o arquivo ifcfg-eth1 em /var/docker/{{container}}/conf
    shell: |
      cat > /var/docker/{{container}}/conf/ifcfg-eth1 << EOF 
      DEVICE={{eth1.device}}
      IPADDR={{eth1.ipaddr}}
      GATEWAY={{eth1.gateway}}
      PREFIX={{eth1.prefix}}
      MACADDR={{eth1.macaddr}}
      EOF
    args:
      executable: /bin/bash
    become: yes
    become_user: root  
  - name: Gerar o dockerfile em /etc/sysconfig/pnet/{{container}}
    shell: |
      cat > /etc/sysconfig/pnet/{{container}} << EOF 
      TAG={{base.tag}}
      CONTAINER_NAME={{container}}
      IMAGE_NAME={{base.image}}

      HOSTNAME=\$CONTAINER_NAME.pocos-net.com.br

      BASE_PATH=/var/docker/\$CONTAINER_NAME
      BIND_MOUNTS=()
      BIND_MOUNTS+=( \$BASE_PATH/conf/ifcfg-eth0:/etc/sysconfig/network-scripts/ifcfg-eth0 )
      BIND_MOUNTS+=( \$BASE_PATH/conf/ifcfg-eth1:/etc/sysconfig/network-scripts/ifcfg-eth1 )
      BIND_MOUNTS+=( \$BASE_PATH/data/log:/var/log )
      BIND_MOUNTS+=( \$BASE_PATH/data/www:/var/www )
      
      COMMAND=/usr/sbin/init

      MEMORY={{memory}}

      NETWORKS=( docker_ger docker_pub)
      HOSTS=( alternatemaster:************* myhosts.pocos-net.com.br:************* )
      EOF
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Instalar a biblioteca docker do python
    vars:
      ansible_python_interpreter: /usr/bin/python3
    shell: |
      pip3 install docker
    args:
      executable: /bin/bash
    become: yes
    become_user: root  
  - name: Logar no docker registry
    vars:
      ansible_python_interpreter: /usr/bin/python3
    docker_login:
      registry: "{{ registry_url }}"
      username: "{{ registry_username }}"
      password: "{{ registry_password }}"
      reauthorize: yes  
  - name: Fazer o pull da imagem base
    shell: |
      docker pull {{base.image}}:{{base.tag}}
    args:
      executable: /bin/bash
    become: yes
    become_user: root 
  - name: Criar o container {{container}} e iniciar
    shell: |
      pnet-container create {{container}}
      pnet-container start {{container}}
    args:
      executable: /bin/bash
    become: yes
    become_user: root   
     
