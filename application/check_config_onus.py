from ztemanager_ds import ZTEManager


if __name__ == "__main__":
    olt = {
        "name": "OLT Cps ZTE_OsvaldoCruz_01",
        "ip": "**********",
        "slot": 1,
        "pon": 1,
        "chassi": 1
    }

    with ZTEManager(
        host=olt["ip"],
    ) as manager:
        # Configuração inicial
        manager.disable_paging()
        
        response = manager.list_auth(olt['chassi'], olt['slot'], olt['pon'])
        onus = response['onus']
        total_items = len(onus)

        items = manager.show_config_onu(chassi=olt['chassi'], slot=olt['slot'], pon=olt['pon'], start_id=1, end_id=total_items)

        for item in items:
            if not 'TELEMIDIA-NAVEGACAO' in item['raw_config']:
                print(f"ONU {item['onu_id']} sem configuracao correta")


        
