import requests
import os
from rq import Queue, Retry
from rq.job import Job
from worker_provision_queue import conn
import json
import time
from datetime import datetime
from dotenv import load_dotenv
from database import *
from tasks_zte import *
from tasks_ixc import *
from tasks_genieacs import *
from rq.serializers import JSONSerializer
import uuid
from pytz import timezone
from common import plan_rule

load_dotenv()

q = Queue('provision_queue', connection=conn)

def job_to_json(job):
    job_result = Job.fetch(job.get_id(), connection=conn)
    job_json = {
        'id' : job_result.get_id(),
        'enqueued_at' : job_result.enqueued_at,
        'status' : job_result.get_status(refresh=True),
        'started_at' : job_result.started_at,
        'ended_at' : job_result.ended_at,
        'exc_info' : job_result.exc_info,
        'worker_name' : job_result.worker_name,
        'last_heartbeat' : job_result.last_heartbeat,
        'origin': job_result.origin,
        'func_name' : job_result.func_name,
        'kwargs': json.dumps(job_result.kwargs, indent=4, sort_keys=True, default=str)
    }
    return job_json

def task_provision(item):

    current_provision= {
        'id' : str(uuid.uuid4()), 
        'serial': item['serial'],
        'username': item['username'],
        'olt_ip': item["olt_ip"],
        'olt': item["olt"],
        'olt_model': item["olt_model"],
        'slot': item['placa'],
        'pon': item['porta'],
        'model': item['modelo'],
        'source': item['source'],
        'status': 'queued' #queued, started, deferred, finished, stopped, scheduled, canceled, failed
    }

    provision_id = update_provision(current_provision)
    item['provision_id'] = provision_id
    item['vlan_dhcp'] = os.getenv('TR069_DHCP_VLANID')
    vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])
    
    item["vlan_pppoe"] = int(vlans['vlan_pppoe'])
    item["vlan_iptv"] = int(vlans['vlan_iptv'])
    item["fiberhome"] = 'AN5506' in item["modelo"]

    jobs = []

    job_id = str(uuid.uuid4())
    job_deauthorizefh = q.enqueue(task_deauthorizefh, args=([job_id]), kwargs=(item), retry=Retry(max=3, interval=15), job_id=job_id)
    job = job_to_json(job_deauthorizefh)
    job['description'] = 'DEAUTHORIZE FIBERHOME'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_deauthorizefh.get_id())

    job_id = str(uuid.uuid4())
    job_deauthorizezte = q.enqueue(task_deauthorizezte, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id)
    job = job_to_json(job_deauthorizezte)
    job['description'] = 'DEAUTHORIZE ZTE'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_deauthorizezte.get_id())

    if(not item["fiberhome"]):
        previous_device = getdevice(item["serial"])
        if(previous_device):
            if(previous_device['name'] == item['username']):
                if('mac' in item and 'vlan_pppoe' in item):
                    job_id = str(uuid.uuid4())
                    job_acschangevlanpppoe = q.enqueue(task_changevlanpppoe, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id)
                    job = job_to_json(job_acschangevlanpppoe)
                    job['description'] = 'ACS CHANGE VLAN PPPOE'
                    job['provision_id'] = provision_id
                    update_job(job)
                    jobs.append(job_acschangevlanpppoe.get_id())

    job_id = str(uuid.uuid4())
    job_authorization = q.enqueue(task_authorization, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id)
    job = job_to_json(job_authorization)
    job['description'] = 'AUTHORIZATION'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_authorization.get_id())

    job_id = str(uuid.uuid4())
    job_dadostecnicos = q.enqueue(task_updateixc, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id, depends_on=job_authorization)
    job = job_to_json(job_dadostecnicos)
    job['description'] = 'DADOSTECNICOS'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_dadostecnicos.get_id())
    

    job_id = str(uuid.uuid4())
    job_config = q.enqueue(task_config, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id, depends_on=job_authorization)
    job = job_to_json(job_config)
    job['description'] = 'CONFIG ONU'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_config.get_id())

    if(not item["fiberhome"]):
        job_id = str(uuid.uuid4())
        job_registered = q.enqueue(task_registered, args=([job_id]), kwargs=(item), retry=Retry(max=10, interval=3), job_id=job_id, depends_on= job_config)
        job = job_to_json(job_registered)
        job['description'] = 'CHECK ONU ACS'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_registered.get_id()) 

        if(previous_device):
            if(previous_device['name'] != item['username']):
                job_id = str(uuid.uuid4())
                job_reprovision = q.enqueue(task_reprovision, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id, depends_on=job_registered)
                job = job_to_json(job_reprovision)
                job['description'] = 'ACS REPROVISION'
                job['provision_id'] = provision_id
                update_job(job)
                jobs.append(job_reprovision.get_id())
        else:
            job_id = str(uuid.uuid4())
            job_reprovision = q.enqueue(task_reprovision, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id, depends_on=job_registered)
            job = job_to_json(job_reprovision)
            job['description'] = 'ACS REPROVISION'
            job['provision_id'] = provision_id
            update_job(job)
            jobs.append(job_reprovision.get_id())            


    current_provision['started_at'] = datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
    update_provision(current_provision)

    return jobs

#def plan_rule(model, plan, username):

    #try:
'''
        Regras ZTE				
        ==========
        Cliente	    Plano	    ONU Nova	ONU Antiga	Acao
        Novo*	    50	        F660	    null	    Provisionar
        Antigo**	50	        F660	    F660	    Provisionar
        Todos	    >50 & <100	F660	    Todos	    Provisionar
        Todos	    >=100	    F670	    Todos	    Provisionar
        
        * uma alocacao
        ** mais de uma alocacao

        valid = None
        f660 = '660' in model
        f670 = '670' in model
        f6600 = '6600' in model

        if(not f660 and not f670 and not f6600):
            return None

        if(int(plan) > 50 and int(plan) < 100):
            if(f670 or f6600):
                valid = 'F660'
            if(f660):
                return None    

        if int(plan) >= 100:
            if(f660 and (not f6600)):
                valid = 'F670'
            if(f670 or f6600):
                return None    

        if valid == None:
            onus = get_onus_alocadas(username)
            #Novo
            if(len(onus) <= 1):
                if(f670):
                    valid = 'F660'
            #Antigo
            else:
                anterior = onus[0]['descricao']
                if(f670):
                    if '660' in anterior:
                        valid = 'F660'
                if(f660 and (not f6600)):
                    if '670' in anterior:
                        valid = 'F670'
                    elif not '660' in anterior:    
                        valid = 'FIBERHOME'

        return valid
    except:
        return False    
'''

#verifica se a onu possue todos os dados para iniciar o provisionamento
def task_checkonu(onu):
  try:
    missing_params = []

    # onu nao pode estar na lista para ignorar
    #if(checkignorelist(serial=onu['serial'])):
    #    missing_params.append('IGNORE')

    # obrigatorio patrimonio
    if(not ('patrimonio' in onu and onu['patrimonio'])):
        missing_params.append('PATRIMONIO')
    
    # Libera apenas se tiver a alocacao do comodato e nao esteja na tabela ignore com o parametro comodato=1
    if(not('id_comodato' in onu and onu['id_comodato']) and not(checkignorelist(serial=onu['serial'], comodato=1)) and not(checkignorelist(username=onu['username'], comodato=1))):
        missing_params.append('ALOCACAO COMODATO')
    
    # Libera apenas se tiver o modelo e nao esteja na tabela ignore com o parametro modelo=1
    if(not('modelo' in onu and onu['modelo'])and not(checkignorelist(serial=onu['serial'], modelo=1))):
        missing_params.append('MODELO')

    # obrigatorio username
    if(not('username' in onu and onu['username'])):
        missing_params.append('USERNAME')

    # obrigatorio mac
    if(not('mac' in onu and onu['mac'])):
        missing_params.append('MAC')

    # obrigatorio login ativo
    if(not('login_ativo' in onu and onu['login_ativo'] == 'S')):
        missing_params.append('LOGIN ATIVO')

    # obrigatorio pacote wifi para onus zte que nao estejam na lista para ignorar
    if('username' in onu and onu['username'] and 'ZTE' in onu['serial'].upper() and not(checkignorelist(serial=onu['serial'], pacote_wifi=1)) and not(checkignorelist(username=onu['username'], pacote_wifi=1)) and not(get_pacotewifi(onu['username']))):
        missing_params.append('PACOTE WIFI')

    

    # verifica se o modelo da onu e compativel com o plano.
    if('plano' in onu and 'modelo' in onu and 'username' in onu and onu['username'] and not(checkignorelist(serial=onu['serial'], plano=1)) and not(checkignorelist(username=onu['username'], plano=1))):
        pending = plan_rule(onu['modelo'], onu['plano'], onu['username'])
        if(pending):
            missing_params.append('ONU INCOMPATIVEL. NECESSARIO {}'.format(pending))

    return missing_params
    
  except Exception as e:
    raise
