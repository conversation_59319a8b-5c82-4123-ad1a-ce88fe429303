#!/usr/bin/python
# -*- coding: utf-8 -*-

from psycopg2 import connect, sql
import psycopg2.extras
import pymysql.cursors
from dotenv import load_dotenv
import os
from log import *
import json
import datetime
#from common import writeLog

load_dotenv()

def __nocdb():
  try:  
    con = psycopg2.connect(
        host=os.getenv('SERVICES1DB_HOST'),
        database=os.getenv('SERVICES1DB_DATABASE'),
        user=os.getenv('SERVICES1DB_USER'),
        password=os.getenv('SERVICES1DB_PASS')
    )
    return con
  except Exception as e:
    errorlog('Falha em nocdb: '+ str(e))

def __unmdb():
  try:  
    con = pymysql.connect(
        host=os.getenv('UNMDB_HOST'),
        user=os.getenv('UNMDB_USER'),
        password=os.getenv('UNMDB_PASS'),
        db=os.getenv('UNMDB_DATABASE'),
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor)
    return con
  except Exception as e:
    errorlog('Falha em unmdb: '+ str(e)) 

def __ixcdb():
    try:
        con = pymysql.connect(host=os.getenv('IXC_HOST'),
                              user=os.getenv('IXC_USER'),
                              password=os.getenv('IXC_PASS'),
                              db=os.getenv('IXC_DATABASE'),
                              charset='utf8mb4',
                              cursorclass=pymysql.cursors.DictCursor)
        return con
    except Exception as e:
        errorlog('Falha em ixcdb: ' + str(e))

def __assinantedb():
  try:  
    con = psycopg2.connect(
        host=os.getenv('CENTRAL_ASSINANTE_HOST'),
        database=os.getenv('CENTRAL_ASSINANTE_DATABASE'),
        user=os.getenv('CENTRAL_ASSINANTE_USER'),
        password=os.getenv('CENTRAL_ASSINANTE_PASS')
    )
    return con
  except Exception as e:
    errorlog('Falha em assinantedb: '+ str(e))

def getoperationmode(username):
  try:  
    con = __assinantedb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = 'SELECT operation_mode FROM public.devices WHERE username = %s'
    cur.execute(sql, (username, ))
    recset = cur.fetchone()
    cur.close()
    if(recset):
      return recset[0]
    else: 
      return 'router'
  except Exception as e:
    errorlog('Falha em getoperationmode: '+ str(e))
  finally:
    con.close() 

# converte campos datetime do python para formato iso
def default(o):
    if isinstance(o, (datetime.date, datetime.datetime)):
        return o.isoformat()    

def addonualerta(alerta):
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = 'SELECT * FROM authservice.alerts where lower(serial) = lower(%s) and tipo = %s'
    cur.execute(sql, (alerta["serial"], alerta["tipo"], ))
    recset = cur.fetchall()

    if(recset):
      return
    
    sql = 'INSERT INTO authservice.alerts (serial, tipo, msg) VALUES(%s, %s, %s)'
    cur.execute(sql, (alerta["serial"], alerta["tipo"], alerta["msg"], ))
    con.commit()
  except Exception as e:
    errorlog('Falha em addonualerta: '+ str(e))
  finally:
    con.close()    

def removeonualerta(serial):
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = 'delete from authservice.alerts where lower(serial) = lower(%s)'
    cur.execute(sql, (serial, ))
    con.commit()
  except Exception as e:
    errorlog('Falha em removeonualerta: '+ str(e))
  finally:
    con.close()   

def getalertas(serial):
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = 'select data, serial, msg, tipo from authservice.alerts where lower(serial) = lower(%s) order by data desc'
    cur.execute(sql, (serial, ))
    recset = cur.fetchall()
    cur.close()
    if(recset):
      return json.dumps(
        recset,
        default=default
      )
    else: 
      return None  
  except Exception as e:
    errorlog('Falha em getalertas: '+ str(e))
  finally:
    con.close()   
 
def unm_oltlist():
    try:
        con = __unmdb()
        with con.cursor() as cur:
            sql = "select n.cobjectname as nome, n.cipaddress as ip from integratecfgdb.t_nedevice n where n.cobjectname not like \'ADSL%%\' group by n.cobjectname"
            cur.execute(sql, ())
            recset = cur.fetchall()
        con.close()
        return recset
    except Exception as e:
        errorlog('Falha em unm_oltlist: ' + str(e))

# lista as placas da olt e o numero de portas
def slotlist(ip_olt):
    try:
        con = __unmdb()
        with con.cursor() as cur:
            sql = '''
              select n.cobjectname as nome, tb.cobjectno as slot,
              case when tb.cobjectname = 'GC8B' then 8 else 16 end as pons from t_boarddevice tb 
              left join integratecfgdb.t_nedevice n on (n.cobjectid = tb.cparentid)
              where n.cipaddress = %s and tb.cobjecttype in (2095, 1035)
            '''  
            cur.execute(sql, (ip_olt, ))
            recset = cur.fetchall()
        con.close()
        return recset
    except Exception as e:
        errorlog('Falha em slotlist: ' + str(e))

##################

# Grava log de provisionamento
def insertlog(log):
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = 'INSERT INTO authservice.authlogs (log, erro, task, patrimonio, serial, mac, username, olt, olt_ip, placa, porta, script, vlan_pppoe, vlan_iptv, vlan_dhcp, porta_onu, onu_id) VALUES(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)'
    cur.execute(sql, (log["msg"], log["erro"], log["task"], log["patrimonio"], log["serial"], log["mac"], log["username"], log["olt"], log["olt_ip"], log["placa"], log["porta"], log["script"], log["vlan_pppoe"], log["vlan_iptv"], log["vlan_dhcp"], log["porta_onu"], log["onu_id"]))
    con.commit()
    con.close()
  except Exception as e:
    errorlog('Falha em insertlog: '+ str(e)) 

# Atualiza log de provisionamento
def updatelog(log):
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = 'UPDATE authservice.authlogs SET verified=%s, retries=%s, last_retry=%s WHERE id = %s'
    cur.execute(sql, (log["verified"], log["retries"], log["last_retry"], log["id"]))
    con.commit()
    con.close()
  except Exception as e:
    errorlog('Falha em updatelog: '+ str(e))   

# Logs de provisionamento
def getauthlog(serial):
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
    sql = 'select * from (SELECT * FROM authservice.authlogs where upper(serial) = upper(%s) order by data desc limit 6) temp order by data'
    cur.execute(sql, (serial, ))
    recset = cur.fetchall()
    con.close()
    if(recset):
      return json.dumps(
        recset,
        default=default
      )
    else:
      return None  
  except Exception as e:
    errorlog('Falha em getauthlog: '+ str(e)) 

def activation_date(username):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
        sql = '''
  	     select cc.data_ativacao from cliente_contrato cc
         left join radusuarios ru on ru.id_contrato = cc.id
         where upper(ru.login) = upper(%s)
        '''
        cur.execute(sql,(username,))
        recset = cur.fetchall()
        if(recset):
          recset = recset[0]['data_ativacao']
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em activation_date: '+ str(e))


# Localiza ONU autorizada
def findauthorizedonu(serial=None, nome=None):
  try:  
    con = __unmdb()
    with con.cursor() as cur:
        sql = '''
                SELECT CONVERT_TZ(d.ccreatetime ,'+00:00','-03:00') as data_provisionamento, 
                upper(d.contmacquery) as serial, d.cauthno as onuid, d.cobjectname as nome, 
                d.cslotno as placa, d.cponno as porta, n.cobjectname as olt_nome, 
                n.cipaddress as olt_ip, d.cequipmentid as modelo FROM integratecfgdb.t_ontdevice d 
                inner join integratecfgdb.t_nedevice n on (n.cobjectid = d.cneid) 
                where {}
              '''  
        if(serial):
          sql = sql.format('upper(d.contmac) = upper(%s)')
          cur.execute(sql,(serial,))
          recset = cur.fetchone()
        elif(nome):
          sql = sql.format('upper(d.cobjectname) = upper(%s)')
          cur.execute(sql,(nome,))
          recset = cur.fetchall()  
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em finauthorizeddonu: '+ str(e))


def findauthorizedonus(username):
  try:  
    con = __unmdb()
    with con.cursor() as cur:
        sql = 'SELECT upper(d.contmacquery) as serial, d.cauthno as onuid, d.cobjectname as nome, d.cslotno as placa, d.cponno as porta, n.cobjectname as olt_nome, n.cipaddress as olt_ip, d.cequipmentid as modelo FROM integratecfgdb.t_ontdevice d inner join integratecfgdb.t_nedevice n on (n.cobjectid = d.cneid) where upper(d.cobjectname) = upper(%s)'
        cur.execute(sql,(username,))
        recset = cur.fetchall()
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em findauthorizedonus: '+ str(e))

# Lista de equipamentos para reprovisionamento (dataexecucao is null)
def reauthlist():
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = "SELECT serial, operador, data, motivo FROM authservice.reauth where dataexecucao is null order by data asc"
    cur.execute(sql, ())
    recset = cur.fetchall()
    con.close()
    return recset
  except Exception as e:
    errorlog('Falha em reauthlist: '+ str(e))  

# Remove ONU da lista de reprovisionamento
def reauthsave(serial):
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = "update authservice.reauth set dataexecucao = CURRENT_TIMESTAMP where upper(serial) = upper(%s) and dataexecucao is null"
    cur.execute(sql, (serial,))
    con.commit()
    con.close()
  except Exception as e:
    errorlog('Falha em reauthsave: '+ str(e))  
    
# Lista de equipamentos bloqueados
def blocklist():
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = "select serial, removewhitelist from authservice.blocked where validade > current_timestamp"
    cur.execute(sql, ())
    recset = cur.fetchall()
    con.close()
    if(recset):
      return recset
    else:
      return None  
  except Exception as e:
    errorlog('Falha em blocklist: '+ str(e))      

def checkblock(serial):
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = "select serial from authservice.blocked where validade > current_timestamp and serial = %s"
    cur.execute(sql, (serial, ))
    recset = cur.fetchall()
    con.close()
    return recset
  except Exception as e:
    errorlog('Falha em checkblock: '+ str(e))    

# Marca o comando removewhitelist como executado
def blocksave(serial):
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = "update authservice.blocked set removewhitelist = 1 where serial = %s"
    cur.execute(sql, (serial,))
    con.commit()
    con.close()
  except Exception as e:
    errorlog('Falha em blocksave: '+ str(e))  

def getalarms(): 
  try:
    con = __unmdb()
    with con.cursor() as cur:
        sql = '''
		select CONVERT_TZ(a.crecvoccurutctime ,'+00:00','-03:00') as data, a.clocationinfo, a.calarmexinfo, 
                a.cobjectline as porta, d.cobjectname as olt, d.cipaddress as olt_ip
		FROM alarmdb.t_alarmlogcur a
		left join integratecfgdb.t_nedevice d on (d.cobjectid = a.cneid)
		where a.calarmcode = 2431 # Ilegal-onu-registration
		and a.ccleartype = 0 # active
 	'''
        cur.execute(sql,)
        recset = cur.fetchall()
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em getalarms: '+ str(e))

def getalarms_conflict(): 
  try:
    con = __unmdb()
    with con.cursor() as cur:
      sql = '''
        select CONVERT_TZ(a.crecvoccurutctime ,'+00:00','-03:00') as data, a.clocationinfo, a.calarmexinfo, 
        a.cobjectline as porta, d.cobjectname as olt, d.cipaddress as olt_ip
        FROM alarmdb.t_alarmlogcur a
        left join integratecfgdb.t_nedevice d on (d.cobjectid = a.cneid)
        where a.calarmcode = 2579 and a.ccleartype = 0
 	    '''
      cur.execute(sql,)
      recset = cur.fetchall()
      con.close()
    return recset
  except Exception as e:
    errorlog('Falha em getalarms_conflict: '+ str(e))

def findunauthorized(serial):
  try:
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
    sql = "SELECT * FROM {} where upper(serial) = upper(%s)".format(os.getenv('UNAUTHORIZED_TABLE'))
    cur.execute(sql, (serial,))
    recset = cur.fetchall()
    if(recset):
      return json.dumps(
        recset,
        default=default
      )
    else:
      return json.dumps(
        [],
        default=default
      )
    con.close()
    return recset

  except Exception as e:
    errorlog('Falha em findunauthorized: '+ str(e))      

def get_unauthorized(model='FBT'):
  try:
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
    sql = 'SELECT * FROM {} where olt_modelo = %s order by data_atualizacao desc'.format(os.getenv('UNAUTHORIZED_TABLE'))
    cur.execute(sql,(model,))
    recset = cur.fetchall()
    cur.close()
    if(recset):
      return json.dumps(
        recset,
        default=default
      )
    else:
      return json.dumps(
        [],
        default=default
      )

  except Exception as e:
    errorlog('Falha em get_unauthorized: '+ str(e))

def update_unauthorized(items, script):
  try:
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    
    sql = "delete from {} where script = %s".format(os.getenv('UNAUTHORIZED_TABLE'))
    cur.execute(sql,(script,) )
    con.commit()

    for item in items:
      if(not 'username' in item):
        continue
      sql = 'SELECT * FROM {} where upper(serial) = upper(%s)'.format(os.getenv('UNAUTHORIZED_TABLE'))
      cur.execute(sql, (item["serial"],))
      recset = cur.fetchone()
      if(recset == None):
        sql = '''
          INSERT INTO {}
          (data_alarme, serial, olt, olt_ip, placa, porta, modelo, username, mac, patrimonio, script, id_comodato, data_comodato, login_ativo, status, olt_modelo)
          VALUES(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        '''.format(os.getenv('UNAUTHORIZED_TABLE'))

        if(not 'olt_modelo' in item):
          item['olt_modelo'] = 'FBT'

        cur.execute(sql, (
          item["data"],
          item["serial"],
          item["olt"],
          item["olt_ip"],
          item["placa"],
          item["porta"],
          item["modelo"],
          item["username"],
          item["mac"],
          item["patrimonio"],
          script,
          item["id_comodato"],
          item["data_comodato"],
          item["login_ativo"],
          item["status"],
          item['olt_modelo']
        ))
        con.commit()
      
    con.close()
  except Exception as e:
    errorlog('Falha em update_unauthorized: '+ str(e))

def clear_unauthorized():
  try:
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = "delete from {}".format(os.getenv('UNAUTHORIZED_TABLE'))  
    cur.execute(sql, ())
    con.commit()
    con.close()
  except Exception as e:
    errorlog('Falha em clear_unauthorized: '+ str(e))

def update_unauthorized_item(item):
  try:
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    if(item['status'] == 'PROVISIONED'):
      sql = "delete from {} where upper(serial) = upper(%s)".format(os.getenv('UNAUTHORIZED_TABLE'))  
      cur.execute(sql, (item["serial"],))
    else:  
      sql = "UPDATE {} set status=%s where upper(serial) = upper(%s)".format(os.getenv('UNAUTHORIZED_TABLE'))
      cur.execute(sql, (item["status"], item["serial"],))
    con.commit()
    con.close()
  except Exception as e:
    errorlog('Falha em update_unauthorized_item: '+ str(e))

# Localiza patrimonio no IXC
def getpatrimonio(serial):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
        sql = '''
  	      select p.*, IFNULL(p2.descricao_alt, 'GPON CURRENCY SFU') as modelo from ixcprovedor.patrimonio p 
          inner join produtos p2 on (p.id_produto = p2.id)
          where upper(p.serial_fornecedor) = upper(%s)
        '''
        cur.execute(sql,(serial,))
        recset = cur.fetchone()
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em getpatrimonio: '+ str(e))

# Localiza patrimonio alocado no IXC
def getpatrimonio_alocado(serial):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
        sql = '''
          select p.serial as patrimonio, p2.descricao_alt as modelo, p.serial_fornecedor as serial, p.id_mac as mac,
          ru.login, ru.senha, ru.ativo as login_ativo, mp.data as data_comodato, mp.id as id_comodato, c.razao, 
          cd.nome as cidade, cc.status as status_contrato, 
          case when LOCATE('K', upper(rg.download)) > 0 then 
          replace(upper(rg.download), 'K', '') / 1000
          else replace(upper(rg.download), 'M', '') end as plano from patrimonio p
          left join radusuarios ru on (replace(upper(ru.mac), ':', '') = replace(upper(p.id_mac), ':', '')) 
          left JOIN radgrupos rg ON rg.id = ru.id_grupo
          left join movimento_produtos mp on (mp.id_patrimonio = p.id and mp.id_contrato = ru.id_contrato and mp.status_comodato = 'E')
          left JOIN cliente_contrato cc on cc.id = mp.id_contrato 
          left join produtos p2 on (p.id_produto = p2.id)
          left join cliente c on c.id = cc.id_cliente 
          left join cidade cd on (cd.id = cc.cidade)
          where p.id_mac is not null and p.id_mac != ''
          and upper(p.serial_fornecedor) = upper(%s) and p.situacao != 5
          order by data_comodato desc
          '''
        cur.execute(sql,(serial,))
        recset = cur.fetchone()
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em getpatrimonio_alocado: '+ str(e))


# Localiza onus alocadas para o usuario
def get_onus_alocadas(username):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
        sql = '''
          select ru.login, mp.status_comodato, mp.descricao, mp.data, mp.qtde_saida from radusuarios ru 
          left join movimento_produtos mp on (mp.id_contrato = ru.id_contrato)
          where ru.login = %s
          and mp.descricao like '%%ONU%%'
          order by mp.id desc
        '''
        cur.execute(sql,(username,))
        recset = cur.fetchall()
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em get_onus_alocadas: '+ str(e))

# Localiza os pacotes do contrato
def get_pacotes(login):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
        sql = '''
          select cp.descricao from radusuarios ru 
          left JOIN cliente_contrato cc on cc.id = ru.id_contrato
          left join vd_contratos_produtos cp on (cp.id_vd_contrato = cc.id_vd_contrato or cp.id_contrato = cc.id)
          where ru.login = %s
        '''
        cur.execute(sql,(login,))
        recset = cur.fetchall()
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em get_pacotes: '+ str(e))

# Localiza informacoes das vlans
def getvlans(ip_olt, placa, porta):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
        sql = '''
               SELECT olt.descricao as nome_olt, placa.descricao as nome_placa, porta.interface as interface_porta, 
               porta.vlan_pppoe, porta.vlan_iptv FROM ixcprovedor.radpop_radio olt
               inner join ixcprovedor.radpop_olt_slot placa on (placa.id_transmissor = olt.id)
               inner join ixcprovedor.radpop_radio_porta porta on (porta.id_slot = placa.id)
               where olt.ip = %s and placa.numero_slot = %s and porta.numero_pon = %s
        ''' 
        cur.execute(sql,(ip_olt,placa,porta,))
        recset = cur.fetchone()
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em getvlans: '+ str(e))

def get_dados_tecnicos(serial, ip_olt, interface):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
      sql = '''
             select ru.id, rp.id_projeto as id_df_projeto, rpr.id as id_transmissor, rrp.id as interface_transmissao,
             ru.id_cliente, ru.id_grupo, ru.senha, ru.login, ru.login_simultaneo, ru.ativo, ru.ip, ru.mac, ru.obs, 
             ru.auto_preencher_ip, ru.auto_preencher_mac, ru.fixar_ip, ru.id_contrato, ru.autenticacao_por_mac, 
             ru.autenticacao, ru.cache, ru.relacionar_ip_ao_login, ru.relacionar_mac_ao_login, ru.online, 
             ru.concentrador, ru.conexao, ru.tipo_conexao, ru.porta_http, ru.id_concentrador, ru.interface, 
             ru.latitude, ru.longitude, ru.tipo_conexao_mapa, ru.senha_md5, ru.ip_aviso, ru.id_caixa_ftth, 
             ru.senha_router1, ru.senha_router2, ru.senha_rede_sem_fio, ru.ftth_porta, ru.id_porta_transmissor, 
             ru.cliente_tem_a_senha, ru.autenticacao_wps, ru.autenticacao_mac, ru.autenticacao_wpa, 
             ru.tipo_vinculo_plano, ru.tempo_conectado, ru.id_hardware, ru.tipo_equipamento, ru.metragem_interna, 
             ru.metragem_externa, ru.tronco, ru.splitter, ru.sinal_ultimo_atendimento, ru.porta_router2, 
             ru.franquia_maximo, ru.franquia_atingida, ru.endereco, ru.endereco_padrao_cliente, ru.numero, 
             ru.bairro, ru.cidade, ru.cep, ru.complemento, ru.referencia, ru.id_condominio, ru.ssid_router_wifi, 
             ru.bloco, ru.apartamento, ru.vlan, ru.vlan_ip_rede, ru.rota, ru.agent_circuit_id, ru.usuario_router1, 
             ru.pd_ipv6, ru.perfil_autorizar_onu, ru.download_atual, ru.upload_atual, ru.relacionar_concentrador_ao_login, 
             ru.pool_radius, ru.id_rad_dns, ru.modelo_tranmissor, ru.gw_vlan, ru.motivo_desconexao, ru.count_desconexao, 
             ru.tempo_conexao, ru.fixar_ipv6, ru.auto_preencher_ipv6, ru.relacionar_ipv6_ao_login, ru.id_filial, 
             ru.ip_aux, ru.porta_aux, ru.ponta, ru.id_radgrupos_pools, ru.service_tag_vlan, ru.framed_fixar_ipv6, 
             ru.framed_autopreencher_ipv6, ru.framed_relacionar_ipv6_ao_login, ru.framed_relaciona_ipv6_ao_login, 
             ru.framed_pd_ipv6, ru.id_integracao, ru.lte_auth_key, ru.lte_auth_opc, ru.lte_id, ru.lte_apns from ixcprovedor.radpop_radio rpr
             left join ixcprovedor.radpop rp on(rp.id=rpr.id_pop)
             left join ixcprovedor.radpop_radio_porta rrp on(rrp.id_pop_radio=rpr.id)
             left join ixcprovedor.patrimonio p on(upper(p.serial_fornecedor)=upper(%s))
             left join ixcprovedor.radusuarios ru on(replace(upper(ru.mac), ':', '')=replace(upper(p.id_mac), ':', ''))
             where rpr.ip = %s and rrp.interface = %s
            '''
      cur.execute(sql, (serial, ip_olt, interface))
      recset = cur.fetchone()
      con.close()

      #recset['endereco'] = recset['endereco'].encode('utf-8')
    return recset

  except Exception as e:
    errorlog('Falha em get_dados_tecnicos: '+ str(e))

def get_transmissor(serial):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
      sql = '''
        select rpr.descricao as transmissor, rrp.interface from patrimonio p 
        left join ixcprovedor.radusuarios ru on (replace(upper(p.id_mac), ':', '') = replace(upper(ru.mac), ':', '') and (p.id_mac != '' and p.id_mac is not null) )
        left join radpop_radio_porta rrp on (rrp.id = ru.interface_transmissao) 
        left join radpop_radio rpr on (rpr.id = ru.id_transmissor) 
        where upper(serial_fornecedor) = %s
      '''      
      cur.execute(sql, (serial))
      recset = cur.fetchone()
      con.close()
    return recset

  except Exception as e:
    print('Falha em get_transmissor: ' + str(e))			

def checkignorelist_old(serial, *pacote_wifi):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        sql = '''
                SELECT * from authservice.ignore
                where upper(serial) = upper(%s)
              '''
        if(pacote_wifi):
            sql = sql + ' and pacote_wifi = %s'
            cur.execute(sql, (serial, pacote_wifi,))
        else:
            cur.execute(sql, (serial,))
        recset = cur.fetchone()
        con.close()
        return recset
    except Exception as e:
        errorlog('Falha em checkignorelist: ' + str(e))  

def checkignorelist(**kwargs):
    # Se o registro da tabela ignore possuir todos os campos
    # com valor zero, a onu sera ignorada no provisionamento
    # Se algum campo possuir valor 1, apenas sera ignorado
    # o parametro correspondente
    try:
        valid = ['serial', 'pacote_wifi', 'comodato', 'username', 'plano']
        if(len(kwargs) > 0):
            params = {}
            # adiciona apenas os parametros validos
            for key, value in kwargs.items():
                if(key in valid):
                  if(key == 'serial'):
                      value = value.upper()
                  params[key] = value

            # se apenas o parametro username ou serial foi informado
            # adiciona os valores zerados para os demais parametros
            # neste caso, esta sendo verificado se a onu devera ser igorada
            # no provisionamento
            if(('serial' in params or 'username' in params) and len(params) == 1):
                valid = filter(lambda v: v not in ['serial', 'username'], valid)
                valid = list(params.keys()) + list(valid)
                #where_clause = 'WHERE ' + \
                #    ' AND '.join([k + ' = %s' for k in valid])
                where_clause = 'WHERE ' + \
                    ' AND '.join([('upper('+ k + ') = %s' if k == 'serial' else k + ' = %s') for k in valid])
                params = list(params.values())
                params = tuple(params + ([0] * (len(valid)-1)))
            else:
                # filtra pelos parametros informados
                #where_clause = 'WHERE ' + \
                #    ' AND '.join([k + ' = %s' for k in params.keys()])
                where_clause = 'WHERE ' + \
                    ' AND '.join([('upper('+ k + ') = %s' if k == 'serial' else k + ' = %s') for k in params.keys()])
                params = tuple(params.values())

            con = __nocdb()
            cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
            sql = "SELECT * FROM authservice.ignore " + where_clause
            cur.execute(sql, (params))
            recset = cur.fetchone()
            con.close()
            return recset

    except Exception as e:
        errorlog('Falha em checkignore: ' + str(e))


def checkignore(serial, **kwargs):
    # Se o registro da tabela ignore possuir todos os campos
    # com valor zero, a onu sera ignorada no provisionamento
    # Se algum campo possuir valor 1, apenas sera ignorado
    # o parametro correspondente
    try:
        valid = ['serial', 'pacote_wifi', 'comodato', 'plano']
        if(len(kwargs) > 0):
            params = {'serial': serial}
            for key, value in kwargs.items():
                if(key in valid):
                  if(key == 'serial'):
                    value = value.upper()
                  params[key] = value
            where_clause = 'WHERE ' + \
                ' AND '.join([k + ' = %s' for k in params.keys()])
            params = tuple(params.values())
        else:
            where_clause = 'WHERE ' + \
                ' AND '.join([k + ' = %s' for k in valid])
            params = [serial]
            params = tuple(params + ([0] * (len(valid)-1)))

        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        sql = "SELECT * FROM authservice.ignore " + where_clause
        cur.execute(sql, (params))
        recset = cur.fetchone()
        con.close()
        return recset
    except Exception as e:
        errorlog('Falha em checkignorelist: ' + str(e))

def addignorelist(**kwargs):
    # Se o registro da tabela ignore possuir todos os campos
    # com valor zero, a onu sera ignorada no provisionamento
    # Se algum campo possuir valor 1, apenas sera ignorado
    # o parametro correspondente
    try:
        valid = ['serial', 'pacote_wifi', 'comodato', 'motivo','username', 'plano']
        params = {}
        if(len(kwargs) > 0):
            for key, value in kwargs.items():
                if(key in valid):
                    params[key] = value

            if(len(params) > 0):
                fields = ', '.join([k for k in params.keys()])
                query_placeholders = ', '.join(['%s'] * len(params))
                values = tuple(params.values())

                con = __nocdb()
                cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
                sql = 'insert into authservice.ignore ({}) values ({})'.format(
                    fields, query_placeholders)
                cur.execute(sql, (values))
                con.commit()
                con.close()
    except Exception as e:
        errorlog('Falha em addignorelist: ' + str(e))


def get_pacotewifi(username):
    try:
        con = __ixcdb()
        with con.cursor() as cur:
            sql = '''
                select cp.descricao as pacote_wifi from radusuarios ru
                inner join cliente_contrato cc on cc.id = ru.id_contrato
                inner join vd_contratos_produtos cp on cp.id_contrato = cc.id or cp.id_vd_contrato = cc.id_vd_contrato
                where ru.login = %s
                and cp.descricao like '%%WIFI%%'
            '''
            cur.execute(sql, (username))
            recset = cur.fetchone()
            con.close()
        return recset

    except Exception as e:
        print('Falha em get_pacotewifi: ' + str(e))        


def insert_signal_noc(signal):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        sql = '''
          INSERT INTO ftth.sinal (username, comentario, temperature, voltage, biascurrent, sendpower, recvpower, oltrecvpower, rttvalue)
          VALUES(%s, %s, %s, %s, %s, %s, %s, %s, %s);
      '''
        cur.execute(sql, (signal['name'], signal['comentario'], signal['temperature'], signal['voltage'],
                    signal['biascurrent'], signal['sendpower'], signal['recvpower'], signal['oltrecvpower'], signal['rtt'],))
        con.commit()
        con.close()
    except Exception as e:
        print('Falha em insert_sinal_noc: ' + str(e))


def olt_list(model='FBT'):
  try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        #sql = 'select * from authservice.olts where fabricante_modelo = %s'
        sql = '''
              select * from authservice.olts o
              where o.ip::inet not in (select ip from authservice.monitors 
              where enabled = 1) and o.fabricante_modelo = %s
        '''      
        cur.execute(sql, (model,))
        recset = cur.fetchall()
        con.close()
        return recset
  except Exception as e:
      print('Ocorreu um erro em olts: '+ str(e))

def monitors(type='FBT'):
  try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        sql = 'select * from authservice.monitors where type = %s and enabled=1'
        cur.execute(sql, (type,))
        recset = cur.fetchall()
        con.close()
        return recset
  except Exception as e:
      print('Ocorreu um erro em monitors: '+ str(e))     


def updatedevice(device):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        valid_fields = ['serial_acs', 'mac', 'patrimonio', 'name', 'olt', 'olt_ip', 'olt_modelo', 'chassi', 'slot', 'pon', 'onuid', 'type', 'onu_modelo', 'vlan_tr069', 'vlan_pppoe', 'vlan_iptv_unicast', 'vlan_iptv_multicast', 'serial', 'script']

        #filtra os campos extras, permitindo apenas os campos iguais ao valid_fields
        device = {k: v for k, v in device.items() if k in valid_fields}

        # writeLog('teste', 'log.txt')

        # writeLog(device, 'log.txt')

        # if (device['serial'] == 'ZTEGCE5FB978'):
        #   device['type'] = 'F670L'

        #exclui os registros da tabela devices que tenham as mesmas informações com outro serial
        if all(key in device for key in ('olt_ip', 'chassi', 'slot', 'pon', 'onuid')):
          delete_query = "delete from authservice.devices where olt_ip = %s and chassi = %s and slot = %s and pon = %s and onuid = %s"
          cur.execute(delete_query, (device['olt_ip'], device['chassi'], device['slot'], device['pon'], device['onuid'],))

        if('serial' in device):
          sql_query = "SELECT * from authservice.devices where upper(serial) = upper(%s)"
          cur.execute(sql_query, (device['serial'],))
          update_query = sql.SQL("UPDATE authservice.devices SET {data} WHERE serial = {serial}").format(
            data=sql.SQL(', ').join(
              sql.Composed([sql.Identifier(k), sql.SQL(" = "), sql.Placeholder(k)]) for k in device.keys()
            ),
            serial=sql.Placeholder('serial')
          )
        elif('mac' in device):
          sql_query = "SELECT * from authservice.devices where replace(upper(mac), ':', '') = replace(upper(%s), ':', '')"
          cur.execute(sql_query, (device['mac'],))
          update_query = sql.SQL("UPDATE authservice.devices SET {data} WHERE replace(upper(mac), ':', '') = replace(upper({mac}), ':', '')").format(
            data=sql.SQL(', ').join(
              sql.Composed([sql.Identifier(k), sql.SQL(" = "), sql.Placeholder(k)]) for k in device.keys()
            ),
            mac=sql.Placeholder('mac')
          )
        else:
          return False  
        recset = cur.fetchone()
        if(recset):
          #print(cur.mogrify(update_query, device))
          with con:
            with con.cursor() as cur:
              cur.execute(update_query, device)
            commit()    
        else:
          insert_query = sql.SQL("INSERT INTO authservice.devices ({columns}) VALUES ({data})").format(
            columns=sql.SQL(', ').join(
              sql.Composed([sql.Identifier(k)]) for k in device.keys()),
            data=sql.SQL(",").join(map(sql.Placeholder, device))
          )     
          #print(cur.mogrify(insert_query, device))
          with con:
            with con.cursor() as cur:
              cur.execute(insert_query, device)
            commit()    
    except Exception as e:
        print('Ocorreu um erro em updatedevice: '+ str(e))   

def update_job(job):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        valid_fields = ['id', 'enqueued_at', 'status', 'description', 'started_at', 'ended_at', 'exc_info', 'worker_name',  'last_heartbeat', 'origin', 'func_name', 'kwargs', 'provision_id']

        #filtra os campos extras, permitindo apenas os campos iguais ao valid_fields
        job = {k: v for k, v in job.items() if k in valid_fields}

        if('id' in job):
          sql_query = "SELECT * from authservice.jobs where id = %s"
          cur.execute(sql_query, (job['id'],))
          update_query = sql.SQL("UPDATE authservice.jobs SET {data} WHERE id = {id}").format(
            data=sql.SQL(', ').join(
              sql.Composed([sql.Identifier(k), sql.SQL(" = "), sql.Placeholder(k)]) for k in job.keys()
            ),
            id=sql.Placeholder('id')
          )
        else:
          return False  
        recset = cur.fetchone()
        if(recset):
          #print(cur.mogrify(update_query, device))
          with con:
            with con.cursor() as cur:
              cur.execute(update_query, job)
          con.commit()  
        else:
          insert_query = sql.SQL("INSERT INTO authservice.jobs ({columns}) VALUES ({data})").format(
            columns=sql.SQL(', ').join(
              sql.Composed([sql.Identifier(k)]) for k in job.keys()),
            data=sql.SQL(",").join(map(sql.Placeholder, job))
          )     
          #print(cur.mogrify(insert_query, device))
          with con:
            with con.cursor() as cur:
              cur.execute(insert_query, job)
            con.commit()  
    except Exception as e:
        print('Ocorreu um erro em update_job: '+ str(e))   

'''
def update_provision_status(provision):
  try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        #verifica se todos as tarefas foram finalizadas
        sql = "
        SELECT COUNT(case when status <> 'finished' then j.provision_id end) as pending,
        COUNT(case when status = 'finished' then j.provision_id end) as finished,
        COUNT(case when status = 'queued' then j.provision_id end) as queued,
        COUNT(case when status = 'failed' then j.provision_id end) as failed
        from authservice.jobs j where provision_id = %s
        "
        cur.execute(sql, (provision,))
        recset = cur.fetchone()
        if(recset['pending'] == 0):
          sql = "update authservice.provisions set status = 'finished', ended_at = current_timestamp where id = %s and status != 'finished'"
          cur.execute(sql, (provision,))
          con.commit()
        #se alguma tarefa falhou marca como falha no provisionamento  
        if(recset['failed'] > 0):
          sql = "update authservice.provisions set status = 'failed', last_heartbeat = current_timestamp where id = %s"
          cur.execute(sql, (provision,))
          con.commit()
        #se alguma tarefa esta na fila, marca como queued no provisionamento
        if(recset['queued'] > 0):
          sql = "update authservice.provisions set status = 'queued', last_heartbeat = current_timestamp where id = %s"
          cur.execute(sql, (provision,))
          con.commit()  

        con.close()
        return recset
  except Exception as e:
      print('Ocorreu um erro em monitors: '+ str(e))     
'''


def update_provision(provision):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        valid_fields = ['id', 'serial', 'source', 'status', 'enqueued_at', 'created_at', 'started_at', 'ended_at', 'last_heartbeat', 'exc_info', 'username', 'model', 'olt', 'olt_ip', 'olt_model', 'slot', 'pon']

        #filtra os campos extras, permitindo apenas os campos iguais ao valid_fields
        provision = {k: v for k, v in provision.items() if k in valid_fields}
        recset = None

        if('id' in provision):
          sql_query = "SELECT * from authservice.provisions where id = %s"
          cur.execute(sql_query, (provision['id'],))
          update_query = sql.SQL("UPDATE authservice.provisions SET {data} WHERE id = {id}").format(
            data=sql.SQL(', ').join(
              sql.Composed([sql.Identifier(k), sql.SQL(" = "), sql.Placeholder(k)]) for k in provision.keys()
            ),
            id=sql.Placeholder('id')
          )
        #else:
        #  return False  
          recset = cur.fetchone()
        #se ja existe o provisionamento, atualiza
        if(recset):
          #print(cur.mogrify(update_query, device))
          with con:
            with con.cursor() as cur:
              cur.execute(update_query, provision)
            con.commit()  
          return recset[0]
        else:
          #se o status do provisionamento for canceled
          #verifica se o provisionamento anterior tambem foi canceled
          #caso positivo, atualiza o provisionamento anterior para
          #evitar excesso de registros canceled
          if(provision['status'] == 'canceled'):
            sql_query = "SELECT * FROM authservice.provisions where serial = %s order by enqueued_at desc limit 1"
            cur.execute(sql_query, (provision['serial'],))
            recset = cur.fetchone()

            if(recset):
              if(recset['status'] == 'canceled'):
                provision['id'] = recset['id']
                update_query = sql.SQL("UPDATE authservice.provisions SET {data} WHERE id = {id}").format(
                  data=sql.SQL(', ').join(
                    sql.Composed([sql.Identifier(k), sql.SQL(" = "), sql.Placeholder(k)]) for k in provision.keys()
                  ),
                  id=sql.Placeholder('id')
                )
                with con:
                  with con.cursor() as cur:
                    cur.execute(update_query, provision)
                  con.commit()  
                return provision
              else:
                insert_query = sql.SQL("INSERT INTO authservice.provisions ({columns}) VALUES ({data}) RETURNING id;").format(
                  columns=sql.SQL(', ').join(
                    sql.Composed([sql.Identifier(k)]) for k in provision.keys()),
                  data=sql.SQL(",").join(map(sql.Placeholder, provision))
                )     
                #print(cur.mogrify(insert_query, device))
                with con:
                  with con.cursor() as cur:
                    cur.execute(insert_query, provision)
                    return cur.fetchone()[0]
            else:
              insert_query = sql.SQL("INSERT INTO authservice.provisions ({columns}) VALUES ({data}) RETURNING id;").format(
                columns=sql.SQL(', ').join(
                  sql.Composed([sql.Identifier(k)]) for k in provision.keys()),
                data=sql.SQL(",").join(map(sql.Placeholder, provision))
              )     
              #print(cur.mogrify(insert_query, device))
              with con:
                with con.cursor() as cur:
                  cur.execute(insert_query, provision)
                  con.commit()
                  return cur.fetchone()[0]        
          else:
            insert_query = sql.SQL("INSERT INTO authservice.provisions ({columns}) VALUES ({data}) RETURNING id;").format(
              columns=sql.SQL(', ').join(
                sql.Composed([sql.Identifier(k)]) for k in provision.keys()),
              data=sql.SQL(",").join(map(sql.Placeholder, provision))
            )     
            #print(cur.mogrify(insert_query, device))
            with con:
              with con.cursor() as cur:
                cur.execute(insert_query, provision)
                con.commit()
                return cur.fetchone()[0]         
                  
    except Exception as e:
        print('Ocorreu um erro em update_provision: '+ str(e))  


def pending_provision(serial):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        sql_query = "SELECT * from authservice.provisions where upper(serial) = upper(%s) and (status = 'queued' or status='started')"
        cur.execute(sql_query, (serial,))
        recset = cur.fetchone()
        return recset

    except Exception as e:
        print('Ocorreu um erro em pending_provision: '+ str(e))  

def get_jobs_test():
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        sql_query = "SELECT * FROM authservice.jobs where description in ('VEIP_PPPOE', 'VEIP_DHCP') and started_at > '02-03-2023' order by started_at desc"
        cur.execute(sql_query, ())
        recset = cur.fetchall()
        return recset

    except Exception as e:
        print('Ocorreu um erro em get_jobs_test: '+ str(e))  

def getdevice(serial=None, olt=None):
  try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        if(serial):
            sql_query = "SELECT * from authservice.devices where upper(serial) = upper(%s)"
            cur.execute(sql_query, (serial,))
            recset = cur.fetchone()

        if(olt):
            sql_query = "SELECT * from authservice.devices where (olt_ip) = %s order by chassi, slot, pon, id"
            cur.execute(sql_query, (olt,))
            recset = cur.fetchall()

        con.close()
        if(recset):
            return recset
        else:
            return {}    

  except Exception as e:
       # app.logger.error(str(e))
       print(str(e))

def removedevice(serial=None, olt=None, chassi=None, slot=None, pon=None, onuid=None):
  try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        if(serial):
            sql_query = "DELETE from authservice.devices where upper(serial) = upper(%s)"
            cur.execute(sql_query, (serial,))
            con.commit()
        elif(all([olt, chassi, slot, pon, onuid])):    
            sql_query = "DELETE from authservice.devices where olt_ip=%s and chassi=%s and slot=%s and pon=%s and onuid=%s"
            cur.execute(sql_query, (olt,chassi,slot,pon,onuid,))
            con.commit()
        con.close()

  except Exception as e:
       # app.logger.error(str(e))
       print(str(e))   

def getdevices(olt=None, slot=None, pon=None):
  try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        sql_query = "SELECT mac from authservice.devices where olt_ip = % and slot = %s"
        cur.execute(sql_query, (olt, slot,))
        recset = cur.fetchall()

        con.close()
        if(recset):
            return recset
        else:
            return []

  except Exception as e:
       # app.logger.error(str(e))
       print(str(e))     

def finish_provisions():
  try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        # Atualiza os provisionamentos que nao possuem tarefas pendentes
        # e que nao estao com status 'finished' ou 'canceled'
        sql_query = '''
          UPDATE authservice.provisions
          SET status = 'finished',
          ended_at = current_timestamp
          FROM (select p.id FROM authservice.provisions p
          LEFT JOIN authservice.jobs j
          ON j.provision_id = p.id AND j.status != 'finished'
          WHERE p.status != 'finished' and p.status != 'canceled'
          AND j.id IS null) AS subquery
          WHERE authservice.provisions.id=subquery.id;	 
        '''  
        cur.execute(sql_query)
        con.commit()

        # Remove os provisionamentos parados na fila ha mais de 30 minutos
        sql_query = '''
          update authservice.provisions p set status = 'failed' WHERE enqueued_at < NOW() - INTERVAL '30 minutes' and status = 'queued'
        '''
        cur.execute(sql_query)
        con.commit()

        con.close()

  except Exception as e:
       # app.logger.error(str(e))
       print(str(e)) 

def getonus(olt=None, slot=None, pon=None):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Base da query
        sql_query = "SELECT * FROM ftth.onus"
        filters = []
        params = []

        # Monta dinamicamente o WHERE
        if olt is not None:
            filters.append("olt_ip = %s")
            params.append(olt)
        if slot is not None:
            filters.append("placa = %s")
            params.append(slot)
        if pon is not None:
            filters.append("porta = %s")
            params.append(pon)

        if filters:
            sql_query += " WHERE " + " AND ".join(filters)
        sql_query += " AND modelo is not null"
        cur.execute(sql_query, params)
        recset = cur.fetchall()

        con.close()
        return recset if recset else []

    except Exception as e:
        print(str(e))
        return []    

def get_login(mac):
    try:
        con = __ixcdb()
        with con.cursor() as cur:
            sql = '''
                SELECT login
                FROM radusuarios ru
                WHERE UPPER(REGEXP_REPLACE(ru.mac, '[^A-Fa-f0-9]', '')) = UPPER(REGEXP_REPLACE(%s, '[^A-Fa-f0-9]', '')) LIMIT 1;
            '''
            cur.execute(sql, (mac))
            recset = cur.fetchone()
            con.close()
        if(recset):
            return recset['login']
        else:
            return None


    except Exception as e:
        print('Falha em get_login: ' + str(e))   
        