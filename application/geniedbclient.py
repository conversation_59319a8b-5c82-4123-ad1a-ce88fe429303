import pymongo

class GenieDBClient:
    def __init__(self, url, database, collection):
        self.url = url
        self.database = database
        self.collection = collection
        
        self.client = pymongo.MongoClient(self.url)
        self.db = self.client[database]
        self.coll = self.db[collection]

    def find_onu_id(self, mac):
        return self.coll.find_one({"InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1.MACAddress._value": mac}, { '_tags': 1 })

    def close(self):
        self.client.close()