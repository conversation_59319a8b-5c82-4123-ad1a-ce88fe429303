from database import *
from dotenv import load_dotenv
import requests
from geniedbclient import GenieDBClient
import time

load_dotenv()

def removeacs(mac):
    try:
        mac = mac.replace(':', '')
        mac = ':'.join(mac[i:i+2] for i in range(0,12,2))
    
        #consulta a onu no banco do genie
        geniedbclient = GenieDBClient(os.getenv('GENIEACS_MONGODB_CONNECTION_URL'), 'genieacs', 'devices')
        onu = geniedbclient.find_onu_id(mac)
        geniedbclient.close()

        if(onu):
            onuid = onu['_id']
            tags = onu['_tags']

            if(not 'Reauth' in tags):
                url = 'http://{}:7557/devices/{}/tasks?timeout=10&connection_request'.format(os.getenv('ACS_IP'), onuid) 
            
                pload = {'name':'reboot'}
                r = requests.post(url, data=json.dumps(pload))
            
                url = 'http://{}:7557/devices/{}'.format(os.getenv('ACS_IP'), onuid)
                r = requests.delete(url)

                #em seguida remove todas as onus filtrando pelo username (caso existam)
                patrimonio = getpatrimonio_alocado(mac)
                if(patrimonio):
                    username = patrimonio['login']
                    url = 'http://{}:7557/devices/?query=%7B%22InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.2.Username%22%3A%22{}%22%7D&projection=_id'.format(os.getenv('ACS_IP'), username)
                    r = requests.get(url)
                    r_dictionary = r.json()

                    if(len(r_dictionary) > 0):
                        for item in r_dictionary:
                            onuid = item['_id']
                
                            #envia um comando de reboot antes de excluir
                            url = 'http://{}:7557/devices/{}/tasks?timeout=10&connection_request'.format(os.getenv('ACS_IP'), onuid) 

                            pload = {'name':'reboot'}
                            r = requests.post(url, data=json.dumps(pload))

                            url = 'http://{}:7557/devices/{}'.format(os.getenv('ACS_IP'), onuid)
                            r = requests.delete(url)
                            return True
            else:
                removetag(mac, 'Reauth')     

        else:
            return True
    except Exception as e:
        raise    


def addtag(mac, tag):
    try:
        mac = mac.replace(':', '')
        mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

        #consulta a onu no banco do genie
        geniedbclient = GenieDBClient(os.getenv('GENIEACS_MONGODB_CONNECTION_URL'), 'genieacs', 'devices')
        onu = geniedbclient.find_onu_id(mac)
        geniedbclient.close()

        if(onu):
            onuid = onu['_id']
            url = 'http://{}:7557/devices/{}/tags/{}'.format(os.getenv('ACS_IP'), onuid, tag)
            r = requests.post(url)
    except Exception as e:
        raise


def removetag(mac, tag):
    try:
        mac = mac.replace(':', '')
        mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

        #consulta a onu no banco do genie
        geniedbclient = GenieDBClient(os.getenv('GENIEACS_MONGODB_CONNECTION_URL'), 'genieacs', 'devices')
        onu = geniedbclient.find_onu_id(mac)
        geniedbclient.close()

        if(onu):
            onuid = onu['_id']
            url = 'http://{}:7557/devices/{}/tags/{}'.format(os.getenv('ACS_IP'), onuid, tag)
            r = requests.delete(url)
    except Exception as e:
        raise         

def changeinformperiod(mac, period):
    try:
        mac = mac.replace(':', '')
        mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

        #consulta a onu no banco do genie
        geniedbclient = GenieDBClient(os.getenv('GENIEACS_MONGODB_CONNECTION_URL'), 'genieacs', 'devices')
        onu = geniedbclient.find_onu_id(mac)
        geniedbclient.close()

        if(onu):
            onuid = onu['_id']
        
            url = 'http://{}:7557/devices/{}/tasks?timeout=10&connection_request'.format(os.getenv('ACS_IP'), onuid) 
            
            pload = {
                    "name": "setParameterValues",
                    "parameterValues": [
                        ["InternetGatewayDevice.ManagementServer.PeriodicInformEnable", "true"],  
                        ["InternetGatewayDevice.ManagementServer.PeriodicInformInterval", period]
                    ]}
            r = requests.post(url, data=json.dumps(pload))
    except Exception as e:
        raise



def changepppoevlanid(mac, vlan):
    try:
        if mac == None:
            return False
        
        mac = mac.replace(':', '')
        mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

        #consulta a onu no banco do genie
        geniedbclient = GenieDBClient(os.getenv('GENIEACS_MONGODB_CONNECTION_URL'), 'genieacs', 'devices')
        onu = geniedbclient.find_onu_id(mac)
        geniedbclient.close()

        if(onu):
            onuid = onu['_id']

            url = 'http://{}:7557/devices/{}/tasks?timeout=10&connection_request'.format(os.getenv('ACS_IP'), onuid) 
            
            pload = {
                    "name": "setParameterValues",
                    "parameterValues": [
                        ["InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.2.X_ZTE-COM_VLANID", vlan]
                    ]}

            r = requests.post(url, data=json.dumps(pload))
    except Exception as e:
        raise

def registered(mac):
  try:
    mac = mac.replace(':', '')
    mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

    #consulta a onu no banco do genie
    geniedbclient = GenieDBClient(os.getenv('GENIEACS_MONGODB_CONNECTION_URL'), 'genieacs', 'devices')
    onu = geniedbclient.find_onu_id(mac)
    geniedbclient.close()

    if(onu):
        return True
    else:
      raise Exception("ONU not found on ACS") 
  except Exception as e:
    raise 

def reboot(mac):
  try:
    #consulta a onu no banco do genie
    geniedbclient = GenieDBClient(os.getenv('GENIEACS_MONGODB_CONNECTION_URL'), 'genieacs', 'devices')
    onu = geniedbclient.find_onu_id(mac)
    geniedbclient.close()

    if(onu):
        onuid = onu['_id']
    
        url = 'http://{}:7557/devices/{}/tasks?timeout=10&connection_request'.format(os.getenv('ACS_IP'), onuid) 
            
        pload = {'name':'reboot'}
        r = requests.post(url, data=json.dumps(pload))
        return True
    else:
        return True
  except Exception as e:
    raise    

start_time = time.time()
print(registered('34:24:3e:2b:ba:6e'))
print("--- %s seconds ---" % (time.time() - start_time))
