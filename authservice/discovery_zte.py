#!/usr/bin/python
# -*- coding: utf-8 -*-
import os
import time
from database import *
from config import *
from multiprocessing import Process
import time
import sys
from datetime import date
from ztemanager import ZTEManager
import threading


scriptname = 'AUTHSERVICE_DISCOVERY_ZTE'


def getdiscovery():
  try:
    unauth_list = []
    for olt in olt_list('ZTE'):
      try:
        manager = ZTEManager(olt['ip'], olt['descricao'])
      #  self.managers.append(manager) 
      #for manager in self.managers:
      #  if(not manager.connected):
      #    manager.connect()
        response = manager.list_unauth()
        print(response)
        manager.disconnect()
        del manager
        unauth_list = unauth_list + response
      except:
        pass  
    onus=[]
    for unauth in unauth_list:

        onu = {
            'olt': unauth["olt_name"],
            'olt_ip': unauth["olt_ip"],
            'olt_modelo': 'ZTE',
            'chassi' : unauth['chassi'],
            'slot': unauth['slot'],
            'pon': unauth['pon'],
            'placa': unauth['slot'],
            'porta': unauth['pon'],
            'serial': unauth['serial'],
            
        }

        onu['onu_type'] = unauth['type']
        if('670' in unauth['type']):
            onu['modelo'] = 'F670L' 
            onu['onu_modelo'] = 'F670L'
        if('660' in unauth['type']):
            onu['modelo'] = 'F660' 
            onu['onu_modelo'] = 'F660'

        onu['data'] = date.today()
        onu['mac'] = None
        onu['login'] = None 
        onu['patrimonio'] = None 
        onu['login_ativo'] = None
        onu['data_comodato'] = None
        onu['id_comodato'] = None
        onu['status'] = 'WAITING'

        patrimonio = getpatrimonio_alocado(onu['serial']) 

        if(patrimonio):
            onu['mac'] = patrimonio['mac']
            onu['patrimonio'] = patrimonio['patrimonio']
            onu['login_ativo'] = patrimonio['login_ativo']
            onu['data_comodato'] = patrimonio['data_comodato']
            onu['id_comodato'] = patrimonio['id_comodato']
            onu['username'] = patrimonio['login']  
            onu['login_ativo'] = patrimonio['login_ativo']
            onu['plano'] = patrimonio['plano']

            if('ZTE' in onu['serial'].upper()):
                onu['tr069'] = 1
            else:
                onu['tr069'] = 0  

            if(((len(ONUS_TESTE) > 0) and (onu['serial'].upper() in ONUS_TESTE) and (TEST_MODE)) or (TEST_MODE == False)): 
                if(IGNORE_LIST_ENABLED):
                  #if(checkignorelist(onu["serial"], 0)):
                  if(checkignorelist(serial=onu["serial"])):
                    msg = '[{}]  ONU {} esta na lista para ignorar e nao sera autorizada'.format(scriptname, onu["serial"])
                    print(msg)
                  else:  
                    onus.append(onu)  
                    #updatedevice(onu)
                else:    
                  onus.append(onu)
                  #updatedevice(onu)

    update_unauthorized(onus, scriptname)
  except:
    pass


if __name__ == '__main__':

  try:
    getdiscovery()
  except Exception as e:
    print('Ocorreu um erro em __main__: '+str(e))