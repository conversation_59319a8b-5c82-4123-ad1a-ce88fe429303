import os
import time
from dotenv import load_dotenv
from datetime import datetime
import re
import sys
import json
from netmiko import ConnectHandler


class ZTEManager:
    """Class for ZTE OLT Management ONU provisioning and configuration

    :param olt: OLT IP Address, defaults to None
    :type olt: str, required

    :param debug: Enable debug information
    :type debug: bool, optional


      Methods
      -------
      connect
          Start ssh connection on OLT
      disconnect
          Stop ssh connection on OLT    
      list_auth(chassi, slot, pon)
          List authorized ONUs
      find_auth(sn)    
          Find authorized ONU
      list_unauth() 
          Retrieve unauthorized ONUs
      auth(sn, type, name)
          Authorize ONU
      deauth(sn)
          Deauthorize ONU    
      bind_profile(sn, mode)    
          Bind profile to ONU
      config_vlans(sn)
          Configure ONU VLANs  
      config_onu(sn, vlan_tr069, vlan_pppoe, vlan_iptv_unicast, vlan_iptv_multicast)
          Configure ONU    
      show_config_onu(sn)
          Show ONU configuration    
      show_signal(sn)
          Show ONU signal
      list_signal(chassi, slot, pon):
          Show OLT/ONU signal list    
      show_detail(sn)
          Show ONU detail
      show_eth_interface(sn, number)
          Show ONU eth interface detail    
      """

    def __init__(self, olt, olt_name=None):
        """
            Parameters
            ----------
            olt : dict
                OLT information
        """
        #try:
        load_dotenv()
        device = {
            'device_type': 'zte_zxros',
            'ip': olt,
            'username': os.getenv('OLT_ZTE_USERNAME'),
            'password': os.getenv('OLT_ZTE_PASSWORD'),
            'secret': os.getenv('OLT_ZTE_PASSWORD'),
            'global_cmd_verify': False,
            'fast_cli': True
        }
        self.olt = olt
        self.olt_name = olt_name
        self.__ssh = ConnectHandler(**device)
        self.__ssh.enable()
        #except Exception as e:
        #    print("Error (init): "+str(e))

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        pass

    def disconnect(self):
        """ Close OLT SSH connection """
        try:
            self.__ssh.disconnect()
        except Exception as e:
            return "Error (disconnect): "+str(e)

    def send_command(self, cmd):
        res = self.__ssh.send_command(cmd, expect_string='#', delay_factor=5)
        print(res)
        return res

    def list_auth(self, chassi, slot, pon):
        """ List authorized ONUs 

        Parameters
        ----------
        chassi : int
            OLT chassi number
        slot : int
            OLT slot number    
        pon : int
            OLT slot pon number    

        Returns
        -------
        dict
            Authorized ONUs list

        Raises
        ------
        Error

        """
        try:
            cmd = "configure terminal\ninterface gpon_olt-{}/{}/{}\nshow this\nend".format(
                chassi, slot, pon)
            res = self.__ssh.send_command(cmd)

            # lista de onus esta entre as tags !<xpon> e !</xpon> da string retornada
            a, b = res.find('!<xpon>'), res.find('!</xpon>')
            if(a != -1):
                # recupera a lista entre as tags
                s = res[a+7:b]
                # retira espacos em branco da string e divide as linhas em um array
                s = s.strip()
                s = s.splitlines()
                # retira os espacos de cada item
                list = map(str.strip, s)

                onulist = []
                for item in list:
                    # localiza o padrao dos itens (Exemplo: onu 4 type AN5506-02-B sn FHTT92DF9650)
                    p = re.compile("onu (.*) type (.*) sn (.*)")
                    result = p.search(item)
                    if(result):
                        # atribui cada grupo de string localizada ao valor da chave correspondente
                        onu = {
                            'id': int(result.group(1)),
                            'type': result.group(2),
                            'sn': result.group(3)
                        }
                        onulist.append(onu)
                # monta uma lista apenas com os ids
                ids = [int(d['id']) for d in onulist]
                ids.sort()
                start, end = ids[0], ids[-1]
                if(start > 1):
                    next_id = 1
                else: 
                    # atribui os ids faltantes a variavel d
                    d = sorted(set(range(start, end + 1)).difference(ids))
                    # atribui a variavel next_id o primeiro id faltante (caso exista) ou incrementa o end
                    if(len(d) > 0):
                        next_id = next(iter(d))
                    else:
                        next_id = end+1

                return {
                    'onus': onulist,
                    'first_id': int(start),
                    'last_id': int(end),
                    'next_id': int(next_id)
                }
            else:
                return {
                    'onus': [],
                    'first_id': None,
                    'last_id': None,
                    'next_id': 1
                }
        except Exception as e:
            return "Error (auth_list): "+str(e)

    def list_onu_state(self):
        """ List ONUs states

        Returns
        -------
        dict
            ONU state list

        Raises
        ------
        Error

        """
        try:
            cmd = "show gpon onu state"
            res = self.__ssh.send_command(cmd)
            onus = []
            if '---' in res:
                res = res.splitlines()
                index = next(i for i, w in enumerate(res) if '---' in w)
                for data in res[index+1:]:
                    # normaliza a string deixando com apenas um espaco por valor
                    data = ' '.join(data.split())
                    p = re.compile('(.*)/(.*)/(.*):(.*) (.*) (.*) (.*) (.*)')
                    search = p.search(data)
                    if(search):
                        onu = {
                            'chassi': search.group(1),
                            'slot': search.group(2),
                            'pon': search.group(3),
                            'onuid': search.group(4),
                            'admin': search.group(5),
                            'omcc': search.group(6),
                            'phase': search.group(7),
                            'speed': search.group(8)
                        }
                        onus.append(onu)
                        import copy
                        onu = copy.deepcopy(onu)
                        onu['onuid'] = int(onu['onuid']) +1
                        onus.append(onu)
                        onu = copy.deepcopy(onu)
                        onu['onuid'] = int(onu['onuid']) +1
                        onus.append(onu)
                        onu = copy.deepcopy(onu)
                        onu['onuid'] = int(onu['onuid']) +1
                        onus.append(onu)
                        onu = copy.deepcopy(onu)
                        onu['onuid'] = int(onu['onuid']) +1
                        onus.append(onu)
                        onu = copy.deepcopy(onu)
                        onu['onuid'] = int(onu['onuid']) +1
                        onus.append(onu)
                        onu = copy.deepcopy(onu)
                        onu['onuid'] = int(onu['onuid']) +1
                        onus.append(onu)
                        onu = copy.deepcopy(onu)
                        onu['onuid'] = int(onu['onuid']) +1
                        onus.append(onu)
                        onu = copy.deepcopy(onu)
                        onu['onuid'] = int(onu['onuid']) +1
                        onus.append(onu)
                        onu = copy.deepcopy(onu)
                        onu['onuid'] = int(onu['onuid']) +1
                        onus.append(onu)
                 
            return onus                            

        except Exception as e:
            return "Error (list_onu_state): "+str(e)

    def find_auth(self, sn):
        """ Find authorized ONU 

        Parameters
        ----------
        sn : str
            ONU serial number

        Returns
        -------
        dict
            Authorized ONU

        Raises
        ------
        Error

        """
        try:
            cmd = "show gpon onu by sn {}".format(sn)
            res = self.__ssh.send_command(cmd)
            time.sleep(0.1)

            if 'No related information to show' in res:
                return False
            else:
                if '---' in res:
                    res = res.splitlines()
                    # localiza a linha pontilhada que separa o resultado da pesquisa
                    index = next(i for i, w in enumerate(res) if '---' in w)
                    chassi = res[index+1].split('/')[0].split('-')[1]
                    slot = res[index+1].split('/')[1]
                    pon = res[index+1].split('/')[2].split(':')[0]
                    onuid = res[index+1].split('/')[2].split(':')[1]

                    onu = {
                        'chassi': int(chassi),
                        'slot': int(slot),
                        'pon': int(pon),
                        'onuid': int(onuid)
                    }
                    return onu
        except Exception as e:
            return "Error (find_auth): "+str(e)

    def list_unauth(self):
        """ Retrieve unauthorized ONUs 

        Returns
        -------
        dict
            Unauthorized ONUs list

        Raises
        ------
        Error

        """
        try:
            unauthorized = []
            cmd = "show pon onu uncfg"
            res = self.__ssh.send_command(cmd)

            # localiza a linha pontilhada que separa a lista de onus nao autorizadas
            if '---' in res:
                res = res.splitlines()
                index = next(i for i, w in enumerate(res) if '---' in w)

                for data in res[index+1:]:
                    # normaliza a string deixando com apenas um espaco por valor
                    data = ' '.join(data.split())
                    # divide os valores em lista, separando cada valor por espaco
                    data = data.split(' ')
                    if(len(data)) == 4:
                        type = data[1]
                        serial = data[2]
                        chassi = int(data[0].split('/')[0].split('-')[1])
                        slot = int(data[0].split('/')[1])
                        pon = int(data[0].split('/')[2])
                        onu = {
                            'olt_ip': self.olt,
                            'olt_name': self.olt_name,
                            'olt_model': 'ZTE',
                            'chassi': chassi,
                            'slot': slot,
                            'pon': pon,
                            'serial': serial,
                            'type': type
                        }
                        unauthorized.append(onu)
            return unauthorized
        except Exception as e:
            return "Error (unauth_list): "+str(e)

    def auth(self, serial, type, name):
        """ Authorize ONU 

        Parameters
        ----------
        sn : str
            ONU serial number
        type : str
            ONU type model
        name : str
            ONU name   

        Returns
        -------
        int
            Authorized ONU index

        """
        try:

            # verifica se a onu esta na lista aguardando autorizacao
            onus = self.list_unauth()

            unauthorized = next(
                (item for item in onus if item["serial"] == serial), None)
            if(unauthorized):
                unauthorized['type'] = type
                unauthorized['name'] = name
                # consulta o proximo id para autorizacao
                list = self.list_auth(
                    unauthorized['chassi'], unauthorized['slot'], unauthorized['pon'])
                next_id = list['next_id']
                unauthorized['onuid'] = next_id
                cmd = "configure terminal\ninterface gpon_olt-{}/{}/{}\nonu {} type {} sn {}\nend".format(
                    unauthorized['chassi'],
                    unauthorized['slot'],
                    unauthorized['pon'],
                    next_id,
                    unauthorized['type'],
                    unauthorized['serial'])

                res = self.__ssh.send_command(cmd)

                if('Not support this ONU' in res):
                    return 'ONU type not supported'
                else:
                    # Nomeia a ONU
                    cmd = "configure terminal\ninterface gpon_onu-{}/{}/{}:{}\nname {}\nend".format(
                        unauthorized['chassi'],
                        unauthorized['slot'],
                        unauthorized['pon'],
                        next_id,
                        name
                    )
                    res = self.__ssh.send_command(cmd)
                    return unauthorized
            return "ONU not found"
        except Exception as e:
            return "Error (auth): "+str(e)

    def deauth(self, sn=None, chassi=None, slot=None, pon=None, onuid=None):
        """ Deauthorize ONU 

        Parameters
        ----------
        sn : str
            ONU serial number
        """
        try:
            if(sn):
                onu = self.find_auth(sn)
            else:
                if(all([chassi, slot, pon, onuid])):
                    onu = {
                        "chassi": chassi,
                        "slot" : slot,
                        "pon" : pon,
                        "onuid" : onuid
                    }    

            if(onu):
                cmd = "configure terminal\ninterface gpon_olt-{}/{}/{}\nno onu {}\nend".format(
                    onu["chassi"], onu["slot"], onu["pon"], onu["onuid"])
                res = self.__ssh.send_command(cmd)

                return True
            else:
                return False    

        except Exception as e:
            return "Error (unauth): " + str(e)

    def block_onu(self, sn):
        try:

            onu = self.find_auth(sn)

            if(onu):
                cmd = """
                configure terminal
                interface gpon_onu-{}/{}/{}:{}
                disable
                """.format(onu["chassi"],
                    onu['slot'],
                    onu['pon'],
                    onu['onuid'],)
                res = self.__ssh.send_command(cmd)

                return onu    
        except Exception as e:
            return "Error (block_onu): " + str(e)

    def unblock_onu(self, sn):
        try:

            onu = self.find_auth(sn)

            if(onu):
                cmd = """
                configure terminal
                interface gpon_onu-{}/{}/{}:{}
                enable
                """.format(onu["chassi"],
                    onu['slot'],
                    onu['pon'],
                    onu['onuid'],)
                res = self.__ssh.send_command(cmd)

                return onu    
        except Exception as e:
            return "Error (unblock_onu): " + str(e)
    '''
    Comando nao funciona
    Verificar documentacao
    def clear_config_onu(self, sn):
        try:

            onu = self.find_auth(sn)

            if(onu):
                cmd = """
                configure terminal
                pon
                clear onu gpon-onu_{}/{}/{}:{}
                """.format(onu["chassi"],
                    onu['slot'],
                    onu['pon'],
                    onu['onuid'],)
                res = self.__ssh.send_command(cmd)

                return onu    
        except Exception as e:
            return "Error (clear_config_onu): " + str(e)
    '''
    
    def config_onu(self, sn, vlan_tr069, vlan_pppoe, vlan_iptv_unicast, vlan_iptv_multicast):
        """ Configure ONU

        Parameters
        ----------
        sn : str
            ONU serial number
        vlan_tr069: str
            TR-069 VLAN id
        vlan_pppoe: str
            PPPoE VLAN id
        vlan_iptv_unicast: str
            IPTV Unicast VLAN id
        vlan_iptv_multicast: str
            IPTV Multicast VLAN id    
        """
        #try:

        onu = self.find_auth(sn)

        if(onu):
            cmd = """
            configure terminal
            interface gpon_onu-{}/{}/{}:{}
            tcont 1 profile {}
            tcont 2 profile {}
            gemport 1 tcont 1
            gemport 2 tcont 2
            gemport 3 tcont 1
            gemport 4 tcont 2
            exit
            pon-onu-mng gpon_onu-{}/{}/{}:{}
            service 3 gemport 3 vlan {}
            service 1 gemport 1 vlan {}
            service 4 gemport 4 vlan {}
            service 2 gemport 2 vlan {}
            mvlan {}
            vlan port eth_0/2 mode tag vlan {}
            exit
            interface vport-{}/{}/{}.{}:1
            service-port 1 user-vlan {} vlan {}
            exit
            interface vport-{}/{}/{}.{}:2
            service-port 2 user-vlan {} vlan {}
            igmp fast-leave enable
            exit
            interface vport-{}/{}/{}.{}:3
            service-port 3 user-vlan {} vlan {}
            exit
            interface vport-{}/{}/{}.{}:4
            service-port 4 user-vlan {} vlan {}
            exit
            igmp mvlan 2
            receive-port vport-{}/{}/{}.{}:2
            end
            """.format(
                onu["chassi"], #interface gpon_onu-{}/{}/{}:{}
                onu['slot'],
                onu['pon'],
                onu['onuid'],
                os.getenv('OLT_ZTE_WAN_PROFILE'), #tcont 1 profile {}
                os.getenv('OLT_ZTE_IPTV_PROFILE'), #tcont 2 profile {}
                onu["chassi"], #pon-onu-mng gpon_onu-{}/{}/{}:{}
                onu['slot'],
                onu['pon'],
                onu['onuid'],
                vlan_pppoe, #service 3 gemport 3 vlan {}
                vlan_tr069, #service 1 gemport 1 vlan {}
                vlan_iptv_unicast, #service 4 gemport 4 vlan {}
                vlan_iptv_multicast, #service 2 gemport 2 vlan {}
                vlan_iptv_multicast, # mvlan {}
                vlan_iptv_multicast, #vlan port eth_0/2 mode tag vlan {}
                onu["chassi"], # interface vport-{}/{}/{}.{}:1
                onu['slot'],
                onu['pon'],
                onu['onuid'],
                vlan_tr069, #service-port 1 user-vlan {} vlan {}
                vlan_tr069,  
                onu["chassi"], #interface vport-{}/{}/{}.{}:2
                onu['slot'],
                onu['pon'],
                onu['onuid'],
                vlan_iptv_multicast, # service-port 2 user-vlan {} vlan {}
                vlan_iptv_multicast,
                onu["chassi"], # interface vport-{}/{}/{}.{}:3
                onu['slot'],
                onu['pon'],
                onu['onuid'],
                vlan_pppoe, #service-port 3 user-vlan {} vlan {}
                vlan_pppoe,
                onu["chassi"], #interface vport-{}/{}/{}.{}:4
                onu['slot'],
                onu['pon'],
                onu['onuid'],
                vlan_iptv_unicast, #service-port 4 user-vlan {} vlan {}
                vlan_iptv_unicast,
                onu["chassi"], #receive-port vport-{}/{}/{}.{}:2
                onu['slot'],
                onu['pon'],
                onu['onuid'])
            #res = self.__ssh.send_command(cmd)
            onu["script"] = cmd   

            self.send_command('configure terminal')   
            self.send_command('interface gpon_onu-{}/{}/{}:{}'.format(onu["chassi"],
                onu['slot'],
                onu['pon'],
                onu['onuid']))
            self.send_command('tcont 1 profile {}'.format(os.getenv('OLT_ZTE_WAN_PROFILE')))
            self.send_command('tcont 2 profile {}'.format(os.getenv('OLT_ZTE_IPTV_PROFILE')))
            self.send_command('gemport 1 tcont 1')
            self.send_command('gemport 2 tcont 2')
            self.send_command('gemport 3 tcont 1')
            self.send_command('gemport 4 tcont 2')
            self.send_command('exit')
            self.send_command('pon-onu-mng gpon_onu-{}/{}/{}:{}'.format(onu["chassi"],
                onu['slot'],
                onu['pon'],
                onu['onuid']))
            self.send_command('service 3 gemport 3 vlan {}'.format(vlan_pppoe))
            self.send_command('service 1 gemport 1 vlan {}'.format(vlan_tr069))
            self.send_command('service 4 gemport 4 vlan {}'.format(vlan_iptv_unicast))
            self.send_command('service 2 gemport 2 vlan {}'.format(vlan_iptv_multicast))
            self.send_command('mvlan {}'.format(vlan_iptv_multicast))
            self.send_command('vlan port eth_0/2 mode tag vlan {}'.format(vlan_iptv_multicast))
            self.send_command('exit')
            self.send_command('interface vport-{}/{}/{}.{}:1'.format(onu["chassi"],
                onu['slot'],
                onu['pon'],
                onu['onuid']))
            self.send_command('service-port 1 user-vlan {} vlan {}'.format(vlan_tr069, vlan_tr069))
            self.send_command('exit')
            self.send_command('interface vport-{}/{}/{}.{}:2'.format(onu["chassi"],
                onu['slot'],
                onu['pon'],
                onu['onuid']))
            self.send_command('service-port 2 user-vlan {} vlan {}'.format(vlan_iptv_multicast, vlan_iptv_multicast))
            self.send_command('igmp fast-leave enable')
            self.send_command('exit')
            self.send_command('interface vport-{}/{}/{}.{}:3'.format(onu["chassi"],
                onu['slot'],
                onu['pon'],
                onu['onuid']))
            self.send_command('service-port 3 user-vlan {} vlan {}'.format(vlan_pppoe, vlan_pppoe))
            self.send_command('exit')
            self.send_command('interface vport-{}/{}/{}.{}:4'.format(onu["chassi"],
                onu['slot'],
                onu['pon'],
                onu['onuid']))
            self.send_command('service-port 4 user-vlan {} vlan {}'.format(vlan_iptv_unicast, vlan_iptv_unicast))
            self.send_command('exit')
            self.send_command('igmp mvlan {}'.format(vlan_iptv_multicast))
            self.send_command('receive-port vport-{}/{}/{}.{}:2'.format(onu["chassi"],
                onu['slot'],
                onu['pon'],
                onu['onuid']))
            self.send_command('end')
            return onu
        else:
            return "ONU not found"
        #except Exception as e:
        #    return "Error (config_onu): " + str(e)

    def show_config_onu(self, sn):
        """ Show ONU configuration

        Parameters
        ----------
        sn : str
            ONU serial number
        """
        try:

            onu = self.find_auth(sn)

            if(onu):
                cmd = "configure terminal\ninterface gpon_onu-{}/{}/{}:{}\nshow this\nexit\nexit".format(
                    onu["chassi"], onu['slot'], onu['pon'], onu['onuid'])
                res = self.__ssh.send_command(cmd)
                res = res.strip()

                # as configuracoes estao entre as tags !<xpon> e !</xpon> da string retornada
                interface = None
                a, b = res.find('!<xpon>'), res.find('!</xpon>')
                if(a != -1):
                    # recupera a lista entre as tags
                    s = res[a+7:b]
                    # retira espacos em branco da string e divide as linhas em um array
                    s = s.strip()
                    s = s.splitlines()
                    # retira os espacos de cada item
                    l = map(str.strip, s)
                    interface = list(l)

                #------- INICIO VLANS ---------#
                cmd = "configure terminal\npon-onu-mng gpon_onu-{}/{}/{}:{}\nshow this\nexit\nexit".format(
                    onu["chassi"], onu['slot'], onu['pon'], onu['onuid'])
                res = self.__ssh.send_command(cmd)

                # as configuracoes estao entre as tags !<xpon> e !</xpon> da string retornada
                pon_onu = None
                a, b = res.find('!<xpon>'), res.find('!</xpon>')

                if(a != -1):
                    # recupera a lista entre as tags
                    s = res[a+7:b]
                    # retira espacos em branco da string e divide as linhas em um array
                    s = s.strip()
                    s = s.splitlines()
                    # retira os espacos de cada item
                    l = map(str.strip, s)
                    pon_onu = list(l)
                
                #------INICIO VPORT --------#
                cmd = "configure terminal\ninterface vport-{}/{}/{}.{}:1\nshow this\nexit\nexit".format(
                    onu["chassi"], onu['slot'], onu['pon'], onu['onuid'])
                res = self.__ssh.send_command(cmd)

                # as configuracoes estao entre as tags !<MSAN> e !</MSAN> da string retornada
                vport1 = None
                a, b = res.find('!<MSAN>'), res.find('!</MSAN>')

                if(a != -1):
                    # recupera a lista entre as tags
                    s = res[a+7:b]
                    # retira espacos em branco da string e divide as linhas em um array
                    s = s.strip()
                    s = s.splitlines()
                    # retira os espacos de cada item
                    l = map(str.strip, s)
                    vport1 = list(l)

                
                cmd = "configure terminal\ninterface vport-{}/{}/{}.{}:2\nshow this\nexit\nexit".format(
                    onu["chassi"], onu['slot'], onu['pon'], onu['onuid'])
                res = self.__ssh.send_command(cmd)

                # as configuracoes estao entre as tags !<MSAN> e !</MSAN> da string retornada
                vport2 = None
                a, b = res.find('!<MSAN>'), res.find('!</MSAN>')

                if(a != -1):
                    # recupera a lista entre as tags
                    s = res[a+7:b]
                    # retira espacos em branco da string e divide as linhas em um array
                    s = s.strip()
                    s = s.splitlines()
                    # retira os espacos de cada item
                    l = map(str.strip, s)
                    vport2 = list(l)

                cmd = "configure terminal\ninterface vport-{}/{}/{}.{}:3\nshow this\nexit\nexit".format(
                    onu["chassi"], onu['slot'], onu['pon'], onu['onuid'])
                res = self.__ssh.send_command(cmd)

                # as configuracoes estao entre as tags !<MSAN> e !</MSAN> da string retornada
                vport3 = None
                a, b = res.find('!<MSAN>'), res.find('!</MSAN>')

                if(a != -1):
                    # recupera a lista entre as tags
                    s = res[a+7:b]
                    # retira espacos em branco da string e divide as linhas em um array
                    s = s.strip()
                    s = s.splitlines()
                    # retira os espacos de cada item
                    l = map(str.strip, s)
                    vport3 = list(l)

                
                cmd = "configure terminal\ninterface vport-{}/{}/{}.{}:4\nshow this\nexit\nexit".format(
                    onu["chassi"], onu['slot'], onu['pon'], onu['onuid'])
                res = self.__ssh.send_command(cmd)

                # as configuracoes estao entre as tags !<MSAN> e !</MSAN> da string retornada
                vport4 = None
                a, b = res.find('!<MSAN>'), res.find('!</MSAN>')

                if(a != -1):
                    # recupera a lista entre as tags
                    s = res[a+7:b]
                    # retira espacos em branco da string e divide as linhas em um array
                    s = s.strip()
                    s = s.splitlines()
                    # retira os espacos de cada item
                    l = map(str.strip, s)
                    vport4 = list(l)
                
                result = {
                    "interface": interface,
                    "pon_onu": pon_onu,
                    "vport1": vport1,
                    "vport2": vport2,
                    "vport3": vport3,
                    "vport4": vport4
                }

                return json.dumps(result, indent=4)
                

            else:
                return "ONU not found"
        except Exception as e:
            return "Error (show_config_onu): " + str(e)

    def show_signal(self, sn):
        """ Show ONU signal

          Parameters
          ----------
          sn : str
            ONU serial number

          Returns
          -------
          dict
            ONU signal

        """
        try:

            onu = self.find_auth(sn)

            if(onu):
                signal = {'up': None, 'down': None}
                cmd = "show pon power attenuation gpon_onu-{}/{}/{}:{}".format(
                    onu['chassi'], onu['slot'], onu['pon'], onu['onuid'])
                res = self.__ssh.send_command(cmd)
                time.sleep(0.5)

                if '---' in res:
                    res = res.splitlines()
                    index = next(i for i, w in enumerate(res) if '---' in w)
                    for data in res[index+1:]:
                        # normaliza a string deixando com apenas um espaco por valor
                        data = ' '.join(data.split())
                        # localiza o padrao dos itens
                        # Exemplos: up Rx :-22.456(dbm) Tx:2.283(dbm) 24.739(dB))
                        #           down Tx :5.330(dbm) Rx:-19.546(dbm) 24.876(dB)
                        p = re.compile('up Rx :(.*) Tx:(.*) (.*)')
                        up = p.search(data)
                        p = re.compile('down Tx :(.*) Rx:(.*) (.*)')
                        down = p.search(data)
                        if(up):
                            signal['up'] = {
                                'olt': up.group(1),
                                'onu': up.group(2),
                                'attenuation': up.group(2)
                            }
                        if(down):
                            signal['down'] = {
                                'olt': down.group(1),
                                'onu': down.group(2),
                                'attenuation': down.group(2)
                            }
                    return signal
            else:
                return "ONU not found"

        except Exception as e:
            print("Error (show_signal): "+str(e))

    def show_detail(self, sn):
        """ Show ONU detail

          Parameters
          ----------
          sn : str
            ONU serial number

          Returns
          -------
          dict
            ONU detail
        """
        try:
            onu = self.find_auth(sn)

            if(onu):
                detail = {}
                cmd = "show gpon onu detail-info gpon_onu-{}/{}/{}:{}".format(
                    onu['chassi'], onu['slot'], onu['pon'], onu['onuid'])
                res = self.__ssh.send_command(cmd)
                res = res.splitlines()
                index = next(i for i, w in enumerate(res) if 'ONU' in w)
                for data in res[index+1:]:
                    if '---' in data:
                        break
                   # normaliza a string deixando com apenas um espaco por valor
                    data = ' '.join(data.split())
                    temp = data.split(':')
                    if(len(temp) > 1):
                        detail[temp[0]] = temp[1].strip()

                return detail
            else:
                return "ONU not found"

        except Exception as e:
            print("Error (show_detail): "+str(e))

    def list_signal(self, chassi, slot, pon):
        """ Show OLT/ONU signal list

          Parameters
          ----------
          chassi : int
            Chassi board number
          slot : int
            Slot number
          pon : int
            Pon Number     

          Returns
          -------
          dict
            ONU signal list 
        """
        try:

            signals = {
                'olt-rx': [],
                'olt-tx': [],
                'onu-rx': [],
                'onu-tx': []
            }
            for key in signals.keys():
                # o retorno de olt-tx segue um padrao diferente dos sinais
                if(key == 'olt-tx'):
                    cmd = "show pon power olt-tx gpon_olt-{}/{}/{}".format(
                        chassi, slot, pon)
                    res = self.__ssh.send_command(cmd)
                    time.sleep(0.5)
                    if '---' in res:
                        res = res.splitlines()
                        # localiza a linha pontilhada que separa o resultado da pesquisa
                        index = next(
                            i for i, w in enumerate(res) if '---' in w)
                        for data in res[index+1:]:
                            # normaliza a string deixando com apenas um espaco por valor
                            data = ' '.join(data.split())
                            if(' ' in data):
                                channel = data.split(' ')[0]
                                signal = data.split(' ')[1]

                                if('(dbm)' in signal):
                                    signal = signal.replace('(dbm)', '')
                                signals['olt-tx'].append({channel: signal})
                else:
                    cmd = "show pon power {} gpon_olt-{}/{}/{}".format(
                        key, chassi, slot, pon)
                    res = self.__ssh.send_command(cmd)
                    time.sleep(0.5)
                    if '---' in res:
                        res = res.splitlines()
                        # localiza a linha pontilhada que separa o resultado da pesquisa
                        index = next(
                            i for i, w in enumerate(res) if '---' in w)
                        for data in res[index+1:]:
                            # normaliza a string deixando com apenas um espaco por valor
                            data = ' '.join(data.split())
                            if('/' in data):
                                chassi = data.split('/')[0].split('-')[1]
                                slot = data.split('/')[1]
                                pon = data.split('/')[2].split(':')[0]
                                onuid = data.split(
                                    '/')[2].split(':')[1].split(' ')[0]
                                if(' ' in data):
                                    temp = data.split(' ')
                                    temp.pop(0)
                                    signal = ' '.join(temp)
                                    if('(dbm)' in signal):
                                        signal = signal.replace('(dbm)', '')
                                    signals[key].append({int(onuid): signal})

            return signals

        except Exception as e:
            print("Error (list_signal): "+str(e))

    def show_eth_interface(self, sn):
        """ Show ONU eth interface detail

          Parameters
          ----------
          sn : str
            ONU serial number

          Returns
          -------
          dict
            ONU eth interface
        """
        try:
            onu = self.find_auth(sn)

            if(onu):
                interfaces = []
                for i in range(4):
                    cmd = "show gpon remote-onu interface eth gpon_onu-{}/{}/{}:{} eth_0/{}".format(
                        onu['chassi'], onu['slot'], onu['pon'], onu['onuid'], i+1)
                    res = self.__ssh.send_command(cmd)
                    time.sleep(0.5)

                    res = res.splitlines()
                    index = next(i for i, w in enumerate(
                        res) if 'Interface' in w)
                    eth = {}
                    for data in res[index:]:
                        # normaliza a string deixando com apenas um espaco por valor
                        data = ' '.join(data.split())
                        # divide apenas a primeira ocorrencia de ":"
                        temp = data.split(':', 1)
                        if(len(temp) > 1):
                            eth[temp[0].rstrip().replace(" ", "_")] = temp[1].strip()
                    interfaces.append(eth)
                return interfaces
            else:
                return "ONU not found"

        except Exception as e:
            print("Error (show_detail): "+str(e))

    def show_equip_info(self, sn):
        """ This command displays the ONU device information, 
          including administrative status, standby battery status, 
          security, device ID, OMCC version, start time, and system uptime. 

          Parameters
          ----------
          sn : str
            ONU serial number

          Returns
          -------
          dict
            ONU information
        """
        try:

            onu = self.find_auth(sn)

            if(onu):
                cmd = "show gpon remote-onu wan gpon_onu-{}/{}/{}:{}".format(
                    onu['chassi'], onu['slot'],  onu['pon'], onu['onuid'])
                res = self.__ssh.send_command(cmd)
                time.sleep(0.5)
                data = res
                data = ' '.join(data.split())

                return data

            else:
                return "ONU not found"

        except Exception as e:
            print("Error (show_equip_info): "+str(e))

    def show_gpon_interface(self, sn):
        """ This command displays the PON port properties.

          Parameters
          ----------
          sn : str
            ONU serial number

          Returns
          -------
          dict
            ONU information
        """
        try:

            onu = self.find_auth(sn)

            if(onu):
                cmd = "show gpon remote-onu interface pon gpon_onu-{}/{}/{}:{}".format(
                    onu['chassi'], onu['slot'], onu['pon'], onu['onuid'])

                res = self.__ssh.send_command(cmd)
                data = res
                data = ' '.join(data.split())
                response = {}
                match = re.search(r"Temperature: ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    response['temperature'] = match.group(1)+'(C)'

                match = re.search(
                    r"Power feed voltage: ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    response['voltage'] = match.group(1) + '(V)'

                match = re.search(
                    r"Laser bias current: ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    response['bias'] = match.group(1)+'mA'

                return response

            else:
                return "ONU not found"

        except Exception as e:
            print("Error (show_gpon_interface): "+str(e))

    def show_olt_information(self):
        """ This command displays the OLT information

          Returns
          -------
          dict
            OLT information
        """
        try:

                cmd = "show software"

                res = self.__ssh.send_command(cmd)
                response = {}
                p = re.compile('System uptime is (.*)')
                search = p.search(res)
                if(search):
                    response['uptime'] = search.group(1)

                p = re.compile('Version: (.*),')
                search = p.search(res)
                if(search):
                    response['software'] = search.group(1)

                res = res.splitlines()
                response['hardware'] = res[0]
                 
                return response
        except Exception as e:
            print("Error (show_olt_information): "+str(e))

    def show_olt_processors(self):
        """ This command displays the OLT processors

          Returns
          -------
          dict
            OLT processors
        """
        try:

                cmd = "show processor"

                res = self.__ssh.send_command(cmd)
                res = res.splitlines()
                processors = []
                for data in res:
                    data = ' '.join(data.split())
                    p  = re.compile('(.*) (.*) (.*)% (.*)% (.*)% (.*)% (.*) (.*) (.*)%')
                    search = p.search(data)
                    if(search):
                        processor = {
                            'name': search.group(1),
                            'character': search.group(2),
                            'cpu_5s': search.group(3) + '%',
                            'cpu_1m': search.group(4)+ '%',
                            'cpu_5m': search.group(5)+ '%',
                            'peak': search.group(6)+ '%',
                            'phymem': search.group(7),
                            'freemem': search.group(8),
                            'mem': search.group(9)+ '%'
                        }    
                        processors.append(processor)
                 
                return processors
        except Exception as e:
            print("Error (show_olt_processors): "+str(e))




    def list_olt_cards(self):
        """ This command displays the OLT cards information

          Returns
          -------
          dict
            OLT Cards
        """
        try:
                cmd = "show card"

                res = self.__ssh.send_command(cmd)
                cards = []
                if '---' in res:
                    res = res.splitlines()
                    index = next(i for i, w in enumerate(res) if '---' in w)
                    for data in res[index+1:]:
                        # normaliza a string deixando com apenas um espaco por valor
                        data = ' '.join(data.split())
                        p = re.compile('(.*) (.*) (.*) (.*) (.*) (.*) (.*)')
                        search = p.search(data)
                        if(search):
                            card = {
                                'shelf': search.group(1),
                                'slot': search.group(2),
                                'type': search.group(3),
                                'name': search.group(4),
                                'port': search.group(5),
                                'version': search.group(6),
                                'status': search.group(7)
                            }
                            cards.append(card)
                    return cards       

        except Exception as e:
            print("Error (list_olt_cards): "+str(e))        