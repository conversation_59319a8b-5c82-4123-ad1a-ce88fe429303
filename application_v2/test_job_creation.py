#!/usr/bin/env python3
"""
Teste para verificar se os jobs estão sendo criados com as dependências corretas
"""

import os
import sys
import uuid
from unittest.mock import Mock, patch
from dotenv import load_dotenv

# Adicionar o diretório atual ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

load_dotenv()

def test_job_creation_with_dependencies():
    """
    Testa se os jobs estão sendo criados com as dependências corretas
    """
    
    print("=== Teste de Criação de Jobs com Dependências ===")
    
    # Mock das dependências para evitar problemas de conexão
    with patch('tasks.conn') as mock_conn, \
         patch('tasks.Queue') as mock_queue_class, \
         patch('tasks.update_job') as mock_update_job, \
         patch('tasks.update_provision') as mock_update_provision, \
         patch('tasks.getvlans') as mock_getvlans, \
         patch('tasks.get_provision_tasks') as mock_get_tasks:
        
        # Configurar mocks
        mock_queue = Mock()
        mock_queue_class.return_value = mock_queue
        mock_update_provision.return_value = "test-provision-id"
        mock_getvlans.return_value = {'vlan_pppoe': 100, 'vlan_iptv': 200}
        
        # Simular tarefas com dependências
        mock_tasks = [
            {
                'id': 1,
                'task': 'Provisionamento na OLT',
                'depends_on': None,
                'retries': 3,
                'interval': 60,
                'order': 1,
                'precondition': None
            },
            {
                'id': 2,
                'task': 'Atualiza dados no banco',
                'depends_on': 'Provisionamento na OLT',
                'retries': 3,
                'interval': 60,
                'order': 2,
                'precondition': None
            },
            {
                'id': 3,
                'task': 'Atualiza dados técnicos no IXC',
                'depends_on': 'Provisionamento na OLT',
                'retries': 3,
                'interval': 60,
                'order': 3,
                'precondition': None
            }
        ]
        mock_get_tasks.return_value = mock_tasks
        
        # Simular jobs criados
        job_calls = []
        
        def mock_enqueue(*args, **kwargs):
            job_id = str(uuid.uuid4())
            job_mock = Mock()
            job_mock.get_id.return_value = job_id
            
            # Capturar informações sobre a chamada
            call_info = {
                'job_id': job_id,
                'args': args,
                'kwargs': kwargs,
                'depends_on': kwargs.get('depends_on'),
                'depends_on_type': type(kwargs.get('depends_on')).__name__ if kwargs.get('depends_on') else None
            }
            job_calls.append(call_info)
            
            return job_mock
        
        mock_queue.enqueue = mock_enqueue
        
        # Item de teste
        test_item = {
            'serial': 'TEST123456',
            'username': 'test_user',
            'olt_ip': '*************',
            'olt': 'OLT_TEST',
            'olt_model': 'ZTE',
            'placa': 1,
            'porta': 1,
            'modelo': 'F601',
            'source': 'test'
        }
        
        try:
            # Importar e executar a função
            from tasks import create_provision
            
            print(f"Executando create_provision com item: {test_item}")
            jobs = create_provision(test_item)
            
            print(f"\nJobs criados: {len(jobs)}")
            
            # Analisar as chamadas
            print(f"\nAnálise das chamadas de enqueue:")
            for i, call in enumerate(job_calls):
                print(f"\nJob {i+1}:")
                print(f"  ID: {call['job_id']}")
                print(f"  Função: {call['args'][0].__name__ if call['args'] else 'N/A'}")
                print(f"  Depends_on: {call['depends_on']}")
                print(f"  Tipo do depends_on: {call['depends_on_type']}")
                
                if call['depends_on']:
                    if hasattr(call['depends_on'], 'get_id'):
                        print(f"  ID da dependência: {call['depends_on'].get_id()}")
                    else:
                        print(f"  ❌ ERRO: depends_on não é um objeto Job válido!")
            
            # Verificar se as dependências estão corretas
            print(f"\n=== Verificação de Dependências ===")
            
            # Job 1 (Provisionamento) não deve ter dependência
            if job_calls[0]['depends_on'] is None:
                print("✅ Job 1 (Provisionamento): Sem dependência - CORRETO")
            else:
                print("❌ Job 1 (Provisionamento): Tem dependência - INCORRETO")
            
            # Job 2 (Atualiza dados) deve depender do Job 1
            if (job_calls[1]['depends_on'] is not None and 
                hasattr(job_calls[1]['depends_on'], 'get_id') and
                job_calls[1]['depends_on'].get_id() == job_calls[0]['job_id']):
                print("✅ Job 2 (Atualiza dados): Depende do Job 1 - CORRETO")
            else:
                print("❌ Job 2 (Atualiza dados): Dependência incorreta")
            
            # Job 3 (Atualiza dados técnicos) deve depender do Job 1
            if (job_calls[2]['depends_on'] is not None and 
                hasattr(job_calls[2]['depends_on'], 'get_id') and
                job_calls[2]['depends_on'].get_id() == job_calls[0]['job_id']):
                print("✅ Job 3 (Atualiza dados técnicos): Depende do Job 1 - CORRETO")
            else:
                print("❌ Job 3 (Atualiza dados técnicos): Dependência incorreta")
            
            print(f"\n✅ Teste concluído com sucesso!")
            return True
            
        except Exception as e:
            print(f"\n❌ Erro durante o teste: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_job_creation_with_dependencies()
    sys.exit(0 if success else 1)
