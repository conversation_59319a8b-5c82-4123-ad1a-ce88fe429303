import pexpect
import select
from database import *
from tl1 import *
from log import *
from ixc_ws import *
import time
import sys
from datetime import datetime as dt
import datetime
import traceback
from config import *
from genieacs import *
import urllib3
from fhmanager import FHManager
#from ztemanager import ZTEManager

def veip_addvlans(onu, vlans):
  if('onu_id' in onu):
    #onu = findauthorizedonu(serial)
    child = pexpect.spawn('telnet {}'.format(onu['olt_ip']), encoding='utf-8')
    
    # Habilita o debug (retorno na tela)
    child.logfile = sys.stdout
    child.expect('Login: ', 5)
    child.sendline(CLI_USER)
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('User> ', 5)
    child.sendline("enable")
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('Admin# ', 5)
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', 5)
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if(rp1000):
        child.sendline('cd onu/lan')
        child.expect('lan#', 5)
        expectstr = 'lan#'
    else:
        child.sendline('cd epononu')
        child.expect('epononu# ', 5)
        child.sendline('cd qinq')
        child.expect('qinq#', 5)
        expectstr = 'qinq#'
    #child.sendline('cd epononu')
    #child.expect('epononu# ', 5)
    #child.sendline('cd qinq')
    #child.expect('qinq#', 5)

    for vlan in vlans:
        child.sendline(
            'set epon slot {} pon {} onu {} port 1 onuveip {} 33024 {} 65535 33024 65535 65535 33024 65535 65535 0 {} 65535'.format(onu['placa'], onu['porta'], onu['onu_id'], vlan["serviceid"], vlan["vlanid"], vlan["profileid"]))
        # child.expect('qinq#', 5)
        child.expect(expectstr, 5)

    child.close()

#adiciona vlans no veip
def add_veip(item, check):
  try:
    vlans = []
    vlans.append({
        "vlanid": item["vlan_pppoe"],
        "serviceid": 2,
        "profileid": TR069_PROFILEID
    })
    vlans.append({
        "vlanid": TR069_DHCP_VLANID,
        "serviceid": 1,
        "profileid": TR069_PROFILEID
    })

    if('onu_id' in item):
      veip_addvlans(item, vlans)
    
    item['vlan_dhcp'] = TR069_DHCP_VLANID
    item["vlan_iptv"] = None

    #se estiver checando a configuracao, incrementa o contador de tentativas e a ultima checagem
    if(check):
      item["retries"] = int(item["retries"]) + 1
      item["last_retry"] = dt.now()
      updatelog(item)
      print("check veip...retry")   
    else:
      #caso contrario, grava apenas o log
      msg = "Adicionando Vlans VEIP [DHCP id:{} - PPPoE id: {}]".format(item['vlan_dhcp'], item["vlan_pppoe"])

      print(msg)
  except Exception as e:
    errorlog('Falha em add_veip: '+ str(e))    
  
def add_vlans(item):
  try:
    #remove todas as vlans
    onuremovevlans(item, 4)
    msg = "Removendo Vlans existentes"
    gravalog(item, 'REMOVE_VLANS', msg, 0)
    print(msg)
    
    #configura vlan pppoe na porta 1
    onuaddvlanunicast(item, 1, item["vlan_pppoe"])  
    msg = "Configurando VLAN PPPoE na porta 1 [id: {}]".format(item["vlan_pppoe"])
    item["porta_onu"] = 1
    vlan_iptv = item["vlan_iptv"]
    item["vlan_iptv"] = None
    item["vlan_dhcp"] = None
    gravalog(item, 'VLANS', msg, 0)
    print(msg)

    #configura vlan unicast iptv na porta 2
    item["vlan_iptv"] = vlan_iptv
    onuaddvlanunicast(item, 2, item["vlan_iptv"])  
    msg = "Configurando VLAN Unicast IPTV na porta 2 [id: {}]".format(item["vlan_iptv"])
    item["vlan_pppoe"] = None
    item["vlan_dhcp"] = None
    item["porta_onu"] = 2

    print(msg)
  except Exception as e:
    errorlog('Falha em add_vlans: '+ str(e))  

def add_multicast(item):
  try:  
    #configura vlan multicast na porta 2  
    onuaddvlanmulticast(item, 2, 2)  
    msg = "Configurando VLAN Multicast na porta 2 [id: 2]"
    item["porta_onu"] = 2
    item["vlan_iptv"] = 2
    item["vlan_pppoe"] = None
    item["vlan_dhcp"] = None

    gravalog(item, 'MULTICAST', msg, 0)
  except Exception as e:
    errorlog('Falha em add_multicast: '+ str(e))  

onus = get_onus('***********')

for onu in onus:
    if('ZTE' in onu['serial'].upper()):
        if('onuid' in onu):
            # Se for compativel com tr069 adiciona as vlans no veip (dhcp e pppoe)
            vlans = getvlans(onu['olt_ip'], onu['placa'], onu['porta'])
            onu['vlan_pppoe'] = int(vlans['vlan_pppoe'])
            onu['vlan_iptv'] = int(vlans['vlan_iptv'])


            if('onuid' in onu):
                onu["onu_id"] = onu["onuid"]
            
                print(add_veip(onu, False))  



'''
item = {
    'serial': 'ZTEGC4ACC558',
    'olt_ip': '***********',
    'placa': '11',
    'porta': '16'
}
'''

'''
onu = findauthorizedonu(item["serial"])
if(onu):
    if('onuid' in onu):
        # Se for compativel com tr069 adiciona as vlans no veip (dhcp e pppoe)
        vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])
        onu['vlan_pppoe'] = int(vlans['vlan_pppoe'])
        onu['vlan_iptv'] = int(vlans['vlan_iptv'])


        if('onuid' in onu):
          onu["onu_id"] = onu["onuid"]
          item["onu_id"] = onu["onuid"]
        item['vlan_pppoe'] = int(vlans['vlan_pppoe'])
        item['vlan_iptv'] = int(vlans['vlan_iptv'])

        print(add_veip(onu, False))  
'''


