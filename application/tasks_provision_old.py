import requests
import os
from rq import Queue, Retry
from rq.job import Job
from worker_provision_queue import conn
import json
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
from database import *
from tasks_fiberhome import *
from tasks_ixc import *
from tasks_genieacs import *
from rq.serializers import JSONSerializer
import uuid
from pytz import timezone

load_dotenv()

q = Queue('provision_queue', connection=conn)

def job_to_json(job):
    job_result = Job.fetch(job.get_id(), connection=conn)
    job_json = {
        'id' : job_result.get_id(),
        'enqueued_at' : job_result.enqueued_at,
        'status' : job_result.get_status(refresh=True),
        'started_at' : job_result.started_at,
        'ended_at' : job_result.ended_at,
        'exc_info' : job_result.exc_info,
        'worker_name' : job_result.worker_name,
        'last_heartbeat' : job_result.last_heartbeat,
        'origin': job_result.origin,
        'func_name' : job_result.func_name,
        'kwargs': json.dumps(job_result.kwargs, indent=4, sort_keys=True, default=str)
    }
    return job_json

def task_provision(item):

    current_provision= {
        'id' : str(uuid.uuid4()), 
        'serial': item['serial'],
        'username': item['username'],
        'olt_ip': item["olt_ip"],
        'olt': item["olt"],
        'olt_model': item["olt_model"],
        'slot': item['placa'],
        'pon': item['porta'],
        'model': item['modelo'],
        'source': item['source'],
        'status': 'queued' #queued, started, deferred, finished, stopped, scheduled, canceled, failed
    }

    provision_id = update_provision(current_provision)
    item['provision_id'] = provision_id

    jobs = []

    job_id = str(uuid.uuid4())
    job_deauthorize = q.enqueue(task_deauthorize, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id)
    job = job_to_json(job_deauthorize)
    job['description'] = 'DEAUTHORIZE'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_deauthorize.get_id())

    job_id = str(uuid.uuid4())
    job_authorization = q.enqueue(task_authorization, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id, depends_on=job_deauthorize)
    #job_authorization = q.enqueue(task_authorization, args=([job_id]), kwargs=(item), retry=Retry(max=5), job_id=job_id)
    job = job_to_json(job_authorization)
    job['description'] = 'AUTHORIZATION'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_authorization.get_id())

    job_id = str(uuid.uuid4())
    job_dadostecnicos = q.enqueue(task_updateixc, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id, depends_on=job_authorization)
    job = job_to_json(job_dadostecnicos)
    job['description'] = 'DADOSTECNICOS'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_dadostecnicos.get_id())
    
    #consulta as vlans
    vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])

    # Se for compativel com tr069 adiciona as vlans no veip (dhcp e pppoe)
    if('ZTE' in item['serial'].upper()):

        #remove do genieacs
        if 'mac' in item:
            job_id = str(uuid.uuid4())
            job_removeacs = q.enqueue(task_removeacs, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id, depends_on=job_authorization)
            job = job_to_json(job_removeacs)
            job['description'] = 'REMOVEACS'
            job['provision_id'] = provision_id
            update_job(job)
            jobs.append(job_removeacs.get_id())

        item['vlan'] = int(vlans['vlan_pppoe'])
        item["serviceid"] = 2

        #adiciona vlan pppoe no veip
        job_id = str(uuid.uuid4())
        job_veip_pppoe = q.enqueue(task_addveip, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_veip_pppoe)
        job['description'] = 'VEIP_PPPOE'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_veip_pppoe.get_id())      

        #verifica vlan pppoe no veip
        job_id = str(uuid.uuid4())
        job_check_veip_pppoe = q.enqueue(task_checkveip, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_veip_pppoe, job_id=job_id)
        job = job_to_json(job_check_veip_pppoe)
        job['description'] = 'CHECK_VEIP_PPPOE'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_check_veip_pppoe.get_id())      
                        
        item['vlan'] = os.getenv('TR069_DHCP_VLANID')
        item["serviceid"] = 1

        #adiciona vlan dhcp no veip
        job_id = str(uuid.uuid4())
        job_veip_dhcp = q.enqueue(task_addveip, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_veip_dhcp)
        job['description'] = 'VEIP_DHCP'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_veip_dhcp.get_id())    

        #verifica vlan dhcp no veip
        job_id = str(uuid.uuid4())
        job_check_veip_dhcp = q.enqueue(task_checkveip, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_veip_dhcp, job_id=job_id)
        job = job_to_json(job_check_veip_dhcp)
        job['description'] = 'CHECK_VEIP_DHCP'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_check_veip_dhcp.get_id())      

        item['vlan'] = 2
        item['vlanport'] = 2
        item['vlantype'] = 'multicast'


        #adiciona vlan iptv multicast na porta 2
        job_id = str(uuid.uuid4())
        job_vlan_multicast = q.enqueue(task_addvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_vlan_multicast)
        job['description'] = 'VLAN_MULTICAST'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_vlan_multicast.get_id())         

        #verifica vlan multicast
        job_id = str(uuid.uuid4())
        job_check_vlan_multicast = q.enqueue(task_checkvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_vlan_multicast, job_id=job_id)
        job = job_to_json(job_check_vlan_multicast)
        job['description'] = 'CHECK_VLAN_MULTICAST'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_check_vlan_multicast.get_id())    
        

    else:
        #remove as vlans
        job_id = str(uuid.uuid4())
        job_removevlans = q.enqueue(task_removevlans, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_removevlans)
        job['description'] = 'REMOVE_VLANS'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_removevlans.get_id())         
                        
        item['vlan'] = int(vlans['vlan_pppoe'])
        item['vlanport'] = 1
        item['vlantype'] = 'unicast'

        #adiciona vlan pppoe na porta 1
        job_id = str(uuid.uuid4())
        job_vlan_pppoe = q.enqueue(task_addvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_removevlans, job_id=job_id)
        job = job_to_json(job_vlan_pppoe)
        job['description'] = 'VLAN_PPPOE'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_vlan_pppoe.get_id()) 

        #verifica vlan pppoe na porta 1
        job_id = str(uuid.uuid4())
        job_check_vlan_pppoe = q.enqueue(task_checkvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_vlan_pppoe, job_id=job_id)
        job = job_to_json(job_check_vlan_pppoe)
        job['description'] = 'CHECK_VLAN_PPPOE'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_check_vlan_pppoe.get_id())         
                        
        item['vlan'] = int(vlans['vlan_iptv'])
        item['vlanport'] = 2
        item['vlantype'] = 'unicast'

        #adiciona vlan iptv unicast na porta 2
        job_id = str(uuid.uuid4())
        job_vlan_iptv = q.enqueue(task_addvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_removevlans, job_id=job_id)
        job = job_to_json(job_vlan_iptv)
        job['description'] = 'VLAN_IPTV'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_vlan_iptv.get_id())    

        #verifica vlan iptv na porta 2
        job_id = str(uuid.uuid4())
        job_check_vlan_iptv = q.enqueue(task_checkvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_vlan_iptv, job_id=job_id)
        job = job_to_json(job_check_vlan_iptv)
        job['description'] = 'CHECK_VLAN_IPTV'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_check_vlan_iptv.get_id())      

        item['vlan'] = 2
        item['vlanport'] = 2
        item['vlantype'] = 'multicast'
        
        #adiciona vlan iptv multicast na porta 2
        job_id = str(uuid.uuid4())
        job_vlan_multicast = q.enqueue(task_addvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_removevlans, job_id=job_id)
        job = job_to_json(job_vlan_multicast)
        job['description'] = 'VLAN_MULTICAST'
        job['provision_id'] = provision_id
        update_job(job)

        #verifica vlan multicast
        job_id = str(uuid.uuid4())
        job_check_vlan_multicast = q.enqueue(task_checkvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), depends_on=job_vlan_multicast, job_id=job_id)
        job = job_to_json(job_check_vlan_multicast)
        job['description'] = 'CHECK_VLAN_MULTICAST'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_check_vlan_multicast.get_id())

    jobs.append(job_vlan_multicast.get_id())         
    current_provision['started_at'] = datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
    update_provision(current_provision)

    return jobs

def plan_rule(model, plan, username):

    try:
        '''
        Regras ZTE				
        ==========
        Cliente	    Plano	    ONU Nova	ONU Antiga	Acao
        Novo*	    50	        F660	    null	    Provisionar
        Antigo**	50	        F660	    F660	    Provisionar
        Todos	    >50 & <=100	F660	    Todos	    Provisionar
        Todos	    >=100	    F670	    Todos	    Provisionar
        
        * uma alocacao
        ** mais de uma alocacao
        '''
        valid = None
        f660 = '660' in model
        f670 = '670' in model

        if((not f660 and not f670) or (int(plan) > 50 and int(plan) <= 100 and f660) or (int(plan) >= 100 and f670)):
            return None    

        if (int(plan) > 100 and f660):
            valid = 'F670'

        if valid == None:
            onus = get_onus_alocadas(username)
            #Novo
            if(len(onus) <= 1):
                if(f670):
                    valid = 'F660'
            #Antigo
            else:
                anterior = onus[0]['descricao']
                if(f670):
                    if '660' in anterior:
                        valid = 'F660'
                if(f660):
                    if '670' in anterior:
                        valid = 'F670'
                    elif not '660' in anterior:    
                        valid = 'FIBERHOME'

        return valid
    except:
        return False    

#verifica se a onu possue todos os dados para iniciar o provisionamento
def task_checkonu(onu):
  try:
    missing_params = []

    # onu nao pode estar na lista para ignorar
    #if(checkignorelist(serial=onu['serial'])):
    #    missing_params.append('IGNORE')

    # obrigatorio patrimonio
    if(not ('patrimonio' in onu and onu['patrimonio'])):
        missing_params.append('PATRIMONIO')

    # Libera apenas se tiver a alocacao do comodato e nao esteja na tabela ignore com o parametro comodato=1
    if(not('id_comodato' in onu and onu['id_comodato']) and not(checkignorelist(serial=onu['serial'], comodato=1)) and not(checkignorelist(username=onu['username'], comodato=1))):
        missing_params.append('ALOCACAO COMODATO')

    # Libera apenas se tiver o modelo e nao esteja na tabela ignore com o parametro modelo=1
    if(not('modelo' in onu and onu['modelo'])and not(checkignorelist(serial=onu['serial'], modelo=1))):
        missing_params.append('MODELO')

    # obrigatorio username
    if(not('username' in onu and onu['username'])):
        missing_params.append('USERNAME')

    # obrigatorio mac
    if(not('mac' in onu and onu['mac'])):
        missing_params.append('MAC')

    # obrigatorio login ativo
    if(not('login_ativo' in onu and onu['login_ativo'] == 'S')):
        missing_params.append('LOGIN ATIVO')

    # obrigatorio pacote wifi para onus zte que nao estejam na lista para ignorar
    if('username' in onu and onu['username'] and 'ZTE' in onu['serial'].upper() and not(checkignorelist(serial=onu['serial'], pacote_wifi=1)) and not(checkignorelist(username=onu['username'], pacote_wifi=1)) and not(get_pacotewifi(onu['username']))):
        missing_params.append('PACOTE WIFI')

    

    # verifica se o modelo da onu e compativel com o plano.
    if('plano' in onu and 'modelo' in onu and 'username' in onu and onu['username'] and not(checkignorelist(serial=onu['serial'], plano=1)) and not(checkignorelist(username=onu['username'], plano=1))):
        pending = plan_rule(onu['modelo'], onu['plano'], onu['username'])
        if(pending):
            missing_params.append('ONU INCOMPATIVEL. NECESSARIO {}'.format(pending))

    return missing_params
    
  except Exception as e:
    raise
