#!/usr/bin/python
# -*- coding: utf-8 -*-

from dotenv import load_dotenv
import os
from ixc_ws import *

#atualiza dados tecnicos no ixc
def task_updateixc(*args, **kwargs):
    try:
        item = kwargs
        payload = get_dados_tecnicos(item['serial'], item['olt_ip'], item["placa"], item["porta"])
        if(payload):
            if('id' in payload and payload['id'] != None):
                if("mac" in payload):
                    mac = payload["mac"]
                    if re.match("[0-9a-f]{2}([-:]?)[0-9a-f]{2}(\\1[0-9a-f]{2}){4}$", mac.lower()):
                        response = update_dados_tecnicos(payload)
                        response = json.loads(response) 
                        if('type' in response):
                            if(response['type'] == 'error'):
                                raise Exception("Erro API IXC: {}".format(response["message"].encode('utf-8'))) 
                        else:
                            raise Exception("Erro API IXC")     
                    else:
                        raise Exception("MAC invalido") 
                else:
                    raise Exception("MAC nao informado") 
            else:
                raise Exception("Dados tecnicos nao localizados")         
        else:
            raise Exception("Dados tecnicos nao localizados")                 
    except Exception as e:
        raise