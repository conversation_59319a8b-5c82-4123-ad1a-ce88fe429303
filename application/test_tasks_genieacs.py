#!/usr/bin/python
# -*- coding: utf-8 -*-
import requests
import base64
import json
import os
import urllib3
from database import *
from dotenv import load_dotenv
import time
import datetime
import re
from genieclient import GenieClient


def task_removeacs(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs
        client = GenieClient()
        client.removeacs(item['mac'])
        update_job({
                                'id': args[0], 
                                'status' :'finished', 
                                'exc_info': '',
                                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })         
        return True

    except Exception as e:
        update_job({
            'id': args[0], 
            'status' :'failed', 
            'exc_info': str(e),
            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })        
        raise

def task_changevlanpppoe(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs
        client = GenieClient()
        client.changepppoevlanid(item['mac'], item['vlan_pppoe'])

        update_job({
                                'id': args[0], 
                                'status' :'finished',
                                'exc_info': '', 
                                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })         
        return True

    except Exception as e:
        update_job({
            'id': args[0], 
            'status' :'failed', 
            'exc_info': str(e),
            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })        
        raise

def task_reprovision(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs
        if('mac' in item):
            client = GenieClient()
            client.removetag(item['mac'], 'Provisioned')
            client.removetag(item['mac'], 'PPPoE')
            client.addtag(item['mac'], 'Provisioning')
            client.changeinformperiod(item['mac'], 10)

        update_job({
                                'id': args[0], 
                                'status' :'finished', 
                                'exc_info': '',
                                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })         
        return True

    except Exception as e:
        update_job({
            'id': args[0], 
            'status' :'failed', 
            'exc_info': str(e),
            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })        
        raise

def task_reboot(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs
        if('mac' in item):
            client = GenieClient()
            client.reboot(item['mac'])

        update_job({
                                'id': args[0], 
                                'status' :'finished',
                                'exc_info': '', 
                                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })         
        return True

    except Exception as e:
        update_job({
            'id': args[0], 
            'status' :'failed', 
            'exc_info': str(e),
            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })        
        raise

def task_registered(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs
        if('mac' in item):
            client = GenieClient()
            onu = client.registered(item['mac'])
            if not onu:
                raise Exception("ONU não encontrada no GenieACS") 

        update_job({
                                'id': args[0], 
                                'status' :'finished',
                                'exc_info': '', 
                                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })         
        return True

    except Exception as e:
        update_job({
            'id': args[0], 
            'status' :'failed', 
            'exc_info': str(e),
            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })        
        raise
        