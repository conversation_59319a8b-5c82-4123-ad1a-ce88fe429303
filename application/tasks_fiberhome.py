#!/usr/bin/python
# -*- coding: utf-8 -*-

from fhmanager import FHManager
from database import *
from dotenv import load_dotenv
import os
from cli import *
from tl1 import *
from ixc_ws import *
import time
import datetime

load_dotenv()

#consulta sinal na olt
def task_getsignal(*args, **kwargs):
    item = kwargs
    manager = FHManager(item['olt'])
    manager.connect()
    onus = onu_list(olt_ip=item['olt'], placa=item['placa'], porta=item['porta'])
    for onu in onus:
        signal = manager.get_signal(
            item['placa'], item['porta'], onu['onuid'], onu['serial'], onu['nome'])
        insert_signal(signal)
    manager.disconnect()

#adiciona vlan no veip
def task_addveip(*args, **kwargs):
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })        
        check = checkwhitelist(item)
        if(check):
            item['onuid'] = check['onuid']
        else:
            update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': 'ONU nao localizada na OLT', 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })        
            raise Exception("ONU não localizada na OLT")

        veip_addvlan(item, item["serviceid"], item["vlan"], os.getenv('TR069_PROFILEID'))

        update_job({
                'id': args[0], 
                'status' :'finished', 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })        
        return True              
        
    except Exception as e:
        update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': str(e), 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })        
        raise Exception("Erro ao adicionar a vlan {} no veip: {}".format(item["vlan"], str(e)))        

#adiciona vlan no veip
def task_checkveip(*args, **kwargs):
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })     

        check = checkwhitelist(item)
        if(check):
            item['onuid'] = check['onuid']
        else:
            update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': 'ONU nao localizada na OLT', 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })        
            raise Exception("ONU não localizada na OLT") 

        #verifica se a vlan foi adicionada no veip
        check = checkveip(item, item["vlan"])
        if(check):
            update_job({
                'id': args[0], 
                'status' :'finished', 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })          
            return True
        else:
            update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': "Erro ao verificar a vlan {} no veip".format(item["vlan"]), 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })   
            raise Exception("Erro ao verificar a vlan {} no veip".format(item["vlan"]))  
    except Exception as e:
        update_job({
                'id': args[0], 
                'status' :'failed', 
                #'exc_info': "Erro ao verificar a vlan {} no veip".format(item["vlan"]), 
                'exc_info': str(e), 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })   
        raise Exception("Erro ao verificar a vlan {} no veip".format(item["vlan"]))  

def checkvlan(onu, porta, vlan):
    try:
        exists = False
        onu_vlans = onuvlans(onu,porta)
        if(onu_vlans):
            for item in onu_vlans:
                if(int(item['CVLAN']) == int(vlan)):
                    exists = True
        return exists
    except Exception as e:
        return False

def checkveip(onu, vlan):
    try:
        exists = False
        veip_vlans = check_veip_vlans(onu)
        if(int(vlan) in veip_vlans):
            exists = True
        return exists
    except Exception as e:
        return False    

def task_checkauthorization(*args, **kwargs):
    item = kwargs

    auth = checkwhitelist(item)
    if checkwhitelist(item):
        return True
    else:
        return False

#remove vlans
def task_removevlans(*args, **kwargs):
    
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        onuremovevlans(item)
        update_job({
            'id': args[0], 
            'status' :'finished', 
            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        update_job({
            'id': args[0], 
            'status' :'failed', 
            'exc_info': str(e),
            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        raise Exception("Erro ao remover as vlans: {}".format(str(e)))   

def task_addvlan(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs
        time.sleep(0.2)

        if(item['vlantype'] == 'unicast'):
            onuaddvlanunicast(item, item['vlanport'], item['vlan'])
        elif(item['vlantype'] == 'multicast'):
            onuaddvlanmulticast(item, item['vlanport'], item['vlan'])  
        
        update_job({
                'id': args[0], 
                'status' :'finished', 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })        
        return True              

    except Exception as e:
        update_job({
            'id': args[0], 
            'status' :'failed', 
            'exc_info': str(e),
            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })       
        raise Exception("Erro ao adicionar a vlan {} na porta {}: {}".format(item['vlan'], item['vlanport'], str(e)))        

def task_checkvlan(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs

        #verifica se a vlan foi adicionada
        check = checkvlan(item, item['vlanport'], item['vlan'])
        if(check):
            update_job({
                'id': args[0], 
                'status' :'finished', 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })        
            return True
        else:
            update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': "Erro ao verificar a vlan {} na porta {}".format(item['vlan'], item['vlanport']),
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })
            raise Exception("Erro ao verificar a vlan {} na porta {}".format(item['vlan'], item['vlanport']))    
    except Exception as e:
        update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': "Erro ao verificar a vlan {} na porta {}".format(item['vlan'], item['vlanport']),
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })
        raise Exception("Erro ao verificar a vlan {} na porta {}".format(item['vlan'], item['vlanport']))    

def task_authorization(*args, **kwargs):

    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs

        #verifica se a onu ja esta autorizada
        check = task_checkauthorization(**item)
        if(check):
            update_job({
                'id': args[0], 
                'status' :'finished', 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })         
            return True

        if('ZTE' in item['serial'].upper()):
            item['modelo'] = 'GPON CURRENCY SFU'
        else:
            patrimonio = getpatrimonio_alocado(item['serial'])  
            if(patrimonio):
                item['modelo'] = patrimonio['modelo']

        response = onuauthorize(item)

        if(response):
            if('error' in response):
                if(response['error'] == True):
                    update_job({
                        'id': args[0], 
                        'status' :'failed', 
                        'exc_info': response["data"],
                        'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                    })       
                    raise Exception("Erro ao autorizar a onu em {}: {} {}".format(item["olt_ip"], response["data"], response["cmd"]))
                else:
                    update_job({
                        'id': args[0], 
                        'status' :'finished', 
                        'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                    })      
                    return True
            else:
                update_job({
                        'id': args[0], 
                        'status' :'failed', 
                        'exc_info': "Erro ao autorizar a onu em {}".format(item["olt_ip"]),
                        'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                })      
                raise Exception("Erro ao autorizar a onu em {}".format(item["olt_ip"]))
        else:
            update_job({
                        'id': args[0], 
                        'status' :'failed', 
                        'exc_info': response["data"],
                        'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                    })       
            raise Exception("Erro ao autorizar a onu em {}: {} ".format(item["olt_ip"], 'Sem resposta da OLT'))
    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })       
        raise Exception("Erro ao autorizar a onu em {}".format(item["olt_ip"]))


def task_deauthorize(*args, **kwargs):
    
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        # remove a onu de todas olts fiberhome
        olts = unm_oltlist()
    
        for olt in olts:
            removewhitelist(olt["ip"], item["serial"])

        # desautoriza todas as onus nomeadas com o mesmo username, exceto a nova
        provisions = findauthorizedonu(None, item["nome"])
        for provision in provisions:
            if(provision['serial'].upper() != item["serial"].upper()):
                removewhitelist(provision['olt_ip'], provision['serial'])
        
        update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro ao desautorizar: {}".format(str(e)))   

def task_test(*args, **kwargs):
    #return True
    #print(args)
    job_id = args[0]
    job={'id': job_id, 'status' :'finished'}
    print('jobid={}'.format(job_id))
    update_job(job)