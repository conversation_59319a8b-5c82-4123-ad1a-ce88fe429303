
from flask import request, jsonify, render_template, Blueprint
import json
from db import *
from cli import *
from rq import Queue, Retry
from rq.job import Job
from worker_config_queue import conn
from dotenv import load_dotenv
import os
from models import token_required
import uuid
from fhmanager import *

job_route = Blueprint('job_route', __name__, template_folder='templates')
q = Queue('config_queue', connection=conn) 

@job_route.route('/status/<job_id>', methods=['GET'])
@token_required
def job_status(job_id):
    job = q.fetch_job(job_id)

    return jsonify({
        'status': job.get_status(refresh=True)
    })
