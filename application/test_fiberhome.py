import requests
import os
from rq import Queue, Retry
from rq.job import Job
from worker_provision_queue import conn
import json
import time
from datetime import datetime
from dotenv import load_dotenv
from database import *
from tasks_fiberhome import *
from tasks_ixc import *
from tasks_genieacs import *
from rq.serializers import JSONSerializer
import uuid

load_dotenv()

#q = Queue('provision_queue', connection=conn, is_async=False) # executa direto sem necessidade de workers
q = Queue('provision_queue', connection=conn)

def job_to_json(job):
    job_result = Job.fetch(job.get_id(), connection=conn)
    job_json = {
        'id' : job_result.get_id(),
        'enqueued_at' : job_result.enqueued_at,
        'status' : job_result.get_status(refresh=True),
        'started_at' : job_result.started_at,
        'ended_at' : job_result.ended_at,
        'exc_info' : job_result.exc_info,
        'worker_name' : job_result.worker_name,
        'last_heartbeat' : job_result.last_heartbeat,
        'origin': job_result.origin,
        'func_name' : job_result.func_name,
        'kwargs': json.dumps(job_result.kwargs)
    }
    return job_json


def provision(item):

    current_provision= {
        'id' : str(uuid.uuid4()), #'7b1f5611-8873-496a-b4a5-f6b252263318', 
        'serial': item['serial'],
        'source': 'DISCOVERY',
        'status': 'queued' #queued, started, deferred, finished, stopped, scheduled, canceled, failed
    }

    provision_id = update_provision(current_provision)
    item['provision_id'] = provision_id

    jobs = []

    job_id = str(uuid.uuid4())
    job_deauthorize = q.enqueue(task_deauthorize, args=([job_id]), kwargs=(item), retry=Retry(max=5), job_id=job_id)
    job = job_to_json(job_deauthorize)
    job['description'] = 'DEAUTHORIZE'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_deauthorize.get_id())

    job_id = str(uuid.uuid4())
    job_authorization = q.enqueue(task_authorization, args=([job_id]), kwargs=(item), retry=Retry(max=5), job_id=job_id, depends_on=job_deauthorize)
    job = job_to_json(job_authorization)
    job['description'] = 'AUTHORIZATION'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_authorization.get_id())

    job_id = str(uuid.uuid4())
    job_dadostecnicos = q.enqueue(task_updateixc, args=([job_id]), kwargs=(item), retry=Retry(max=5), job_id=job_id, depends_on=job_authorization)
    job = job_to_json(job_dadostecnicos)
    job['description'] = 'DADOSTECNICOS'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_dadostecnicos.get_id())
    
    #consulta as vlans
    vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])

    # Se for compativel com tr069 adiciona as vlans no veip (dhcp e pppoe)
    if('ZTE' in item['serial'].upper()):

        #remove do genieacs
        patrimonio = getpatrimonio_alocado(item['serial']) 
        if(patrimonio):
            if 'mac' in patrimonio:
                item['mac'] = patrimonio['mac']

                job_id = str(uuid.uuid4())
                job_removeacs = q.enqueue(task_removeacs, args=([job_id]), kwargs=(item), retry=Retry(max=5), job_id=job_id, depends_on=job_authorization)
                job = job_to_json(job_removeacs)
                job['description'] = 'REMOVEACS'
                job['provision_id'] = provision_id
                update_job(job)
                jobs.append(job_removeacs.get_id())


        item['vlan'] = int(vlans['vlan_pppoe'])
        item["serviceid"] = 2

        #adiciona vlan pppoe no veip
        job_id = str(uuid.uuid4())
        job_veip_pppoe = q.enqueue(task_addveip, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_veip_pppoe)
        job['description'] = 'VEIP_PPPOE'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_veip_pppoe.get_id())         
                        
        item['vlan'] = os.getenv('TR069_DHCP_VLANID')
        item["serviceid"] = 1

        #adiciona vlan dhcp no veip
        job_id = str(uuid.uuid4())
        job_veip_dhcp = q.enqueue(task_addveip, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_veip_dhcp)
        job['description'] = 'VEIP_DHCP'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_veip_dhcp.get_id())         

        item['vlan'] = 2
        item['vlanport'] = 2
        item['vlantype'] = 'multicast'

        #adiciona vlan iptv multicast na porta 2
        job_id = str(uuid.uuid4())
        job_vlan_multicast = q.enqueue(task_addvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_vlan_multicast)
        job['description'] = 'VLAN_MULTICAST'
        job['provision_id'] = provision_id
        update_job(job)

        jobs.append(job_vlan_multicast.get_id())         

    else:
        #remove as vlans
        job_id = str(uuid.uuid4())
        job_removevlans = q.enqueue(task_removevlans, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_removevlans)
        job['description'] = 'REMOVE_VLANS'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_removevlans.get_id())         
                        
        item['vlan'] = int(vlans['vlan_pppoe'])
        item['vlanport'] = 1
        item['vlantype'] = 'unicast'

        #adiciona vlan pppoe na porta 1
        job_id = str(uuid.uuid4())
        job_vlan_pppoe = q.enqueue(task_addvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_removevlans, job_id=job_id)
        job = job_to_json(job_vlan_pppoe)
        job['description'] = 'VLAN_PPPOE'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_vlan_pppoe.get_id())         
                        
        item['vlan'] = int(vlans['vlan_iptv'])
        item['vlanport'] = 2
        item['vlantype'] = 'unicast'

        #adiciona vlan iptv unicast na porta 2
        job_id = str(uuid.uuid4())
        job_vlan_iptv = q.enqueue(task_addvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_removevlans, job_id=job_id)
        job = job_to_json(job_vlan_iptv)
        job['description'] = 'VLAN_IPTV'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_vlan_iptv.get_id())         

        item['vlan'] = 2
        item['vlanport'] = 2
        item['vlantype'] = 'multicast'

        #adiciona vlan iptv multicast na porta 2
        job_id = str(uuid.uuid4())
        job_vlan_multicast = q.enqueue(task_addvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_removevlans, job_id=job_id)
        job = job_to_json(job_vlan_multicast)
        job['description'] = 'VLAN_MULTICAST'
        job['provision_id'] = provision_id
        update_job(job)

    jobs.append(job_vlan_multicast.get_id())         
    current_provision['started_at'] = datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
    update_provision(current_provision)

    return jobs

item = {
        'olt_ip': '***********',
        'serial': 'ZTEGc4b78079',
        'modelo' : 'F670L',
        'placa' : 12,
        'porta': 16, 
        'nome' : 'testes.reginaldo' 
}

'''
patrimonio = getpatrimonio_alocado(item['serial']) 
if(patrimonio):
    item['mac'] = patrimonio['mac']

job_id = str(uuid.uuid4())
#item['job_id'] = job_id
job_test = q.enqueue(task_removeacs, args=([job_id]), kwargs=(item), retry=Retry(max=5), job_id=job_id)
job = job_to_json(job_test)
job['description'] = 'REMOVEACS'
update_job(job)
#job = job_to_json(job_test)
#job['description'] = 'TEST'
#update_job(job)


'''

#if(pending_provision(item['serial'])):
#    print('Provisionamento pendente') 
#else:
#    print(provision(item))
#if(alarm['olt_ip'] == '***********'): # and value[1].upper() == 'ZTEGC4B347CB'):

#print('***********' in [m['ip'] for m in monitors()])
print(provision(item))

