#!/usr/bin/python
# -*- coding: utf-8 -*-

import telnetlib
import psycopg2
import psycopg2.extras
import pymysql.cursors
from config import *
from database import *

def placa(servico):
  try:  
    str = servico
    str = str[str.rfind('-')+1:len(str)]
    str = str.split('_')
    return str[0]
  except Exception as e:
      return str(e)
      errorlog('Falha ao executar placa: '+ str(e))  

def porta(servico):
  try:  
    str = servico
    str = str[str.rfind('-')+1:len(str)]
    str = str.split('_')
    return str[1]
  except Exception as e:
    return str(e)
    errorlog('Falha ao executar porta: '+ str(e))     

# Processa o retorno do TL1 e transforma em array ou boolean
def parsedata(data):
    try:
      data = data.split('\n')
      str = data[6].split('=')
      block_records = int(str[1])
      if(block_records == 0):
        return []
      else:    
        str = data[10].replace('\r', '')
        headers = str.split('\t')
        str = data[11:]
        records = []
        for i in range(block_records):
            x = str[i]
            x = x.replace('\r', '')
            columns = x.split('\t')
            record = {}
            for y in range(len(headers)):
                header = headers[y]
                record[header] = columns[y]
            records.append(record)    
        return records
    except:
      return False

#Lista ONUs nao autorizadas
def unauthorized_onus():
  try:
    olts = oltlist()  
    unauthorized = []
    for olt in olts:
        tn = telnetlib.Telnet(UNMTL1_HOST, int(UNMTL1_PORT), 5)
        #tn.set_debuglevel(100)
        tn.read_until(b'', 5)
        cmd = 'LOGIN:::CTAG::UN=%s,PWD=%s;' % (UNMTL1_USER, UNMTL1_PASS)
        tn.write(cmd.encode('ascii') + b"\n")
        ret = tn.read_until(b';', 5)
        if(ret.find(b'ENDESC=No error') > 0):
            cmd = 'LST-UNREGONU::OLTID=%s:CTAG::;' % (olt["ip"])
            tn.write(cmd.encode('ascii') + b"\n")
            ret = tn.read_until(b';', 5)
            onus = parsedata(ret.decode('ascii'))
            if(onus):
              if(len(onus) > 0):
                for item in onus:
                  onu = {
                        'olt': olt["nome"],
                        'olt_ip': olt["ip"],
                        'placa': item['SLOTNO'],
                        'porta': item['PONNO'],
                        'serial': item['MAC'],
                        'modelo': item['DT'],
                        'username' : None
                  }
                  unauthorized.append(onu)

        cmd = 'LOGOUT:::CTAG::;'
        tn.write(cmd.encode('ascii') + b"\n")
        ret = tn.read_until(b';', 5)

    onus = []
    for onu in unauthorized:
      onus.append((onu["serial"].lower()))    
    onusinfo = onuinfolist(tuple(onus))
    
    info = {}
    for onu in onusinfo:
      info[str(onu["serial"].lower())] = onu 

    result = [] 
    for onu in unauthorized:
      serial = str(onu["serial"]).lower()

      item = {
        'serial': str(onu['serial']),
        'olt': str(onu["olt"]),
        'ip_olt': str(onu["olt_ip"]),
        'placa': str(onu['placa']),
        'porta': str(onu['porta']),
        'modelo': str(onu['modelo']),
        'vlanid' : getvlanid(str(onu["olt"]), int(onu['placa']), int(onu['porta'])),
        'vlanid_unicast_iptv' : getvlanid_unicast_iptv(str(onu["olt"]), int(onu['placa']), int(onu['porta'])), 
        'username' : None,
        'senha' : None,
        'mac' : None,
        'patrimonio' : None,
        'modelo' : None,
        'situacaoequipamento' : None,
        'wifi' : None, 
        'iptv' : None,
        'stb' : None,
        'escopo' : None,
        'ip_olt_sistema' : None,
        'compat_portas' : None,
        'compat_wifi' : None,
        'compat_tr069' : None
      }  
      if(serial in info):
        item['username'] = info[serial]["username"]
        item['wifi'] = info[serial]["wifi"]
        item['iptv'] = info[serial]["iptv"]
        item['stb'] = info[serial]["stb"]
        item['senha'] = info[serial]["senha"]
        item['escopo'] = info[serial]["escopo"]
        item['modelo'] = info[serial]["modelo"]
        item['ip_olt_sistema'] = info[serial]["ip_olt"]
        item['situacaoequipamento'] = info[serial]["situacaoequipamento"]
        item['patrimonio'] = info[serial]["patrimonio"]
        item['mac'] = info[serial]["mac"]
        item['compat_portas'] = info[serial]["compat_portas"]
        item['compat_wifi'] = info[serial]["compat_wifi"]
        item['compat_tr069'] = info[serial]["compat_tr069"]

      result.append(item)  

    return result
  except Exception as e:
    return e
  finally:
    tn.close()

# Executa um comando via TL1
def cmdexec(cmd, returntype):
    try:
       tn = telnetlib.Telnet(UNMTL1_HOST, int(UNMTL1_PORT), 5)
       #tn.set_debuglevel(100)
       tn.read_until(b'', 5)
       str = 'LOGIN:::CTAG::UN=%s,PWD=%s;' % (UNMTL1_USER, UNMTL1_PASS)
       tn.write(str.encode('ascii') + b"\n")
       ret = tn.read_until(b';', 5)
       if(ret.find(b'ENDESC=No error') > 0): 
         tn.write(cmd.encode('ascii') + b"\n")
         ret = tn.read_until(b';', 5)
         if(returntype == 'array'):
             return parsedata(ret.decode('ascii'))
         else:
             if(ret.find(b'ENDESC=No error') > 0):
               return {
                 "error" : False
               }           
             else:

               return {
                 "error" : True,
                 "data" : ret.decode('utf-8')
               }           
    except Exception as e:
        return {
            "error" : True,
            "data" : e.message
        }
        
# Autoriza a onu
def onuauthorize(onu):
    
    try:
      cmd = "ADD-ONU::OLTID=%s,PONID=NA-NA-%s-%s:CTAG::AUTHTYPE=MAC,ONUTYPE=%s,ONUID=%s,NAME=%s;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["modelo"], onu["serial"], onu["username"])
      print(cmd)
      ret = cmdexec(cmd, "boolean")
      return ret
    except Exception as e:
      errorlog('Falha ao executar onuauthorize: '+ str(e))

# Desautoriza a onu
def onudeauthorize(onu):
    try:
      ret = cmdexec("DEL-ONU::OLTID=%s,PONID=NA-NA-%s-%s:CTAG::ONUIDTYPE=MAC,ONUID=%s;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu['serial']), "boolean")
      return ret
    except Exception as e:
      errorlog('Falha ao executar onudeauthorize: '+ str(e))

# Remove todas as VLANs
def onuremovevlans(onu, portas):
  #  try:
      string_portas = ''
      for i in range(portas):
        string_portas += 'NA-NA-NA-{}|'.format(i+1)
      string_portas = string_portas[:-1]
      cmd = "DEL-LANPORTVLAN::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s,ONUPORT=%s:CTAG::;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], string_portas)
      ret = cmdexec(cmd, "boolean")
      return ret
   # except Exception as e:
    #  errorlog('Falha ao executar onuremovevlanunicast: '+ str(e))

# Adiciona VLAN Unicast
def onuaddvlanunicast(onu, lan, vlan):
    try:
      cmd = "CFG-LANPORTVLAN::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s,ONUPORT=NA-NA-NA-%s:CTAG::CVLAN=%s,CCOS=1;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], lan, vlan)  
      ret = cmdexec(cmd, "boolean")
      return ret
    except Exception as e:
      errorlog('Falha ao executar onuaddvlanunicast: '+ str(e))

# Adiciona VLAN Multicast
def onuaddvlanmulticast(onu, lan, vlan):
    try:
      cmd = "ADD-LANIPTVPORT::OLTID=%s,PONID=1-1-%s-%s,ONUIDTYPE=MAC,ONUID=%s,ONUPORT=NA-NA-NA-%s:CTAG::MVLAN=%s,CCOS=1;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], lan, vlan)  
      ret = cmdexec(cmd, "boolean")
      return ret
    except Exception as e:
      errorlog('Falha ao executar onuaddvlanmulticast: '+ str(e))

# Configura autenticacao PPPoE na LAN
def onuppoelan(onu, lan):
    try:
      ret = cmdexec("SET-WANSERVICE::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s:CTAG::STATUS=1,MODE=2,CONNTYPE=2,VLAN=%d,NAT=1,COS=1,QOS=1,IPMODE=3,PPPOEPROXY=2,PPPOEUSER=%s,PPPOEPASSWD=%s,PPPOENAME=%s,PPPOEMODE=1,UPORT=%d;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], lan, onu["vlan"], vlan, onu["username"], onu["senha"], onu["username"], lan), "boolean")
      return ret
    except Exception as e:
      errorlog('Falha ao executar onupppoelan: '+ str(e))

# Configura autenticacao PPPoE no Wi-fi
def onupppoewifi(onu):
    try:
      ret = cmdexec("SET-WANSERVICE::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s:CTAG::STATUS=1,MODE=2,CONNTYPE=2,VLAN=%d,NAT=1,COS=1,QOS=1,IPMODE=3,PPPOEPROXY=2,PPPOEUSER=%s,PPPOEPASSWD=%s,PPPOENAME=%s,PPPOEMODE=1,SSID=1;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], onu["vlanid"], onu["username"], onu["senha"], onu["username"]), "boolean")
      return ret
    except Exception as e:
      errorlog('Falha ao executar onupppoewifi: '+ str(e))

# Configura rede Wi-fi
def onuconfigwifi(onu, idequipamento):
    try:
      ret = cmdexec("CFG-WIFISERVICE::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s:CTAG::WILESS-AREA=1,WILESS-CHANNEL=8,WILESS-STANDARD=802.11bgn,T-POWER=20,SSID=1,SSID-ENABLE=1,SSID-NAME=%s,SSID-VISIBALE=0,AUTH-MODE=WPA2PSK,ENCRYP-TYPE=AES,PRESHARED-KEY=%s;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], 'telemidia_{}'.format(idequipamento), onu["senha"]), "boolean")
      return ret
    except Exception as e:
      errorlog('Falha ao executar onuconfigwifi: '+ str(e))

# Configura DHCP
def onudhcp(onu):
    try:
      ret = cmdexec("CFG-USERDHCPSERVER::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s:CTAG::LANIP=***********,ENABLE=true,DHCPPOOLSTART=***********,DHCPPOOLEND=***********54,DHCPPRIDNS=**************,DHCPSECDNS=*************,DHCPGATEWAY=***********,DHCPMASK=*************;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"]), "boolean")
      return ret
    except Exception as e:
      errorlog('Falha ao executar onudhcp: '+ str(e))      
