from test_tl1 import *
from tasks_fiberhome import task_checkauthorization

'''
onu = {
    'olt_ip': '***********',
    'placa': 11,
    'porta': 16,
    'serial': 'ZTEGd1e95074'
}
'''


onu = {
    'olt_ip': '***********',
    'placa': 12,
    'porta': 16,
    'serial': 'ZTEGc4b347cb',
    'modelo': 'GPON CURRENCY SFU',
    'nome': 'testes.f670'
}

#check = task_checkauthorization(**onu)

response = onuauthorize(onu)

print(response)