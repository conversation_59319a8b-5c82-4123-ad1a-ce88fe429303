"""
Provision Service
"""
from flask_socketio import SocketIO, emit
from flask import Flask, render_template, url_for, copy_current_request_context, session, request
from flask_cors import CORS
from time import sleep
from threading import Lock
import psycopg2
import psycopg2.extras
import select
import eventlet
import json
import datetime
import os
from dotenv import load_dotenv

load_dotenv()

eventlet.monkey_patch(socket=False)     

app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
app.config['DEBUG'] = False
#CORS(app) #, support_credentials=True)
CORS(app, support_credentials=True)

#socketio = SocketIO(app, async_mode='eventlet', logger=True, engineio_logger=True)
socketio = SocketIO(app, async_mode='eventlet')
#socketio.init_app(app) #, cors_allowed_origins="*")
socketio.init_app(app, cors_allowed_origins="*")
thread_lock = Lock()

def __nocdb():
  try:  
    con = psycopg2.connect(
        host=os.getenv('SERVICES1DB_HOST'),
        database=os.getenv('SERVICES1DB_DATABASE'),
        user=os.getenv('SERVICES1DB_USER'),
        password=os.getenv('SERVICES1DB_PASS')
    )
    return con
  except Exception as e:
    errorlog('Falha em nocdb: '+ str(e))

# converte campos datetime do python para formato iso
def default(o):
    if isinstance(o, (datetime.date, datetime.datetime)):
        return o.isoformat()    

def get_unauthorized():
  try:
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
    sql = 'SELECT * FROM authservice.unauthorized order by data_atualizacao desc'
    cur.execute(sql,())
    recset = cur.fetchall()
    cur.close()
    if(recset):
      return json.dumps(
        recset,
        default=default
      )
    else:
      return json.dumps(
        [],
        default=default
      )

  except Exception as e:
    print('Falha em get_unauthorized: '+ str(e))

def today():
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
    sql = '''
            select count(*) from (
                select count(*) from authservice.authlogs 
                where data::date > CURRENT_DATE - interval '1 day'
                group by upper(serial)
            ) today
          '''  
    cur.execute(sql, ())
    recset = cur.fetchone()
    if(recset):
      recset = recset[0]
    else:
      recset = 0    
    con.close()
    return recset
  except Exception as e:
    print('Falha em get_unauthorized: '+ str(e))

def get_logs():
  try:  
    con = __nocdb()
    cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
    sql = '''
            select * from authservice.authlogs a 
            order by data desc limit 20
          '''  
    cur.execute(sql, ())
    recset = cur.fetchall()
    if(recset):
      return json.dumps(
        recset,
        default=default
      )
    else:
      return json.dumps(
        [],
        default=default
      )
    con.close()
    return recset
  except Exception as e:
    print('Falha em get_logs: '+ str(e))




# Aguarda por notificacoes
def notify_start():
  con = __nocdb()
  con.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)

  curs = con.cursor()
  curs.execute("LISTEN auth_insert;")
  curs.execute("LISTEN unauthorized;")
  curs.execute("LISTEN unauthorized_delete;")

  

  while True:
    socketio.emit('service_status', {'now' : datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}, namespace='/monitor')  
    socketio.sleep(1)
    if select.select([con],[],[],5) != ([],[],[]):
        con.poll()
        while con.notifies:
            socketio.sleep(1)
            notify = con.notifies.pop(0)
            payload = json.loads(notify.payload)
            serial = payload["record"]["serial"]

            if(notify.channel == 'unauthorized' or notify.channel == 'unauthorized_delete'):
              socketio.emit('unauthorized', json.loads(get_unauthorized()), namespace='/monitor')

            if(notify.channel == 'auth_insert'):
              socketio.emit('authlogs', payload["record"], namespace='/monitor')  
              socketio.emit('today', {'count' : today()}, namespace='/monitor')  

@app.route('/')
def index():
    return render_template('index.html')

@socketio.on('connect', namespace='/monitor')
def test_connect():
    session['id'] = request.sid
    socketio.emit('today', {'count' : today()}, room=session['id'], namespace='/monitor')  
    print('Client connected')

@socketio.on('disconnect', namespace='/monitor')
def test_disconnect():
    print('Client disconnected')

@socketio.on('today', namespace='/monitor')
def get_today():
    session['id'] = request.sid
    socketio.emit('today', {'count' : today()}, room=session['id'], namespace='/monitor')  
    socketio.emit('logs', json.loads(get_logs()), room=session['id'], namespace='/monitor')  

if __name__ == '__main__':
    with thread_lock:
      thread = socketio.start_background_task(notify_start)
    socketio.run(app, port=5004,host='0.0.0.0', keyfile='/etc/letsencrypt/live/provision.telemidia.net.br/privkey.pem', certfile='/etc/letsencrypt/live/provision.telemidia.net.br/cert.pem')    

