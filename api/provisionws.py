from flask import Flask, request, jsonify, make_response, Blueprint, session
from db import *
from dotenv import load_dotenv
import os
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta
from flask_cors import CORS
import logging
import urllib3
import eventlet
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from threading import Lock
import select
import json   


urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

eventlet.monkey_patch(socket=False)     

app = Flask(__name__)
load_dotenv()
app.config['SECRET_KEY'] = os.getenv('APP_SECRET')
CORS(app , support_credentials=True)
socketio = SocketIO(app, async_mode='eventlet')
socketio.init_app(app, cors_allowed_origins="*")
thread_lock = Lock()

# Aguarda por notificacoes
def notify_start():
  con = psycopg2.connect(
            host=os.getenv('SERVICES1DB_HOST'),
            database=os.getenv('SERVICES1DB_DATABASE'),
            user=os.getenv('SERVICES1DB_USER'),
            password=os.getenv('SERVICES1DB_PASS')
        )
  con.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)

  curs = con.cursor()
  curs.execute("LISTEN provision_update;")

  while True:
    socketio.sleep(0)
    if select.select([con],[],[],5) != ([],[],[]):
        con.poll()
        while con.notifies:
            notify = con.notifies.pop(0)
            payload = json.loads(notify.payload)

            if(notify.channel == 'provision_update'):
              socketio.emit('provision_update', payload["record"], namespace='/provision')  

@socketio.on('connect', namespace='/proviison')
def connected():
    session['id'] = request.sid
    socketio.emit('heartbeat', {'currentTimeUnix': time.time()}, room=session['id'], namespace='/app')
    print(datetime.now().strftime('%Y-%m-%d %H:%M:%S') + ' [Client connected] : (Client id: ' + session['id'] + ')')
    
@socketio.on('disconnect', namespace='/provision')
def disconnect():
  if('room' in session):
    if(session["room"]):
      leave_room(session["room"])
    print(datetime.now().strftime('%Y-%m-%d %H:%M:%S') + ' [Client disconnected] : (Client id: ' + session['id'] + ')')

if __name__ == '__main__':
    
    with thread_lock:
      thread = socketio.start_background_task(notify_start)
    
    socketio.run(app, port=5010,host='0.0.0.0', keyfile='/etc/letsencrypt/live/provision.telemidia.net.br/privkey.pem', certfile='/etc/letsencrypt/live/provision.telemidia.net.br/cert.pem')
    #socketio.run(app, port=5010,host='0.0.0.0')

    print(datetime.now().strftime('%Y-%m-%d %H:%M:%S') + ' [APP STARTED] : Listening on port 5010')

