#!/usr/bin/python
# -*- coding: utf-8 -*-

from ztemanager import ZTEManager
from database import *
from dotenv import load_dotenv
import os
from cli import *
#from tl1 import *
from ixc_ws import *
import time
import datetime

load_dotenv()

def task_authorization(*args, **kwargs):

    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs

        device = {
            "olt": item["olt"],
            "olt_ip" : item["olt_ip"],
            "olt_modelo": "ZTE",
            "serial" : item["serial"],
            "type": item["modelo"],
            "name" : item["username"],
            "vlan_tr069" : item['vlan_dhcp'],
            "vlan_pppoe" : item['vlan_pppoe'],
            "vlan_iptv_unicast" : item['vlan_iptv'], 
            "vlan_iptv_multicast" : 2,
            "chassi": 1,
            "slot": item["placa"],
            "pon": item["porta"]
        }

        if 'mac' in item:
            device["mac"] = item["mac"]

        if 'patrimonio' in item:  
            device["patrimonio"] = item["patrimonio"]

        if 'modelo' in item:
            device["onu_modelo"] = item["modelo"]

        manager = ZTEManager(item['olt_ip'], item['olt'])
        response = manager.auth(item['serial'], item['modelo'], item['username']) #via objeto

        if('onuid' in response):
            item['onuid'] = response['onuid']
            device['onuid'] = response['onuid']
      
        if('chassi' in response):
            item['chassi'] = response['chassi']  
            device['chassi'] = response['chassi']  

        updatedevice(device)

        if('onuid' in response):

            update_job({
                'id': args[0], 
                'status' :'finished', 
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })      
            return True
        else:
            update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(response),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })       
            raise Exception("Erro em task_authorization: {}".format(str(e)))   
        
    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })       
        raise Exception("Erro ao autorizar a onu em {}".format(item["olt_ip"]))


def task_deauthorizefh(*args, **kwargs):
    
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        # remove a onu de todas olts fiberhome
        #olts = unm_oltlist()
        olts = monitors('FBT')
    
        for olt in olts:
            removewhitelist(olt["ip"], item["serial"])

        # desautoriza todas as onus nomeadas com o mesmo username, exceto a nova
        provisions = findauthorizedonu(None, item["username"])
        for provision in provisions:
            if(provision['serial'].upper() != item["serial"].upper()):
                removewhitelist(provision['olt_ip'], provision['serial'])
        
        update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro em deauthorizefh: {}".format(str(e)))   


def task_deauthorizezte(*args, **kwargs):
    
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
       
        # se a onu ja estava autorizada em uma olt ZTE: 
        previous_device = getdevice(item["serial"])
        if(previous_device):
            manager = ZTEManager(previous_device['olt_ip'], previous_device['olt'])
            response = manager.deauth(None, previous_device['chassi'], previous_device['slot'], previous_device['pon'], previous_device['onuid'])
            manager.disconnect()
            del manager

        update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro ao deauthorizezte: {}".format(str(e)))

def task_config(*args, **kwargs):

    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })

        device = getdevice(item["serial"])
        if(device):

            manager = ZTEManager(item['olt_ip'], item['olt'])
            response = manager.config_onu(item['chassi'], item['slot'], item['pon'], device['onuid'], int(item['vlan_dhcp']), int(item['vlan_pppoe']), int(item['vlan_iptv']), 2)
            if('script' in response):
                device['script'] = response['script']
                updatedevice(device)
            manager.disconnect()
            del manager

        update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro ao configurar a ONU: {}".format(str(e)))   