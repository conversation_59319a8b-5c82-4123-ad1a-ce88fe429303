import time
import os
from dotenv import load_dotenv
import pexpect
from datetime import datetime
import re
import sys
import json


class Telnet:
    def __init__(self, ip, user, password, debug):
        try:
            self.connected = False
            self.ip = ip
            self.user = user
            self.password = password
            self.timeout = 5
            self.rp1000 = False
            self.debug = debug
            load_dotenv()

        except Exception as e:
            print("Error on init (telnet): "+str(e))
            # sys.exit(1)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        pass

    # @classmethod
    def connect(self):
        try:
            self.__client = pexpect.spawn('telnet {}'.format(
                self.ip), timeout=30)

            if(self.__client):
                self.__login()

            if(self.debug):
                self.__client.logfile = open("fhmanager.log", "w+")

        except Exception as e:
            self.connected = False
            # return e
            print("(Telnet) Error on connect: {}".format(str(e)))

            # sys.exit(1)

    def __login(self):
        try:
            self.__client.expect(
                [pexpect.TIMEOUT, 'Login: ', pexpect.EOF])
            self.__client.sendline(os.getenv('CLI_USER'))
            self.__client.expect(
                [pexpect.TIMEOUT, 'Password: ', pexpect.EOF])
            self.__client.sendline(os.getenv('CLI_PASS'))
            self.__client.expect(
                [pexpect.TIMEOUT, 'User> ', pexpect.EOF])
            self.__client.sendline("enable")
            self.__client.expect(
                [pexpect.TIMEOUT, 'Password: ', pexpect.EOF])
            self.__client.sendline(os.getenv('CLI_PASS'))
            self.__client.expect(
                [pexpect.TIMEOUT, 'Admin# ', pexpect.EOF])
            # verifica se e rp1000
            self.__client.sendline('dir')
            self.__client.expect(
                [pexpect.TIMEOUT, 'Admin# ', pexpect.EOF])
            ret = ""
            ret += self.__client.before  # .decode()
            rp1000 = ret.find('<DIR>') != -1
            self.rp1000 = rp1000
            self.connected = True
        except Exception as e:
            # return e
            print("(Telnet) Error on login: {}".format(str(e)))

    def disconnect(self):
        try:
            if(self.__client):
                self.__client.close()
                self.connected = False
        except Exception as e:
            print("(Telnet) Error on disconnect: {}".format(str(e)))
            sys.exit(1)

    def execute(self, cmd, expectstr):
        try:
            if(self.connected):
                self.__client.sendline(cmd)
                self.__client.expect(
                    [pexpect.TIMEOUT, expectstr, pexpect.EOF])
                return self.__client.before  # .decode()
            else:
                return "Telnet Not connected"
        except Exception as e:
            print("Error on execute: {}".format(str(e)))
         #   sys.exit(1)
        except pexpect.TIMEOUT:
            print("Error on execute: {}".format('Timeout exceeded'))
           # sys.exit(1)


class FHManager:
    """
      Class for Fiberhome OLT Management
      ONU provisioning and configuration

      ...

      Attributes
      ----------
      olt : dict
          OLT information
      debug: boolean
          Enable debug

      Methods
      -------
      connect
          Start telnet connection on OLT
      disconnect
          Stop telnet connection on OLT
      find_auth(sn)
          Find authorized ONU
      list_unauth()
          Retrieve unauthorized ONUs
      deauth(sn)
          Deauthorize ONU
      """

    def __init__(self, olt, slots=None, debug=False, olt_name=None):
        """
            Parameters
            ----------
            olt : str
                OLT ip
            debug: boolean
                Enable debug
        """
        try:
            self.olt = olt
            self.olt_name = olt_name
            self.slots = slots
            self.__telnet = Telnet(self.olt, os.getenv(
                'CLI_USER'), os.getenv('CLI_PASS'), debug)
            self.connected = False
            self.debug = debug
        except Exception as e:
            print("(FHManager) Error on fhmanager.init: "+str(e))

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        pass

    def connect(self):
        """ Start OLT Telnet connection """
        try:
            self.__telnet.connect()
            self.connected = True
            return self.connected
        except Exception as e:
            self.connected = False
            return e
            # return "(FHManager) Error on connect: "+str(e)

    def disconnect(self):
        """ Close OLT Telnet connection """
        try:
            self.__telnet.disconnect()
            self.connected = False
        except Exception as e:
            self.connected = False
            return "(FHManager) Error on fhmanager.disconnect: "+str(e)

    def find_auth(self, serial):
        """ Check if ONU is authorized

        Parameters
        ----------
        serial : str
            ONU serial number

        Returns
        -------
        boolean

        Raises
        ------
        Error
        """
        try:

            if(self.connected == False):
                return "FHManager Not connected"

            if(self.__telnet.rp1000):
                expectstr = 'onu#'
                self.__telnet.execute('cd onu', expectstr)
            else:
                expectstr = 'gpononu#'
                self.__telnet.execute('cd gpononu', expectstr)

            # padrao physicid = quatro primeiros caracteres maiusculos e o restante minusculo
            serial = serial[:4].upper()+serial[4:].lower()
            data = self.__telnet.execute(
                'show onu-authinfo phy-id {}'.format(serial), expectstr)
            data = data.replace(' ', '')

            self.disconnect()
            if(self.__telnet.rp1000):
                if("Onu is not authcated" in data):
                    return False
                else:
                    return "ONU:" in data
            else:
                return "Auth-Status:Authed" in data

        except Exception as e:
            print("(FHManager) Error on fhmanager.find_auth: {}".format(str(e)))
        except pexpect.TIMEOUT:
            print("(FHManager) Error on fhmanager.find_auth: {}".format(
                'Timeout exceeded'))

    def get_veip(self, slot, pon, onuid):
        """ Return VEIP config for ONU

        Parameters
        ----------
        slot : int
            OLT slot number

        pon : int
            OLT pon number

        onuid : int
            ONU id number

        Returns
        -------
        dict

        Raises
        ------
        Error
        """
        try:

            if(self.connected == False):
                return "FHManager Not connected"

            if(self.__telnet.rp1000):
                self.__telnet.execute('cd onu', 'onu#')
                expectstr = 'lan#'
                self.__telnet.execute('cd lan', expectstr)
            else:
                self.__telnet.execute('cd epononu', 'epononu#')
                expectstr = 'qinq#'
                self.__telnet.execute('cd qinq', expectstr)

            data = self.__telnet.execute('show epon slot {} pon {} onu {} onuveip servindex'.format(
                slot, pon, onuid), expectstr)

            if(self.__telnet.rp1000):
                p = re.compile(
                    "Cvlan                   : (.*)")
                result = map(int, p.findall(data))
            else:
                p = re.compile(
                    "cvlan 33024 (.*) 65535 tvlan 33024 65535 65535 svlan 33024 65535 65535 tls 0 servmode 2 svlan 65535  servdiff 65535")
                result = map(int, p.findall(data))
            self.disconnect()
            return list(result)

        except Exception as e:
            print("(FHManager) Error on fhmanager.get_veip: {}".format(str(e)))
            return []
        except pexpect.TIMEOUT:
            print("(FHManager) Error on fhmanager.get_veip: {}".format(
                'Timeout exceeded'))
            return []

    def set_veip(self, slot, pon, onuid, vlans):

        try:
            if(self.connected == False):
                return "FHManager Not connected"

            if(self.__telnet.rp1000):
                self.__telnet.execute('cd onu/lan', 'lan#')
                expectstr = 'lan#'
            else:
                self.__telnet.execute('cd epononu', 'epononu#')
                self.__telnet.execute('cd qinq', 'qinq#')
                expectstr = 'qinq#'

            for vlan in vlans:
                data = self.__telnet.execute('set epon slot {} pon {} onu {} port 1 onuveip {} 33024 {} 65535 33024 65535 65535 33024 65535 65535 0 {} 65535'.format(
                    slot, pon, onuid, vlan["serviceid"], vlan["vlanid"], vlan["profileid"]), expectstr)
            self.disconnect()
            return True
        except Exception as e:
            print("(FHManager) Error on fhmanager.set_veip: {}".format(str(e)))
        except pexpect.TIMEOUT:
            print("(FHManager) Error on fhmanager.set_veip: {}".format(
                'Timeout exceeded'))

    def deauth(self, serial):
        """ Deauthorize ONU

        Parameters
        ----------
        sn : str
            ONU serial number
        """
        try:
            if(self.connected == False):
                return "FHManager Not connected"

            if(self.__telnet.rp1000):
                self.__telnet.execute('cd onu', 'onu#')
                expectstr = 'onu#'
            else:
                self.__telnet.execute('cd gpononu', 'gpononu#')
                expectstr = 'gpononu#'

            # padrao physicid = quatro primeiros caracteres maiusculos e o restante minusculo
            serial = serial[:4].upper()+serial[4:].lower()
            data = self.__telnet.execute(
                'set whitelist phy_addr address {} password null action delete'.format(serial), expectstr)
            data = data.replace(' ', '')

            result = 'success' in data or 'setonuwhitelistok' in data
            return result
        except Exception as e:
            print("(FHManager) Error on fhmanager.unauth: {}".format(str(e)))
            return False
        except pexpect.TIMEOUT:
            print("(FHManager) Error on fhmanager.unauth: {}".format(
                'Timeout exceeded'))
            return False

    def list_unauth(self, slot=None, pon=None):
        """ Search for unauthorize ONUs

        Parameters
        ----------
        slot : str
            Slot for searching
        pon : str
            Pon for searching
        """
        try:
            if(self.connected == False):
                return "FHManager Not connected"
            if(not self.slots):
                return "Slots not defined"

            if(self.__telnet.rp1000):
                self.__telnet.execute('cd onu', 'onu#')
                expectstr = r'Admin\\onu# '
            else:
                self.__telnet.execute('cd gpononu', 'gpononu#')
                expectstr = r'Admin\\gpononu# '

            unauthorized = []
            if(slot == None):
                slots = self.slots
            else:
                slots = [{'slot': slot, 'pons': 16}]

            for slot_item in slots:
                if(pon == None):
                    pons_range = []
                    for i in range(slot_item['pons']):
                        pon_item = {}
                        pon_item['slot'] = slot_item['slot']
                        pon_item['pon'] = i+1
                        pons_range.append(pon_item)
                else:
                    pons_range = []
                    pon_item = {}
                    pon_item['slot'] = slot
                    pon_item['pon'] = pon
                    pons_range.append(pon_item)

                for pon_item in pons_range:

                    # print("[{}] Capturando onus nao autorizadas na OLT {} ({}) Placa {} Porta {}".format(scriptname, olt['nome'], olt['ip'], pon['placa'], pon['porta']))
                    if (self.__telnet.rp1000):
                        data = self.__telnet.execute('show discovery slot {} pon {}'.format(
                            pon_item['slot'], pon_item['pon']), expectstr)
                    else:
                        data = self.__telnet.execute('show discovery slot {} link {}'.format(
                            pon_item['slot'], pon_item['pon']), expectstr)

                    if(data):
                        data = data.splitlines()

                        if (len(data) > 1):
                            header = (
                                (data[1].replace(' ', '').replace('-', '')).split(','))
                            if (len(header) > 2):
                                value = header[2].split('=')
                                count = value[1]

                                # caso tenha alguma onu na lista, os dados se iniciam no indice 7
                                list = data[7:]
                                # remove itens vazios
                                while("" in list):
                                    list.remove("")

                                for s in list:
                                    # normaliza a string deixando com apenas um espaco por valor
                                    s = ' '.join(s.split())
                                    # divide os valores em lista, separando cada valor por espaco
                                    s = s.split(' ')
                                    onu = {
                                        'olt_ip': self.olt,
                                        'olt_model': 'FBT',
                                        'chassi': None,
                                        'slot': pon_item['slot'],
                                        'pon': pon_item['pon'],
                                        'sn': s[2],
                                        'type': s[1]
                                    }
                                    unauthorized.append(onu)
            return unauthorized
        except pexpect.TIMEOUT:
            print("(FHManager) Error on fhmanager.list_unauth: {}".format(
                'Timeout exceeded'))
            return False
        except Exception as e:
            print("(FHManager) Error on fhmanager.list_unauth: {}".format(str(e)))
            return []

    def change_port_isolation(self, slot, pon, onuid, option):
        """ Change port isolation parameter

        Parameters
        ----------
        slot : str
            Slot number
        pon : str
            Pon number
        onuid : str
            ONU id
        option : str
            enable/disable

        """
        try:
            if(self.connected == False):
                return "FHManager Not connected"

            if(self.__telnet.rp1000):
                cmdstr = 'set port_separate slot {} pon {} onu {} separate {}'
                self.__telnet.execute('cd onu', 'onu#')
                expectstr = r'Admin\\onu# '
            else:
                cmdstr = 'set port_separate slot {} link {} onu {} separate {}'
                self.__telnet.execute('cd gpononu', 'gpononu#')
                expectstr = r'Admin\\gpononu# '

            data = self.__telnet.execute(cmdstr.format(
                slot, pon, onuid, option), expectstr)

            return 'ok' in data

        except pexpect.TIMEOUT:
            print("(FHManager) Error on fhmanager.change_port_isolation: {}".format(
                'Timeout exceeded'))
            return False
        except Exception as e:
            print(
                "(FHManager) Error on fhmanager.change_port_isolation: {}".format(str(e)))
            return False

    def get_signal(self, slot, pon, onuid, serial, name):
        """ Get ONU signal

        Parameters
        ----------
        slot : str
            Slot number
        pon : str
            Pon number
        onuid : str
            ONU id
        """
        try:
            if(self.connected == False):
                return "FHManager Not connected"

            if(self.__telnet.rp1000):
                expectstr = 'onu#'
                self.__telnet.execute('cd onu', expectstr)
                cmdstr = 'show optic_module slot {} pon {} onu {}'
                expectstr = r'Admin\\onu# '
            else:
                expectstr = 'gpononu#'
                self.__telnet.execute('cd gpononu', expectstr)
                cmdstr = 'show optic_module slot {} link {} onu {}'
                expectstr = r'Admin\\gpononu# '

            data = self.__telnet.execute(cmdstr.format(
                slot, pon, onuid), expectstr)

            signal = {
                'olt': self.olt,
                'serial': serial,
                'name': name,
                'slot': slot,
                'pon': pon,
                'onuid': onuid,
                'temperature': None,
                'voltage': None,
                'biascurrent': None,
                'sendpower': None,
                'recvpower': None,
                'oltrecvpower': None,
                'rtt': None
            }

            if(data.find('unactive') == -1):
                data = ' '.join(data.split())
            else:
                data = ''

            if(data != ''):

                match = re.search(r"slot ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['slot'] = match.group(1)

                match = re.search(r"link ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['pon'] = match.group(1)

                match = re.search(r"pon ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['pon'] = match.group(1)

                match = re.search(r"onu ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['onuid'] = match.group(1)

                match = re.search(r"TEMPERATURE : ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['temperature'] = match.group(1)

                match = re.search(r"VOLTAGE : ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['voltage'] = match.group(1)

                match = re.search(r"BIAS CURRENT : ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['biascurrent'] = match.group(1)

                match = re.search(r"SEND POWER : ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['sendpower'] = match.group(1)

                match = re.search(r"RECV POWER : ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['recvpower'] = match.group(1)

                # OLT Receive Power
                if(self.__telnet.rp1000):
                    self.__telnet.execute('cd ..', 'Admin# ')
                    expectstr = r'Admin\\card# '
                    self.__telnet.execute('cd card', expectstr)
                    cmdstr = 'show optic_module_para slot {} pon {}'
                else:
                    self.__telnet.execute('cd gponlinecard', 'gponline# ')
                    cmdstr = 'show optic_module_par slot {} link {}'
                    expectstr = 'gponline# '

                data = self.__telnet.execute(cmdstr.format(
                    slot, pon), expectstr)

                data = data.splitlines()

                indices = [i for i, s in enumerate(data) if 'ITEM' in s]

                items = []
                if(len(indices) > 0):
                    for item in data[indices[0]+1:]:
                        item = ' '.join(item.split()).split()

                        if(len(item) > 1):

                            items.append({
                                'onuid': item[0],
                                'recv_power': item[1]
                            })

                    olt_recv_power = next(item['recv_power']
                                          for item in items if item["onuid"] == str(onuid))

                    signal['oltrecvpower'] = olt_recv_power

                # Distancia da ONU
                if(self.__telnet.rp1000):
                    self.__telnet.execute('cd ..', 'Admin# ')
                    expectstr = r'Admin\\onu# '
                    self.__telnet.execute('cd onu', expectstr)
                    cmdstr = 'show rtt_value slot {} pon {} onu {}'
                else:
                    expectstr = r'Admin\\gpononu# '
                    self.__telnet.execute('cd gpononu', expectstr)
                    cmdstr = 'show rtt_value slot {} link {} onu {}'

                data = self.__telnet.execute(cmdstr.format(
                    slot, pon, onuid), expectstr)

                match = re.search(
                    r"ONU RTT VALUE = ([-+]?\d*\.\d+|\d+)", data)
                if match:
                    signal['rtt'] = match.group(1)

                self.__telnet.execute('cd ..', 'Admin# ')

            return signal

        except pexpect.TIMEOUT:
            print("(FHManager) Error on fhmanager.get_signal: {}".format(
                'Timeout exceeded'))
            return False
        except Exception as e:
            print(
                "(FHManager) Error on fhmanager.get_signal: {}".format(str(e)))
            return False

    def getlastonofftime(self, slot, pon, onuid):
        """ Get ONU Last ON/OFF time

        Parameters
        ----------
        slot : str
            Slot number
        pon : str
            Pon number
        onuid : str
            ONU id
        """

        if(self.connected == False):
            return "FHManager Not connected"

        if(self.__telnet.rp1000):
            expectstr = 'onu#'
            self.__telnet.execute('cd onu', expectstr)
            cmdstr = 'show onu_last_on_and_off_time slot {} pon {} onu {}'
        else:
            expectstr = 'gpononu#'
            self.__telnet.execute('cd gpononu', expectstr)
            cmdstr = 'show onu_last_on_and_off_time slot {} link {} onu {}'

        data = self.__telnet.execute(cmdstr.format(
            slot, pon, onuid), expectstr)

        response = {}
        p = re.compile("Last Off Time = (.*),Last On Time = (.*)")
        match = p.search(data)
        if(match):
            response['last_off_time'] = match.group(1).replace('\r', '')
            response['last_on_time'] = match.group(2).replace('\r', '')

        return response
