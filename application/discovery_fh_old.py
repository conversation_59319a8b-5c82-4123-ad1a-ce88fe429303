#!/usr/bin/python
# -*- coding: utf-8 -*-

from database import *
from dotenv import load_dotenv
import os
from cli import *
from multiprocessing import Process
import time
import pexpect
import sys
from datetime import date
from genieacs import *
from worker_provision_queue import conn
from tasks_provision import *

load_dotenv()

scriptname = 'DISCOVERY'

def discovery(olt, placa, porta):
#  try:
    onus = []
    child = pexpect.spawn('telnet {}'.format(olt['ip']), encoding='utf-8', codec_errors='ignore')
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_USER'))
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline("enable")
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    ret = ""
    ret += child.before
    
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if (rp1000):
      child.sendline('cd onu')
      expectstr = 'onu#'  
    else:
      child.sendline('cd gpononu')
      expectstr = 'gpononu#'

    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))  
    unauthorized = []
    if(placa == None):
      slots = slotlist(olt['ip'])
    else:
      slots = [{'slot' : placa, 'pons' : 16}]

    for slot in slots:
      if(porta == None):
        pons = []
        for i in range(slot['pons']):
          pon = {}
          pon['placa'] = slot['slot']
          pon['porta'] = i+1
          pons.append(pon) 
      else:
        pons = []
        pon = {}
        pon['placa'] = placa
        pon['porta'] = porta
        pons.append(pon) 

      for pon in pons:
        #print("[{}] Capturando onus nao autorizadas na OLT {} ({}) Placa {} Porta {}".format(scriptname, olt['nome'], olt['ip'], pon['placa'], pon['porta']))
        ret = ""
        if (rp1000):
          child.sendline('show discovery slot {} pon {}'.format(pon['placa'], pon['porta']))
          child.expect(r'Admin\\onu# ', int(os.getenv('PEXPECT_TIMEOUT')))
          ret += child.before
          child.sendline('clear')
          child.expect(r'Admin\\onu# ', int(os.getenv('PEXPECT_TIMEOUT')))
        else:  
          child.sendline('show discovery slot {} link {}'.format(pon['placa'], pon['porta']))
          child.expect(r'Admin\\gpononu# ', int(os.getenv('PEXPECT_TIMEOUT')))
          ret += child.before
          child.sendline('clear')
          child.expect(r'Admin\\gpononu# ', int(os.getenv('PEXPECT_TIMEOUT')))

        data = ret.splitlines()

        if (len(data) > 1):
          header = ((data[1].replace(' ', '').replace('-', '')).split(','))
          if (len(header) > 2):
            value = header[2].split('=')
            count = value[1]

            # caso tenha alguma onu na lista, os dados se iniciam no indice 7
            list = data[7:]
            # remove itens vazios
            while("" in list):
                list.remove("")

            for s in list:
              # normaliza a string deixando com apenas um espaco por valor
              s = ' '.join(s.split())
              # divide os valores em lista, separando cada valor por espaco
              s = s.split(' ')
              onu = {
                  'olt': olt["nome"],
                  'olt_ip': olt["ip"],
                  'placa': pon['placa'],
                  'porta': pon['porta'],
                  'serial': s[2],
                  'modelo': s[1]
              }
              onu['data'] = date.today()
              onu['source'] = 'DISCOVERY'
              onu['mac'] = None
              onu['username'] = None 
              onu['patrimonio'] = None 
              onu['login_ativo'] = None
              onu['data_comodato'] = None
              onu['id_comodato'] = None
              onu['status'] = 'WAITING'

              patrimonio = getpatrimonio_alocado(onu['serial'])  

              if(patrimonio):
                onu['mac'] = patrimonio['mac']
                onu['modelo'] = patrimonio['modelo']
                onu['patrimonio'] = patrimonio['patrimonio']
                onu['login_ativo'] = patrimonio['login_ativo']
                onu['data_comodato'] = patrimonio['data_comodato']
                onu['id_comodato'] = patrimonio['id_comodato']
                onu['username'] = patrimonio['login']  
                onu['nome'] = patrimonio['login']  
                onu['login_ativo'] = patrimonio['login_ativo']
                onu['plano'] = patrimonio['plano']
                
                if('ZTE' in onu['serial'].upper()):
                  onu['tr069'] = 1
                else:
                  onu['tr069'] = 0  

                if((os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None) and (checkignorelist(serial=onu["serial"].upper()))):
                  msg = 'ONU {} esta na lista para ignorar e nao sera autorizada'.format(onu["serial"])
                  print(msg)
                else:
                  #se for informada uma onu de testes, provisiona apenas esta onu
                  if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                    onus.append(onu)

    #se existir alguma onu nao provisionada em olt no sistema antigo de provisionamento adiciona na tabela unauthorized
    if(len(onus) > 0):
      update_unauthorized(onus, scriptname)

    child.close()
    exit()
 # except Exception as e:
 #   print('Falha em discovery: '+ str(e)) 

if __name__ == '__main__':
  try:

    #olts = monitors('FBT')
    olts = unm_oltlist()

    proccess = []

    for olt in olts:
      if(olt['ip'] not in [m['ip'] for m in monitors()]):
        p = Process(target=discovery, args=(olt,None, None,))
        p.start()
        proccess.append(p)
  except Exception as e:
    print('Ocorreu um erro em __main__: '+str(e))    
