from ixc_ws import *
from database import *

scriptname = 'test_dadostecnicos'

item = {
    'serial': 'ZTEGC44DAB59',
    'olt_ip': '*************',
    'placa': '2',
    'porta': '12'
}

#carrega os dados via banco para atualizar os dados tecnicos no ixc

payload = get_dados_tecnicos(item['serial'], item['olt_ip'], '0-0-{}-{}'.format(item["placa"], item["porta"]))
if(payload):
    if('id' in payload and payload['id'] != None):
        #atualizar os dados tecnicos no ixc via api
        msg = '[{}] Atualizando dados tecnicos no IXC {} '.format(scriptname, item["serial"])
        print(msg)
        response = update_dados_tecnicos(payload)
        response = json.loads(response) 
        if('type' in response):
            if(response['type'] == 'error'):
                print('Atualizando dados tecnicos no IXC {}..ERRO'.format(item["serial"]))
            else:
                msg = 'Atualizando dados tecnicos no IXC'.format(item["serial"])
               # gravalog(item, 'IXC', msg, 0)
        else:
            print('Atualizando dados tecnicos no IXC {}..ERRO'.format(item["serial"]))
else:
    print('Atualizando dados tecnicos no IXC {}..ERRO'.format(item["serial"]))



