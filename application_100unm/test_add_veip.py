import requests
import os
from rq import Queue, Retry
from rq.job import Job
from worker_provision_queue import conn
import json
import time
from datetime import datetime
from dotenv import load_dotenv
from database import *
from tasks_fiberhome import *
from tasks_ixc import *
from tasks_genieacs import *
from rq.serializers import JSONSerializer
import uuid
from pytz import timezone

load_dotenv()

q = Queue('provision_queue_100unm', connection=conn)

def job_to_json(job):
    job_result = Job.fetch(job.get_id(), connection=conn)
    job_json = {
        'id' : job_result.get_id(),
        'enqueued_at' : job_result.enqueued_at,
        'status' : job_result.get_status(refresh=True),
        'started_at' : job_result.started_at,
        'ended_at' : job_result.ended_at,
        'exc_info' : job_result.exc_info,
        'worker_name' : job_result.worker_name,
        'last_heartbeat' : job_result.last_heartbeat,
        'origin': job_result.origin,
        'func_name' : job_result.func_name,
        'kwargs': json.dumps(job_result.kwargs, indent=4, sort_keys=True, default=str)
    }
    return job_json


jobs = get_jobs_test()
for job in jobs:
    print(job['kwargs'])

'''
    #consulta as vlans
    vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])

    # Se for compativel com tr069 adiciona as vlans no veip (dhcp e pppoe)
    if('ZTE' in item['serial'].upper()):

        item['vlan'] = int(vlans['vlan_pppoe'])
        item["serviceid"] = 2

        #adiciona vlan pppoe no veip
        job_id = str(uuid.uuid4())
        job_veip_pppoe = q.enqueue(task_addveip, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_veip_pppoe)
        job['description'] = 'VEIP_PPPOE'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_veip_pppoe.get_id())         
                        
        item['vlan'] = os.getenv('TR069_DHCP_VLANID')
        item["serviceid"] = 1

        #adiciona vlan dhcp no veip
        job_id = str(uuid.uuid4())
        job_veip_dhcp = q.enqueue(task_addveip, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_veip_dhcp)
        job['description'] = 'VEIP_DHCP'
        job['provision_id'] = provision_id
        update_job(job)
        jobs.append(job_veip_dhcp.get_id())         

        item['vlan'] = 2
        item['vlanport'] = 2
        item['vlantype'] = 'multicast'

        #adiciona vlan iptv multicast na porta 2
        job_id = str(uuid.uuid4())
        job_vlan_multicast = q.enqueue(task_addvlan, args=([job_id]), kwargs=(item), retry=Retry(max=5), depends_on=job_authorization, job_id=job_id)
        job = job_to_json(job_vlan_multicast)
        job['description'] = 'VLAN_MULTICAST'
        job['provision_id'] = provision_id
        update_job(job)

        jobs.append(job_vlan_multicast.get_id())         
    

    return jobs
'''