#!/usr/bin/env python3
"""
Teste para verificar se as dependências estão sendo criadas corretamente no PythonRQ
"""

import os
import sys
import uuid
from dotenv import load_dotenv

# Adicionar o diretório atual ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tasks import create_provision
from database import get_provision_tasks

load_dotenv()

def test_dependencies():
    """
    Testa se as dependências estão sendo criadas corretamente
    """

    # Primeiro, vamos verificar quais OLTs estão disponíveis no banco
    from database import __nocdb
    import psycopg2.extras

    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Verificar se há tarefas configuradas
        sql_query = """
        SELECT DISTINCT m.ip, COUNT(t.id) as task_count
        FROM authservice.monitors m
        LEFT JOIN authservice.tasks t ON t.task_group_id = m.task_group
        GROUP BY m.ip
        HAVING COUNT(t.id) > 0
        LIMIT 5
        """
        cur.execute(sql_query)
        available_olts = cur.fetchall()

        if not available_olts:
            print("ERRO: Nenhuma OLT com tarefas configuradas encontrada")
            # Tentar buscar qualquer OLT
            sql_query = "SELECT DISTINCT ip FROM authservice.monitors LIMIT 5"
            cur.execute(sql_query)
            available_olts = cur.fetchall()

        con.close()

        if not available_olts:
            print("ERRO: Nenhuma OLT encontrada no banco de dados")
            return False

        print("=== OLTs disponíveis no banco ===")
        for olt in available_olts:
            if 'task_count' in olt:
                print(f"  - {olt['ip']} ({olt['task_count']} tarefas)")
            else:
                print(f"  - {olt['ip']}")

        # Usar a primeira OLT disponível
        test_olt_ip = available_olts[0]['ip']

    except Exception as e:
        print(f"Erro ao buscar OLTs: {e}")
        # Fallback para um IP de teste
        test_olt_ip = '*************'

    # Item de teste
    test_item = {
        'serial': 'TEST123456',
        'username': 'test_user',
        'olt_ip': test_olt_ip,
        'olt': 'OLT_TEST',
        'olt_model': 'ZTE',
        'placa': 1,
        'porta': 1,
        'modelo': 'F601',
        'source': 'test'
    }

    print(f"\n=== Teste de Dependências ===")
    print(f"Testando com item: {test_item}")

    try:
        # Primeiro, vamos verificar as tarefas disponíveis
        tasks = get_provision_tasks(olt=test_item["olt_ip"])
        print(f"\nTarefas encontradas para OLT {test_item['olt_ip']}:")

        if not tasks:
            print("ERRO: Nenhuma tarefa encontrada para esta OLT")
            print("Isso pode ser normal se não houver tarefas configuradas para esta OLT")
            return True  # Não é um erro fatal
            
        for task in tasks:
            print(f"  - ID: {task['id']}, Task: {task['task']}, Depends_on: {task['depends_on']}, Order: {task['order']}")
        
        # Agora vamos criar a provision
        print(f"\nCriando provision...")
        jobs = create_provision(test_item)
        
        print(f"\nJobs criados: {len(jobs)}")
        for i, job_id in enumerate(jobs):
            print(f"  Job {i+1}: {job_id}")
            
        print("\n✅ Teste concluído com sucesso!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_dependencies()
    sys.exit(0 if success else 1)
