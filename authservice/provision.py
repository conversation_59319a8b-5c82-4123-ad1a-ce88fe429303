import pexpect
import select
from database import *
from tl1 import *
from cli import *
from log import *
from ixc_ws import *
import time
import sys
from datetime import datetime as dt
import datetime
import traceback
from config import *
from genieacs import *
import urllib3
from fhmanager import FHManager
#from ztemanager import ZTEManager

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

scriptname = 'provision.service'

# Aguarda por notificacoes de insert na tabela unauthorized e authlogs
def notify_start():
  try:
    con = psycopg2.connect(
          host=SERVICES1DB_HOST,
          database='noc2',
          user=SERVICES1DB_USER,
          password=SERVICES1DB_PASS
    )
    con.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)

    curs = con.cursor()
    curs.execute("LISTEN unauthorized;") #onus aguardando autorizacao
    curs.execute("LISTEN auth_insert;") #logs de provisionamento (checagem de configuracoes)
    #verifica se existem onus para provisionamento
    check_onus(get_unauthorized())

    while True:
      if select.select([con],[],[],5) != ([],[],[]):
          con.poll()
          while con.notifies:
              notify = con.notifies.pop(0)
              #print('channel: {} pid: {} payload: {}'.format(notify.channel, notify.pid, notify.payload))
              payload = json.loads(notify.payload)
              if(notify.channel == 'unauthorized'):
                  check_onus(findunauthorized(payload["record"]["serial"])) #tenta provisionar apenas a onu que foi inserida na tabela

              if(notify.channel == 'auth_insert'):
                #faz a checagem da configuracao apenas se ainda nao foi verificada
                # e o numero de tentativas nao excedeu o limite
                if(payload['record']['verified'] == 0):
                  if(payload['record']['retries'] < PROVISION_CHECK_RETRIES):
                    check_task(payload['record'])  
  except Exception as e:
    errorlog(traceback.format_exc())
    raise  
    #errorlog('Falha em notify_start: '+ str(e))                  

#seleciona o tipo de checagem de configuracao
def check_task(record):
  try:
    #para todas as onus caso nao esteja em modo teste e apenas se existirem onus no modo teste
    if(((len(ONUS_TESTE) > 0) and (record['serial'].upper() in ONUS_TESTE) and (TEST_MODE)) or (TEST_MODE == False)):
      #lista de checagens possiveis
      switcher = {
          'AUTHORIZATION': check_authorization,
          'MULTICAST': check_multicast,
          'VLANS': check_vlans,
          'VEIP': check_veip
      }
      task = record['task']
      if(task in switcher):
        switcher[task](record)
      else:
        record["verified"] = 1
        updatelog(record)
        print("Invalid task: {}".format(task))
  except Exception as e:
    errorlog('Falha em check_task: '+ str(e))        

#nao implementado
def check_authorization(record):
  #ret = checkwhitelist(record["olt_ip"], record["serial"])
  #if(ret):
    record["verified"] = 1
    updatelog(record)
  #  print("check auth...OK")


#nao implementado
def check_multicast(record):
  print("check multicast") 
  record["verified"] = 1
  updatelog(record)

#nao implementado
def check_vlans(record):
  print("check vlans")  
  record["verified"] = 1
  updatelog(record)

def check_veip(record):
  try:
    veip_vlans = check_veip_vlans(record)
    #verifica se as vlans no veip esta corretas
    if(record["vlan_pppoe"] in veip_vlans and record["vlan_dhcp"] in veip_vlans):
      record["verified"] = 1
      updatelog(record)
      print("check veip...OK") 
    else:
      #tenta adicionar o veip novamente caso nao existam ou estejam incorretas  
      add_veip(record, True)  
  except Exception as e:
    errorlog('Falha em chck_veip: '+ str(e))    


'''
# Regra de planos
#
# Fiberhome para planos de 50MB ou inferiores
# F660 para planos maiores que 50MB e menores que 100MB
# F670 para planos de 100MB ou superiores

def plan_rule(model, plan):
    valid = None
    if('670' in model and int(plan) > 50 and int(plan) < 100):
        valid = 'F660'

    if('660' in model and int(plan) >= 100):
        valid = 'F670'

    if(('670' in model or '660' in model) and int(plan) <= 50):
        valid = 'FIBERHOME'

    return valid
'''    

def plan_rule(model, plan, username):

    '''
    Regras ZTE				
    ==========
    Cliente	    Plano	    ONU Nova	ONU Antiga	Acao
    Novo*	    50	        F660	    null	    Provisionar
    Antigo**	50	        F660	    F660	    Provisionar
    Todos	    >50 & <100	F660	    Todos	    Provisionar
    Todos	    >=100	    F670	    Todos	    Provisionar
    
    * uma alocacao
    ** mais de uma alocacao
    '''
    valid = None
    f660 = '660' in model
    f670 = '670' in model

    if(not f660 and not f670):
        return None

    if(int(plan) > 50 and int(plan) < 100):
        if(f670):
            valid = 'F660'
        if(f660):
            return None    

    if int(plan) >= 100:
        if(f660):
            valid = 'F670'
        if(f670):
            return None    

    if valid == None:
        onus = get_onus_alocadas(username)
        #Novo
        if(len(onus) <= 1):
            if(f670):
                valid = 'F660'
        #Antigo
        else:
            anterior = onus[0]['descricao']
            if(f670):
                if '660' in anterior:
                    valid = 'F660'
            if(f660):
                if '670' in anterior:
                    valid = 'F670'
                elif not '660' in anterior:    
                    valid = 'FIBERHOME'

    return valid


def gravalog(item, task, msg, erro):
  try:
    fields = ['script', 'patrimonio', 'serial', 'mac', 'username', 
    'olt', 'olt_ip', 'placa', 'porta', 'vlan_dhcp', 'vlan_pppoe', 'vlan_iptv', 'porta_onu', 'onu_id']
    
    log = {}    
    
    for field in fields:
      if(field in item.keys()):
        log[field] = item[field]
      else:
        log[field] = None
    
    log["script"] = scriptname
    log['msg'] = msg
    log['erro'] = erro
    log['task'] = task
    insertlog(log)
  except Exception as e:
    errorlog('Falha em gravalog: '+ str(e))  

#adiciona vlans no veip
def add_veip(item, check):
  try:
    vlans = []
    vlans.append({
        "vlanid": item["vlan_pppoe"],
        "serviceid": 2,
        "profileid": TR069_PROFILEID
    })
    vlans.append({
        "vlanid": TR069_DHCP_VLANID,
        "serviceid": 1,
        "profileid": TR069_PROFILEID
    })

    if('onu_id' in item):
      veip_addvlans(item, vlans)
    
    item['vlan_dhcp'] = TR069_DHCP_VLANID
    item["vlan_iptv"] = None

    #se estiver checando a configuracao, incrementa o contador de tentativas e a ultima checagem
    if(check):
      item["retries"] = int(item["retries"]) + 1
      item["last_retry"] = dt.now()
      updatelog(item)
      print("check veip...retry")   
    else:
      #caso contrario, grava apenas o log
      msg = "Adicionando Vlans VEIP [DHCP id:{} - PPPoE id: {}]".format(item['vlan_dhcp'], item["vlan_pppoe"])
      gravalog(item, 'VEIP', msg, 0)
      print(msg)
  except Exception as e:
    errorlog('Falha em add_veip: '+ str(e))    
  
def add_vlans(item):
  try:
    #remove todas as vlans
    onuremovevlans(item, 4)
    msg = "Removendo Vlans existentes"
    gravalog(item, 'REMOVE_VLANS', msg, 0)
    print(msg)
    
    #configura vlan pppoe na porta 1
    onuaddvlanunicast(item, 1, item["vlan_pppoe"])  
    msg = "Configurando VLAN PPPoE na porta 1 [id: {}]".format(item["vlan_pppoe"])
    item["porta_onu"] = 1
    vlan_iptv = item["vlan_iptv"]
    item["vlan_iptv"] = None
    item["vlan_dhcp"] = None
    gravalog(item, 'VLANS', msg, 0)
    print(msg)

    #configura vlan unicast iptv na porta 2
    item["vlan_iptv"] = vlan_iptv
    onuaddvlanunicast(item, 2, item["vlan_iptv"])  
    msg = "Configurando VLAN Unicast IPTV na porta 2 [id: {}]".format(item["vlan_iptv"])
    item["vlan_pppoe"] = None
    item["vlan_dhcp"] = None
    item["porta_onu"] = 2
    gravalog(item, 'VLANS', msg, 0)
    print(msg)
  except Exception as e:
    errorlog('Falha em add_vlans: '+ str(e))  

def add_multicast(item):
  try:  
    #configura vlan multicast na porta 2  
    onuaddvlanmulticast(item, 2, 2)  
    msg = "Configurando VLAN Multicast na porta 2 [id: 2]"
    item["porta_onu"] = 2
    item["vlan_iptv"] = 2
    item["vlan_pppoe"] = None
    item["vlan_dhcp"] = None

    gravalog(item, 'MULTICAST', msg, 0)
  except Exception as e:
    errorlog('Falha em add_multicast: '+ str(e))  

'''
def auth_zte(item):
  try:
    manager = ZTEManager(item['olt_ip'], False)
    if(manager.connect()):
      #autoriza a onu
      msg = 'Autorizando ONU {}'.format(item["serial"])
      gravalog(item, 'AUTH_ZTE', msg, 0)
      print(msg)
      manager.auth(item['serial'], item['modelo'], item['username'])

      #configura a onu
      item['vlan_dhcp'] = TR069_DHCP_VLANID
      vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])
      item["vlan_pppoe"] = int(vlans['vlan_pppoe'])
      item["vlan_iptv"] = int(vlans['vlan_iptv'])
      msg = "Configurando ONU [DHCP id:{} - PPPoE id: {}]".format(item['vlan_dhcp'], item["vlan_pppoe"])
      gravalog(item, 'CONFIG_ZTE', msg, 0)
      print(msg)
      updatedevice(item)
      manager.config_onu(item['serial'], item['vlan_dhcp'], item['vlan_pppoe'], item['vlan_iptv'], 2)
      manager.disconnect()
      #return {"status": "SUCCESS", "result": result}
  except Exception as e:
    errorlog('Falha em auth_zte: '+ str(e))        
'''

def start_provision(item):
 # try:
    if(item['olt_modelo'] == 'ZTE'):
      return False
    #atualiza o status da onu
    item["status"] = "PROVISIONING"
    update_unauthorized_item(item)
    print('ONU sera provisionada {}'.format(item['serial']))

    

    pausa = 1
    aviso = ""

    try:
      #carrega os dados via banco para atualizar os dados tecnicos no ixc

      payload = get_dados_tecnicos(item['serial'], item['olt_ip'], '0-0-{}-{}'.format(item["placa"], item["porta"]))
      print(payload)
      if(payload):
        if('id' in payload and payload['id'] != None):
          #atualizar os dados tecnicos no ixc via api
          msg = '[{}] Atualizando dados tecnicos no IXC {} '.format(scriptname, item["serial"])
          print(msg)
          response = update_dados_tecnicos(payload)
          response = json.loads(response) 
          print(response)
          if('type' in response):
            if(response['type'] == 'error'):
              print('Atualizando dados tecnicos no IXC {}..ERRO'.format(item["serial"]))
              #return False
            else:
              msg = 'Atualizando dados tecnicos no IXC'.format(item["serial"])
              gravalog(item, 'IXC', msg, 0)
          else:
            print('Atualizando dados tecnicos no IXC {}..ERRO'.format(item["serial"]))
            #return False
      else:
        print('Atualizando dados tecnicos no IXC {}..ERRO'.format(item["serial"]))
        #return False

    except:
      pass 

    if('ZTE' in item['serial'].upper()):
      if(item['olt_modelo'] == 'FBT'):
        item['modelo'] = 'GPON CURRENCY SFU'
      item['tr069'] = 1
    else:
      item['tr069'] = 0  

    # autoriza a onu
    

    # TODO Remover a ONU das OLTS ZTE
    # remove a onu de todas olts
    olts = unm_oltlist()
    
    for olt in olts:
      removewhitelist(olt["ip"], item["serial"], False)

    # desautoriza todas as onus nomeadas com o mesmo username, exceto a nova
    provisions = findauthorizedonu(None, item["username"])
    for provision in provisions:
      if(provision['serial'].upper() != item["serial"].upper()):
        removewhitelist(provision['olt_ip'], provision['serial'], False)
    ##################################################################  

    #remove do ACS se for ZTE:
    try:
      if (item['tr069'] == 1):
        removeacs(item['mac'])
    except:
      msg = 'Erro ao remover a ONU {} do genie. Verique se o servico genieacs-nbi esta em execucao no servidor do genie ACS'.format(item["serial"])
      gravalog(item, 'REMOVEACS', msg, 0)
      pass

    response = onuauthorize(item)
    if (response['error'] == False):
      time.sleep(pausa)
      msg = 'Autorizando ONU {}'.format(item["serial"])
      gravalog(item, 'AUTHORIZATION', msg, 0)
      
      #desabilita o port isolation
      onu = findauthorizedonu(item["serial"])
      if(onu):
        if('onuid' in onu):
          msg = 'Desabilitando Port Isolation {}'.format(item["serial"])
          change_port_isolation(onu, 'disable')  
          gravalog(item, 'PORT_ISOLATION', msg, 0)

        # Se for compativel com tr069 adiciona as vlans no veip (dhcp e pppoe)
        vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])
        onu['vlan_pppoe'] = int(vlans['vlan_pppoe'])
        onu['vlan_iptv'] = int(vlans['vlan_iptv'])

        onu["patrimonio"] = item["patrimonio"]
        onu["mac"] = item["mac"]
        onu["username"] = item["username"]
        onu["olt"] = item["olt"]
        if('onuid' in onu):
          onu["onu_id"] = onu["onuid"]
          item["onu_id"] = onu["onuid"]
        item['vlan_pppoe'] = int(vlans['vlan_pppoe'])
        item['vlan_iptv'] = int(vlans['vlan_iptv'])
      
        #TESTE############
        #item["tr069"] = 1 
        ##################

        if (item['tr069'] == 1):
          add_veip(onu, False)  
          
          
          #verifica se o modo de operacao da onu esta em bridge
          #e adiciona a vlan pppoe na porta 1 e vlan iptv unicast no veip
          
          operation_mode = getoperationmode(item["username"])
          if(operation_mode=='bridge'):
            

            #configura vlan iptv unicast no veip
            vlans = []
            vlans.append({
              "vlanid": item["vlan_iptv"],
              "serviceid": 3,
              "profileid": TR069_PROFILEID
            })
            
            if('onu_id' in item):
              veip_addvlans(item, vlans)
              msg = "Adicionando Vlans VEIP [IPTV Unicast id:{}]".format(item['vlan_iptv'])
              gravalog(item, 'VEIP', msg, 0)
              print(msg)  

            #configura vlan pppoe na porta 1
            onuaddvlanunicast(item, 1, item["vlan_pppoe"])  
            msg = "Configurando VLAN PPPoE na porta 1 [id: {}]".format(item["vlan_pppoe"])
            item["porta_onu"] = 1
            
            item["vlan_iptv"] = None
            item["vlan_dhcp"] = None
            gravalog(item, 'VLANS', msg, 0)
            print(msg)  
          
        else:
          add_vlans(item)
        
        add_multicast(item)  

        msg = '[{}] ONU {} autorizada'.format(scriptname, onu["serial"])
        print(msg)

        #Captura o sinal da onu e grava no banco

        #if(onu):
        #  print(onu)
        #  if('onuid' in onu):
        #    manager = FHManager(onu['olt_ip'], False)
        #    if(manager.connect()):
        #        signal = manager.get_signal(
        #            onu['placa'], onu['porta'], onu['onuid'], onu['serial'], onu['nome'])
        #        signal['comentario'] = 'Provisionamento'
        #        print(signal)
        #        insert_signal_noc(signal)

        #        manager.disconnect()

    else:
      msg = response['data'].encode('ascii', 'ignore')
      gravalog(item, 'AUTHORIZATION', msg, 1)
  #except Exception as e:
  #  errorlog('Falha em start_provision: '+ str(e))    


#verifica se as onus possuem todos os dados para iniciar o provisionamento
def check_onus(onus):
  try:
    if(onus):
      onus = json.loads(onus)

      for onu in onus:

        if(((len(ONUS_TESTE) > 0) and (onu['serial'].upper() in ONUS_TESTE) and (TEST_MODE)) or (TEST_MODE == False)):
          
          #TESTE############
          #onu = dict(onu)
          #onu["patrimonio"] = 1 
          #onu["modelo"] = 'AN5506-04-F1' 
          #onu["username"] = "fibrateste2"
          #onu["login_ativo"] = "S"
          #onu["mac"] = "18:A3:E8:F2:FD:88"
          ##################
          missing_params = []

          # onu nao pode estar na lista para ignorar
          if(checkignorelist(serial=onu['serial'])):
            missing_params.append('IGNORE')

          # obrigatorio patrimonio
          if(not ('patrimonio' in onu and onu['patrimonio'])):
              missing_params.append('PATRIMONIO')

          # Libera apenas se tiver o modelo e nao esteja na tabela ignore com o parametro modelo=1
          if(not('modelo' in onu and onu['modelo'])and not(checkignorelist(serial=onu['serial'], modelo=1))):
          #if(not('modelo' in onu and onu['modelo'])):
              missing_params.append('MODELO')

          # obrigatorio username
          if(not('username' in onu and onu['username'])):
              missing_params.append('USERNAME')

          # obrigatorio mac
          if(not('mac' in onu and onu['mac'])):
              missing_params.append('MAC')

          # obrigatorio login ativo
          if(not('login_ativo' in onu and onu['login_ativo'] == 'S')):
              missing_params.append('LOGIN ATIVO')

          # obrigatorio pacote wifi para onus zte que nao estejam na lista para ignorar
          if('username' in onu and onu['username'] and 'ZTE' in onu['serial'].upper() and not(checkignorelist(serial=onu['serial'], pacote_wifi=1)) and not(checkignorelist(username=onu['username'], pacote_wifi=1)) and not(get_pacotewifi(onu['username']))):
            missing_params.append('PACOTE WIFI')

          # Libera apenas se tiver a alocacao do comodato e nao esteja na tabela ignore com o parametro comodato=1
          if(not('id_comodato' in onu and onu['id_comodato']) and not(checkignorelist(serial=onu['serial'], comodato=1)) and not(checkignorelist(username=onu['username'], comodato=1))):
            missing_params.append('ALOCACAO COMODATO')

          # verifica se o modelo da onu e compativel com o plano.
          if('plano' in onu and 'modelo' in onu and 'username' in onu and onu['username'] and not(checkignorelist(serial=onu['serial'], plano=1)) and not(checkignorelist(username=onu['username'], plano=1))):
            #pending = plan_rule(onu['modelo'], onu['plano'])
            pending = plan_rule(onu['modelo'], onu['plano'], onu['username'])
            if(pending):
              missing_params.append('ONU INCOMPATIVEL. NECESSARIO {}'.format(pending))


          if(len(missing_params) == 0):
              start_provision(dict(onu))
          else:
            if(IGNORE_LIST_ENABLED):
              if(onu['status'] != 'IGNORED'):
                onu['status'] = 'FALTANDO: {}'.format(
                                    ', '.join(missing_params))
                update_unauthorized_item(onu)
            else:
              onu['status'] = 'FALTANDO: {}'.format(
                                    ', '.join(missing_params))
              update_unauthorized_item(onu)
  except Exception as e:
    errorlog(traceback.format_exc())
    raise
    #errorlog('Falha em check_onus: '+ str(e))           

notify_start()    
