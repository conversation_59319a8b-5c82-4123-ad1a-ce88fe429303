from db import *
import uuid
from worker_config_queue import conn
from rq import Queue, Retry
from rq.job import Job
from tasks_fiberhome import *

q = Queue('config_queue', connection=conn) 

def manage_vlan_rewrite():

    params = {
        'serial': 'ZTEGCC4D3ACC'
    }

    if "serial" in params:
        serial = params['serial']
        onu = getonu(serial=serial)
        jobs = []

        if(onu):
            #se a onu estiver autorizada em mais de uma interface sera necessario informar placa/porta
            if(len(onu)>1):
                if all (k in params for k in ("placa","porta")):
                    onu = getonu(serial=serial, placa=placa, porta=porta)
                    if(onu == None):
                        return {'status' : 'ERROR', 'msg': "ONU not found"}
                    else:
                        onu = onu[0]    
                else:
                    return {'status' : 'ERROR', 'msg': "Missing params: placa, porta"}
            else:
                onu = onu[0]    
            
            #consulta as vlans
            vlans = getvlans(onu['olt'], onu['placa'], onu['porta'])
            if(vlans == None):
                return {'status' : 'ERROR', 'msg': "Missing vlan information"}

            if(onu['olt_modelo'] == 'ZTE'):
                return {'status' : 'ERROR', 'msg': "Not implemented yet"}

            # Se for compativel com tr069 adiciona as vlans no veip (dhcp e pppoe)
            if('ZTE' in onu['serial'].upper()):
                onu['vlan_id'] = int(vlans['vlan_pppoe'])
                onu["service_id"] = 2

                #adiciona vlan pppoe no veip
                job_id = str(uuid.uuid4())
                job_veip_pppoe = q.enqueue(task_addveip, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                jobs.append(job_id)

                onu['vlan_id'] = os.getenv('TR069_DHCP_VLANID')
                onu["service_id"] = 1

                #adiciona vlan dhcp no veip
                job_id = str(uuid.uuid4())
                job_veip_dhcp = q.enqueue(task_addveip, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                jobs.append(job_id)

                onu['vlan_id'] = 2
                onu['vlan_port'] = 2
                onu['vlan_type'] = 'multicast'

                #adiciona vlan iptv multicast na porta 2
                job_id = str(uuid.uuid4())
                job_vlan_multicast = q.enqueue(task_addvlan, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                jobs.append(job_id)

            else:
                #remove as vlans
                job_id = str(uuid.uuid4())
                job_removevlans = q.enqueue(task_removevlans, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                jobs.append(job_id)

                onu['vlan_id'] = int(vlans['vlan_pppoe'])
                onu['vlan_port'] = 1
                onu['vlan_type'] = 'unicast'

                #adiciona vlan pppoe na porta 1
                job_id = str(uuid.uuid4())
                job_vlan_pppoe = q.enqueue(task_addvlan, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                jobs.append(job_id)                                

                onu['vlan_id'] = int(vlans['vlan_iptv'])
                onu['vlan_port'] = 2
                onu['vlan_type'] = 'unicast'

                #adiciona vlan iptv unicast na porta 2
                job_id = str(uuid.uuid4())
                job_vlan_iptv = q.enqueue(task_addvlan, args=([job_id]), kwargs=(onu), retry=Retry(max=5), depends_on=job_removevlans, job_id=job_id)
                jobs.append(job_id)

                onu['vlan_id'] = 2
                onu['vlan_port'] = 2
                onu['vlan_type'] = 'multicast'
                
                #adiciona vlan iptv multicast na porta 2
                job_id = str(uuid.uuid4())
                job_vlan_multicast = q.enqueue(task_addvlan, args=([job_id]), kwargs=(onu), retry=Retry(max=5), depends_on=job_removevlans, job_id=job_id)
                jobs.append(job_id)

            return {'status': 'OK', 'msg' : jobs}     
        else:
            return {'status' : 'ERROR', 'msg': "ONU not found"}
    else:
        return {'status' : 'ERROR', 'msg': "Missing params: {}".format(', '.join(set(required_params).difference(set(params))))}


print(manage_vlan_rewrite())