[all:vars]
ansible_connection=ssh
ansible_user=root

[localhost]
127.0.0.1

[srv-provision]
************

[srv-provision:vars]
mac0_device=macger
mac0_devicetype=macvlan
mac0_type=macvlan
mac0_bootproto=static
mac0_macvlan_mode=bridge
mac0_ipaddr="************"
mac0_prefix=24
mac0_gateway="***********"
mac0_dns1="***********"
mac0_dns2="***********"
registry_url="https://docker-registry.telemidia.net.br"
registry_username="reginaldo.bert<PERSON><PERSON>"
registry_password="Mc@)2)5)9"

[redis]
************

[redis:vars]
ansible_connection=ssh
ansible_user="reginaldo.bertolucci"

[provision]
************

[provision:vars]
container=provision
ansible_connection=ssh
ansible_user="reginaldo.bert<PERSON><PERSON>"
git_repo = "git.telemidia.net.br/Telemidia/provision.git"
git_username="reginaldo.bert<PERSON><PERSON>"
git_password="Mc@)2)5)9"
redis_host=************

