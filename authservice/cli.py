#!/usr/bin/python
# -*- coding: utf-8 -*-

import pexpect
import sys
from database import *
from config import *
import re
import time
from tl1 import *

def veip_addvlans(onu, vlans):
  if('onu_id' in onu):
    #onu = findauthorizedonu(serial)
    child = pexpect.spawn('telnet {}'.format(onu['olt_ip']))
    
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', 5)
    child.sendline(CLI_USER)
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('User> ', 5)
    child.sendline("enable")
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('Admin# ', 5)
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', 5)
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if(rp1000):
        child.sendline('cd onu/lan')
        child.expect('lan#', 5)
        expectstr = 'lan#'
    else:
        child.sendline('cd epononu')
        child.expect('epononu# ', 5)
        child.sendline('cd qinq')
        child.expect('qinq#', 5)
        expectstr = 'qinq#'
    #child.sendline('cd epononu')
    #child.expect('epononu# ', 5)
    #child.sendline('cd qinq')
    #child.expect('qinq#', 5)

    for vlan in vlans:
        child.sendline(
            'set epon slot {} pon {} onu {} port 1 onuveip {} 33024 {} 65535 33024 65535 65535 33024 65535 65535 0 {} 65535'.format(onu['placa'], onu['porta'], onu['onu_id'], vlan["serviceid"], vlan["vlanid"], vlan["profileid"]))
        # child.expect('qinq#', 5)
        child.expect(expectstr, 5)

    child.close()

def check_veip_vlans(onu):
    onu = findauthorizedonu(onu["serial"])
    child = pexpect.spawn('telnet {}'.format(onu['olt_ip']))

    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', 5)
    child.sendline(CLI_USER)
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('User> ', 5)
    child.sendline("enable")
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('Admin# ', 5)

    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', 5)
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################

    child.sendline('cd service')
    child.expect('service# ', 5)
    child.sendline('terminal length 0')
    child.expect('service# ', 5)
    child.sendline('cd ..')
    child.expect('Admin# ', 5)

    if (rp1000):
        child.sendline('cd onu')
        child.expect('onu#', 5)
        child.sendline('cd lan')
        expectstr = 'lan#'
    else:
        child.sendline('cd epononu')
        child.expect('epononu#', 5)
        child.sendline('cd qinq')
        expectstr = 'qinq#'

    child.expect(expectstr, 5)

    child.sendline('show epon slot {} pon {} onu {} onuveip servindex'.format(
        onu['placa'], onu['porta'], onu['onuid']))
    child.expect(expectstr, 5)
    ret = ""
    ret += child.before
    child.close()

    if (rp1000):
        p = re.compile(
            "Cvlan                   : (.*)")
        result = map(int, p.findall(ret))
    else:
        p = re.compile(
            "cvlan 33024 (.*) 65535 tvlan 33024 65535 65535 svlan 33024 65535 65535 tls 0 servmode 2 svlan 65535  servdiff 65535")
        result = map(int, p.findall(ret))

    return result


def change_port_isolation(onu, option):
  child = pexpect.spawn('telnet {}'.format(onu['olt_ip']))

  # Habilita o debug (retorno na tela)
  #child.logfile = sys.stdout
  child.expect('Login: ', 5)
  child.sendline(CLI_USER)
  child.expect('Password: ', 5)
  child.sendline(CLI_PASS)
  child.expect('User> ', 5)
  child.sendline("enable")
  child.expect('Password: ', 5)
  child.sendline(CLI_PASS)
  child.expect('Admin# ', 5)

  #child.sendline('cd gpononu')
  #child.expect('gpononu# ', 5)

  # verifica se e rp1000
  child.sendline('dir')
  child.expect('Admin# ', 5)
  ret = ""
  ret += child.before
  rp1000 = ret.find('<DIR>') != -1
  ######################
  if (rp1000):
    child.sendline('cd onu')
    expectstr = 'onu#'
    cmdstr = 'set port_separate slot {} pon {} onu {} separate {}'
  else:
    child.sendline('cd gpononu')
    expectstr = 'gpononu#'
    cmdstr = 'set port_separate slot {} link {} onu {} separate {}'

  child.expect(expectstr, 5)

  #child.sendline('set port_separate slot {} link {} onu {} separate {}'.format(onu['placa'], onu['porta'], onu['onuid'], option))
  #child.expect('gpononu#', 5)
  child.sendline(cmdstr.format(onu['placa'], onu['porta'], onu['onuid'], option))
  child.expect(expectstr, 5)
  child.close()

def gponsignal(serial):
  try:      
    onu = findonu(serial)
    child = pexpect.spawn('telnet {}'.format(onu['olt_ip']), encoding='utf-8')
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', 5)
    child.sendline(CLI_USER)
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('User> ', 5)
    child.sendline("enable")
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('Admin# ', 5)
    child.sendline('cd gpononu')
    child.expect('gpononu# ', 5)

    #Informacoes da ONU (campo OLT RECV POWER nao e mais utilizado)
    child.sendline('show optic_module slot {} link {} onu {}'.format(onu['placa'], onu['porta'], onu['onuid']))
    child.expect(r'Admin\\gpononu# ', 5)
    data = child.before.splitlines()

    result = {}
    for item in data[4:]:
       record = item.split(':')
       result[record[0].strip()] = record[1].replace('\t', ' ').strip()

    child.sendline('clear')

    #Este comando lista todas as ONUs sendo necessario filtrar pelo ID
    child.sendline('cd gponlinecard')
    child.expect('gponline# ', 5)
    child.sendline('show optic_module_par slot {} link {}'.format(onu['placa'], onu['porta']))

    ret=""
    while True:
      i = child.expect(['Press any key to continue', 'gponline# '], 5)
      ret += child.before
      if i == 0:
        child.send('a')
      else:
        break    

    data = ret.splitlines()
    onus = {}

    for s in data:
      if (s.find('(Dbm)') != -1): 
        values = s.split('\t')  
        s1 = values[0]
        s2 = values[1]

        # Filtra os cacos da string
        # Exemplo: '  -- Ctrl+c to stop-- \x00\x1b[2J\x1b[1;74HMaster\x1b[2;1H\x00\x00\x00\x00\x00\x00\x005', '-20.36', '(Dbm)'
        i = s1.rfind('\x00') 
    
        if(i != -1):
          onus[s1[i+1:len(s1)]] = s2 
        else:
          if(s1.find('SEND POWER')) == -1:
            onus[s1] = s2


    child.sendline('clear')
    child.sendline('cd gpononu')
    child.expect('gpononu# ', 5)
    # Distancia da ONU
    child.sendline('show rtt_value slot {} link {} onu {}'.format(onu['placa'], onu['porta'], onu['onuid']))
    child.expect(r'Admin\\gpononu# ', 5)
    data = child.before.splitlines()
    record = data[1].split('=')
    if(record[0].find('onu is in unactive') == -1):
      result[record[0].strip()] = record[1].strip()

    #Substitui o campo OLT RECV POWER pelo sinal novo
    result['OLT RECV POWER'] = onus.get(str(onu["onuid"]), "ERRO") + ' (Dbm)'
    #pior sinal da porta
    result['MIN OLT RECV POWER'] = onus.get(max(onus, key=onus.get))
    #melhor sinal da porta
    result['MAX OLT RECV POWER'] = onus.get(min(onus, key=onus.get))
    #media sinal da porta
    filtered_vals = [float(v) for _, v in onus.items() if v != 0]
    average = sum(filtered_vals) / len(filtered_vals)
    result['AVG OLT RECV POWER'] = ("%.2f" % average)
    # total de onus na porta
    result['TOTAL ONUS PON'] = len(onus)

    child.close()
    return result
  except:
    return False   

def checkwhitelist(ip_olt, serial):
  try:   
    child = pexpect.spawn('telnet {}'.format(ip_olt))
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', 5)
    child.sendline(CLI_USER)
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('User> ', 5)
    child.sendline("enable")
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('Admin# ', 5)
  # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', 5)
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if (rp1000):
      child.sendline('cd onu')
      expectstr = 'onu#'
    else:
      child.sendline('cd gpononu')
      expectstr = 'gpononu#'

    child.expect(expectstr, 5)
    
    #padrao physicid = quatro primeiros caracteres maiusculos e o restante minusculo
    serial = serial[:4].upper()+serial[4:].lower()
    child.sendline('show onu-authinfo phy-id {}'.format(serial))
    child.expect(expectstr, 5)
    data = child.before.replace(' ', '')
    if(data.find('Unknowncommand') != -1):
      #caso a olt não possua o comando onu-authinfo, executar o comando a seguir
      child.sendline('show whitelist phy_addr select address {}'.format(serial))
      child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
      data = child.before
      #11    15    32  AN5506-SFU     Auth   ZTEGcced73e9
      match = re.search(r"([-+]?\d*\.\d+|\d+) (.*) ([-+]?\d*\.\d+|\d+) (.*) (.*) (.*)", data)
      if match:
        return True
      else:
        return False  
    else:
      if(rp1000):
        if("Onu is not authcated" in data):
          return False
        else:
          return "ONU:" in data
      else:  
        return "Auth-Status:Authed" in data
  except:
    return False  

def removewhitelist(olt, serial, utf8):
  try:   
    if(utf8):
      child = pexpect.spawn('telnet {}'.format(olt), encoding='utf-8')
    else:  
      child = pexpect.spawn('telnet {}'.format(olt))
    #child = pexpect.spawn('telnet {}'.format(olt))
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', 5)
    child.sendline(CLI_USER)
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('User> ', 5)
    child.sendline("enable")
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('Admin# ', 5)
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', 5)
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if (rp1000):
      child.sendline('cd onu')
      expectstr = 'onu#'  
    else:
      child.sendline('cd gpononu')
      expectstr = 'gpononu#'

    child.expect(expectstr, 5)
 #   child.sendline('cd gpononu')
    
  #  child.expect('gpononu# ', 5)

    #padrao physicid = quatro primeiros caracteres maiusculos e o restante minusculo
    serial = serial[:4].upper()+serial[4:].lower()

    child.sendline('set whitelist phy_addr address {} password null action delete'.format(serial))
    #child.expect(r'Admin\\gpononu# ', 5)
    child.expect(expectstr, 5)
    data = child.before.replace(' ', '')
#    return "setonuwhitelistok" in data
    result = 'success' in data or 'setonuwhitelistok' in data
    return result
  except:
    return False  


def checkblacklist(serial):
  try:   
    onu = findonu(serial)
    if(onu == None):
      return False      
    ip_olt = onu['olt_ip']
    placa = onu['placa']

    child = pexpect.spawn('telnet {}'.format(ip_olt))
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', 5)
    child.sendline(CLI_USER)
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('User> ', 5)
    child.sendline("enable")
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('Admin# ', 5)
    child.sendline('cd service')
    
    child.expect('service# ', 5)
    
    child.sendline('telnet slot {}'.format(placa))
    child.expect('GCOB# ', 5)
    
    child.sendline('cd omci')
    child.expect('omci# ', 5)

    child.sendline('show exception_detect status')
    ret=""
    while True:
      i = child.expect(['Press any key to continue', 'omci# '], 5)
      ret += child.before
      if i == 0:
        child.send('a')
      else:
        break    

    # Verifica se a blacklist está vazia
    if(ret.find('Total result number = 0') != -1):
      return False    

    data = ret.splitlines()

    # verifica se o padrao do retorno e com linhas e colunas ou somente um registro
    
    # padrao apenas um registro
    if(data[5].replace(' ', '').find('PON=') == 0):
      values = {}    
      blacklist = {}
      for s in data[5:]:
        if (s.find('The parameter description:') == -1): 
          if(s != ''):
            s1 = s.replace(' ', '').split('=')
            values[s1[0]] = s1[1]
            
        else:
          break  
      blacklist[values['ONU_SN'].replace('-', '')] = {
              'PON' : values['PON'],
              'ONU_ID' : values['ONU_ID'],
              'ONU_SN' : values['ONU_SN'],
              'TIME' : values['TIME'],
              'DETECT_NUM' : values['DETECT_NUM'],
              'FLAG' : values['FLAG']
      }   

      if(serial in blacklist):       
        return(blacklist[serial])              
      else:
        return False
        
    # padrao linhas e colunas   
    if(data[5].replace(' ', '').find('PONONU') == 0):
      blacklist = {}
      for s in data[6:]:
        if (s.find('The parameter description:') == -1): 
          if(s != ''): 
            s1 = (re.sub(' +', ' ', s))
            values = s1.split(' ')
            blacklist[values[2].replace('-', '')] = {
              'PON' : values[0],
              'ONU_ID' : values[1],
              'ONU_SN' : values[2],
              'TIME' : values[3],
              'DETECT_NUM' : values[4],
              'FLAG' : values[5]
            }  
        else:
          break    
      if(serial in blacklist):       
        return(blacklist[serial])              
      else:
        return False   
  except:
    return False           

def removeblacklist(serial):
  try:
    onu = findonu(serial)
    if(onu == None):
      return False      
    ip_olt = onu['olt_ip']
    placa = onu['placa']
    porta = onu['porta']

    child = pexpect.spawn('telnet {}'.format(ip_olt))
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', 5)
    child.sendline(CLI_USER)
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('User> ', 5)
    child.sendline("enable")
    child.expect('Password: ', 5)
    child.sendline(CLI_PASS)
    child.expect('Admin# ', 5)
    child.sendline('cd service')
    
    child.expect('service# ', 5)
    
    child.sendline('telnet slot {}'.format(placa))
    child.expect('GCOB# ', 5)
    
    child.sendline('cd omci')
    child.expect('omci# ', 5)   

    sn = serial[:4] + '-' + serial[4:]

    child.sendline('set exception_detect list ponno {} onusn {} white'.format(porta, sn))
    ret=""
    child.expect('omci# ', 5)
    ret += child.before

    if (ret.find('failed') != -1): 
      return 'ONU não está autorizada nesta placa/porta'
    else:
      return True  
  except:
    return False


