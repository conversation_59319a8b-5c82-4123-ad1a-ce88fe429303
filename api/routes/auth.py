
from flask import request, jsonify, render_template, Blueprint
import json
from db import *
import tasks_fiberhome
import tasks_zte
from rq import Queue, Retry
from rq.job import Job
from worker_config_queue import conn
from dotenv import load_dotenv
import os
from models import token_required

auth_route = Blueprint('auth_route', __name__, template_folder='templates')
q = Queue('config_queue', connection=conn) 

@auth_route.route('/auth', methods=['POST'])
@token_required
def manage_auth():
    if(request.form):
        params = request.form
    else:
        if(request.data):
            params = json.loads(request.data.decode())
        else:
            params = []
    
    required_params = ("olt","serial", "placa", "porta", "type", "name")

    if all (k in params for k in required_params):
        olt = params['olt']
        serial = params['serial']
        olt_type = olt_model(olt)
        q = Queue('config_queue', connection=conn) 
        if(olt_type=='FBT'):
            job_auth = q.enqueue(tasks_fiberhome.task_authorization, kwargs=(params), retry=Retry(max=5))
            return jsonify({'status': 'OK', 'msg' : job_auth.get_id()}) 
        elif(olt_type=='ZTE'):
            job_auth = q.enqueue(tasks_zte.task_authorization, kwargs=(params), retry=Retry(max=5))
            return jsonify({'status': 'OK', 'msg' : job_auth.get_id()}) 
    else:
        return jsonify({'status' : 'ERROR', 'msg': "Missing params: {}".format(', '.join(set(required_params).difference(set(params))))})

@auth_route.route('/deauth', methods=['POST'])
@token_required
def manage_deauth():
    if(request.form):
        params = request.form
    else:
        if(request.data):
            params = json.loads(request.data.decode())
        else:
            params = []
    
    required_params = ("olt", "serial")

    if all (k in params for k in required_params):
        olt = params['olt']
        olt_type = olt_model(olt)
        q = Queue('config_queue', connection=conn) 

        if(olt_type=='FBT'):
            job_deauth = q.enqueue(tasks_fiberhome.task_deauthorize, kwargs=(params), retry=Retry(max=5))
            return jsonify({'status': 'OK', 'msg' : job_deauth.get_id()}) 
        elif(olt_type=='ZTE'):
            job_deauth = q.enqueue(tasks_zte.task_deauthorize, kwargs=(params), retry=Retry(max=5))
            return jsonify({'status': 'OK', 'msg' : job_deauth.get_id()}) 
    else:
        return jsonify({'status' : 'ERROR', 'msg': "Missing params: {}".format(', '.join(set(required_params).difference(set(params))))})
        
'''
@auth_route.route('/deauth', methods=['POST'])
@token_required
def manage_deauth():
    if(request.form):
        params = request.form
    else:
        if(request.data):
            params = json.loads(request.data.decode())
        else:
            params = []
    
    required_params = ("serial", "name")

    if all (k in params for k in required_params):
        serial = params['serial']
        name = params['name']
        olt_type = olt_model(olt)
        q = Queue('config_queue', connection=conn) 

        job_id = str(uuid.uuid4())
        job_deauthorize = q.enqueue(task_deauthorize, args=([job_id]), kwargs=(params), retry=Retry(max=5), job_id=job_id)
        job = job_to_json(job_deauthorize)
        job['description'] = 'DEAUTHORIZE'
        update_job(job)
        return jsonify({'status': 'OK', 'msg' : job_id}) 

    else:
        return jsonify({'status' : 'ERROR', 'msg': "Missing params: {}".format(', '.join(set(required_params).difference(set(params))))})
'''        