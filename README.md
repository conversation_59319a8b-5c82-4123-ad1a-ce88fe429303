## Sistema de provisionamento

**ansible**:
    Contém os scripts ansible para a criação de toda a estrutura do sistema de provisionamento incluindo servidor físico, containers e serviços

**api**
    API REST do provisionamento

**application**
    Serviço de provisionamento que utiliza enfileiramento de jobs para otimização da carga de trabalho

**authservice**
    Sistema legado de provisionamento. Será descontinuado

**confs**
    Arquivo de configuração do gerenciamento de serviços Supervisor

**letsencrypt**
    Certificados do serviço web

**monitor**
    Serviço websocket de monitoramento do provisionamento no NOC


<img src='http://git.telemidia.net.br/Telemidia/provision/raw/branch/master/diagrama.png'>


## Processo de provisionamento detalhado

<img src='http://git.telemidia.net.br/Telemidia/provision/raw/branch/master/processo.png'>




**1. Descoberta**

Um serviço de discovery é executado a cada 2 minutos buscando ONUs não autorizadas em todas as placas e portas de todas as OLTs Fiberhome e a cada 1 minutos em OLTs ZTE

Um serviço lê os alarmes de onus não autorizadas no UNM a cada 30 segundos (apenas OLTs Fiberhome)

**2. Checagem**

Assim que uma ONU é descoberta como não autorizada, é feita uma checagem se esta ONU já está em processo de provisionamento. Caso não esteja ela passa por uma análise prévia verificando se possui todos os parâmetros para ser provisionada

*Regras para uma ONU ser provisionada:*

- Possuir patrimônio cadastrado no IXC
- Patrimônio deve estar alocado para um cliente
- MAC da WAN deve estar cadastrado corretamente no login do cliente
- Login deve estar ativo
- Contrato deve conter um pacote wifi para ONUs ZTE
- O modelo da ONU deve atender as seguintes regras:

    F660: Planos com velocidade > 50 e < 100 
    F670: Planos com velocidade > 100
     
Assim que a checagem da ONU é realizada, caso esteja conforme ela entra em uma fila de provisionamento. Caso não atenda alguma regra, o registro do provisionamento é gravado com o motivo da recusa.

**3. Enfileiramento**

A fila de provisionamento é executada paralelamente por processos distribuídos.

Estes processos são responsáveis por autorizar e configurar as ONUs através de comandos executados nas OLTs por Telnet e/ou SSH

**4. Autorização / Configuração / Validação**

Os processos executam as seguintes tarefas:

- Desautoriza a ONU de todas as OLTs Fiberhome e ZTE
- Autoriza a ONU na OLT (Fiberhome ou ZTE)
- Configura VLANs
- Valida se as configurações foram efetivadas
 

Concluída a configuração na OLT, a onu receberá um IP via DHCP na VLAN 10
Também receberá o valor da URL do servidor ACS (GenieACS) via OPTION 43
IMPORTANTE: É obrigatório que a ONU já possua uma conexão DHCP criada com a vlan 10.
Caso não possua, a configuração não será concluída

A ONU irá tentar se conectar com o ACS e assim que a conexão é estabelecida,
o GenieACS irá finalizar as seguintes configurações:

- Configura o wifi caso a ONU já possua dados de configuração anterior do Wifi
- Configura uma conexão PPPoE
- Desabilita o firewall 



**Reprovisionamento**

Um serviço é executado aguardando solicitações de reprovisionamento.
Caso seja solicitado, a ONU será desautorizada da OLT e seguirá todo o processo de provisionamento descrito acima.

