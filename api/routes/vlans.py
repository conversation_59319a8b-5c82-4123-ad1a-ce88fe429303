
from flask import request, jsonify, render_template, Blueprint
import json
from utils import *
from db import *
from cli import *
import tasks_fiberhome
import tasks_zte
from rq import Queue, Retry
from rq.job import Job
from worker_config_queue import conn
from dotenv import load_dotenv
import os
from models import token_required
import uuid
from fhmanager import *

vlan_route = Blueprint('vlan_route', __name__, template_folder='templates')
q = Queue('config_queue', connection=conn) 

@vlan_route.route('/vlan', methods=['POST'])
@token_required
def manage_vlan():
    if(request.form):
        params = request.form
    else:
        if(request.data):
            params = json.loads(request.data.decode())
        else:
            params = []
    
    required_params = ("olt","serial", "vlan_id", "vlan_type")
    if all (k in params for k in required_params):
        olt = params['olt']
        serial = params['serial']
        olt_type = olt_model(olt)
        if(olt_type=='FBT'):
            onu = onu_list(olt_ip=olt, serial=serial)
            
            if(onu):
                #se a onu estiver autorizada em mais de uma interface sera necessario informar placa/porta
                if(len(onu)>1):
                    if all (k in params for k in ("slot","pon")):
                        return "OK"
                    else:
                        return jsonify(
                            {'status' : 'ERROR', 'msg': "Missing params: slot, pon"})
                else:
                    if(params['vlan_type'] == 'veip'):
                        #verifica se a vlan ja existe
                        vlans = check_veip_vlans(onu[0])

                        for vlan in vlans:
                            if(int(vlan["vlan"]) == int(params["vlan_id"])):
                                return jsonify({'status' : 'ERROR', 'msg': "VLAN already exists"})
                        else:        
                            # monta uma lista apenas com os ids
                            ids = [int(d['index']) for d in vlans]
                            ids.sort()
                            start, end = ids[0], ids[-1]
                            if(start > 1):
                                next_id = 1
                            else: 
                                # atribui os ids faltantes a variavel d
                                d = sorted(set(range(start, end + 1)).difference(ids))
                                # atribui a variavel next_id o primeiro id faltante (caso exista) ou incrementa o end
                                if(len(d) > 0):
                                    next_id = next(iter(d))
                                else:
                                    next_id = end+1
                                params["service_id"] = next_id  

                                job_veip = q.enqueue(tasks_fiberhome.task_addveip, kwargs=(params), retry=Retry(max=5))
                                return jsonify({'status': 'OK', 'msg' : job_veip.get_id()}) 
                    elif(params['vlan_type'] == 'unicast' or params['vlan_type'] == 'multicast'):
                        if('vlan_port' in params):
                            job_vlan = q.enqueue(tasks_fiberhome.task_addvlan, kwargs=(params), retry=Retry(max=5))
                            return jsonify({'status': 'OK', 'msg': job_vlan.get_id()}) 
                        else:
                            return jsonify({'status' : 'ERROR', 'msg': "Missing params: vlan_port"})

                    else:
                        return jsonify({'status' : 'ERROR', 'msg': "VLAN Type invalid: must be [unicast, multicast, veip]"})
            else:
                return jsonify({'status' : 'ERROR', 'msg': "ONU not found in OLT"})
        elif(olt_type=='ZTE'):
            onu = getdevice(serial)
            if(onu):
                job_vlan = q.enqueue(tasks_zte.task_addvlan, kwargs=(params), retry=Retry(max=5))
                return jsonify({'status': 'OK', 'msg': job_vlan.get_id()}) 
            else:
                jsonify({'status' : 'ERROR', 'msg': "ONU not found in OLT"})     

    else:
        return jsonify({'status' : 'ERROR', 'msg': "Missing params: {}".format(', '.join(set(required_params).difference(set(params))))})


@vlan_route.route('/vlan', methods=['DELETE'])
@token_required
def delete_vlan():

    try:

        if(request.form):
            params = request.form
        else:
            if(request.data):
                params = json.loads(request.data.decode())
            else:
                params = []
        
        required_params = ("olt", "serial", "port")
        if not all (k in params for k in required_params):
            raise Exception("Missing params: {}".format(', '.join(set(required_params).difference(set(params)))))
        
        olt = params['olt']
        serial = params['serial']
        port = params['port']

        olt_type = olt_model(olt)
        if not olt_type:
            raise Exception('Unable to determine OLT model')
        
        if olt_type=='FBT':
            params = {
                'olt': olt,
                'serial': serial,
                'port': port,
            }
            job_vlan = q.enqueue(tasks_fiberhome.task_removevlan, kwargs=(params), retry=Retry(max=3))

        elif olt_type=='ZTE':
            onu = getdevice(serial)
            if not onu:
                raise Exception('ONU not found')
        
            params = {
                'olt': onu['olt_ip'],
                'chassi': onu['chassi'],
                'slot' : onu['slot'],
                'pon' : onu['pon'],
                'onuid': onu['onuid'],
                'port': params['port']
            }
            job_vlan = q.enqueue(tasks_zte.task_removevlan, kwargs=(params), retry=Retry(max=3))

        data = {
            'job_id': job_vlan.get_id()
        }

        response = response_success(data=data)
    
    except Exception as e:
        response = response_error(msg=str(e))
    
    return response



@vlan_route.route('/vlan/rewrite', methods=['POST'])
@token_required
def manage_vlan_rewrite():
    try:
        if(request.form):
            params = request.form
        else:
            if(request.data):
                params = json.loads(request.data.decode())
            else:
                params = []
        
        if "serial" in params:
            serial = params['serial']
            onu = getonu(serial=serial)
            jobs = []

            if(onu):
                #se a onu estiver autorizada em mais de uma interface sera necessario informar placa/porta
                if(len(onu)>1):
                    if all (k in params for k in ("placa","porta")):
                        onu = getonu(serial=serial, placa=placa, porta=porta)
                        if(onu == None):
                            return jsonify({'status' : 'ERROR', 'msg': "ONU not found"})
                        else:
                            onu = onu[0]    
                    else:
                        return jsonify(
                            {'status' : 'ERROR', 'msg': "Missing params: placa, porta"})
                else:
                    onu = onu[0]    
                
                #consulta as vlans
                vlans = getvlans(onu['olt'], onu['placa'], onu['porta'])
                if(vlans == None):
                    return jsonify(
                            {'status' : 'ERROR', 'msg': "Missing vlan information"})

                if(onu['olt_modelo'] == 'ZTE'):
                    return jsonify(
                            {'status' : 'ERROR', 'msg': "Not implemented yet"})

                # Se for compativel com tr069 adiciona as vlans no veip (dhcp e pppoe)
                if('ZTE' in onu['serial'].upper()):
                    onu['vlan_id'] = int(vlans['vlan_pppoe'])
                    onu["service_id"] = 2

                    #adiciona vlan pppoe no veip
                    job_id = str(uuid.uuid4())
                    job_veip_pppoe = q.enqueue(tasks_fiberhome.task_addveip, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                    jobs.append(job_id)

                    onu['vlan_id'] = os.getenv('TR069_DHCP_VLANID')
                    onu["service_id"] = 1

                    #adiciona vlan dhcp no veip
                    job_id = str(uuid.uuid4())
                    job_veip_dhcp = q.enqueue(tasks_fiberhome.task_addveip, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                    jobs.append(job_id)

                    onu['vlan_id'] = 2
                    onu['vlan_port'] = 2
                    onu['vlan_type'] = 'multicast'

                    #adiciona vlan iptv multicast na porta 2
                    job_id = str(uuid.uuid4())
                    job_vlan_multicast = q.enqueue(tasks_fiberhome.task_addvlan, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                    jobs.append(job_id)

                else:
                    #remove as vlans
                    job_id = str(uuid.uuid4())
                    job_removevlans = q.enqueue(tasks_fiberhome.task_removevlans, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                    jobs.append(job_id)

                    onu['vlan_id'] = int(vlans['vlan_pppoe'])
                    onu['vlan_port'] = 1
                    onu['vlan_type'] = 'unicast'

                    #adiciona vlan pppoe na porta 1
                    job_id = str(uuid.uuid4())
                    job_vlan_pppoe = q.enqueue(tasks_fiberhome.task_addvlan, args=([job_id]), kwargs=(onu), retry=Retry(max=5), job_id=job_id)
                    jobs.append(job_id)                                

                    onu['vlan_id'] = int(vlans['vlan_iptv'])
                    onu['vlan_port'] = 2
                    onu['vlan_type'] = 'unicast'

                    #adiciona vlan iptv unicast na porta 2
                    job_id = str(uuid.uuid4())
                    job_vlan_iptv = q.enqueue(tasks_fiberhome.task_addvlan, args=([job_id]), kwargs=(onu), retry=Retry(max=5), depends_on=job_removevlans, job_id=job_id)
                    jobs.append(job_id)

                    onu['vlan_id'] = 2
                    onu['vlan_port'] = 2
                    onu['vlan_type'] = 'multicast'
                    
                    #adiciona vlan iptv multicast na porta 2
                    job_id = str(uuid.uuid4())
                    job_vlan_multicast = q.enqueue(tasks_fiberhome.task_addvlan, args=([job_id]), kwargs=(onu), retry=Retry(max=5), depends_on=job_removevlans, job_id=job_id)
                    jobs.append(job_id)

                return jsonify({'status': 'OK', 'msg' : jobs})     
            else:
                return jsonify({'status' : 'ERROR', 'msg': "ONU not found"})
        else:
            return jsonify({'status' : 'ERROR', 'msg': "Missing params: {}".format(', '.join(set(required_params).difference(set(params))))})
    except Exception as e:
        return str(e)
