﻿#!/usr/bin/python
# -*- coding: utf-8 -*-

from database import get_onus_alocadas

def plan_rule(model, plan, username):

    try:
        '''
        Regras ZTE				
        ==========
        Cliente	    Plano	    ONU Nova	ONU Antiga	Acao
        Novo*	    50	        F660	    null	    Provisionar
        Antigo**	50	        F660	    F660	    Provisionar
        Todos	    >50 & <100	F660	    Todos	    Provisionar
        Todos	    >=100	    F670	    Todos	    Provisionar
        
        * uma alocacao
        ** mais de uma alocacao
        '''
        valid = None
        f660 = '660' in model
        f670 = '670' in model
        f6600 = '6600' in model

        if(not f660 and not f670 and not f6600):
            return None

        if(int(plan) > 50 and int(plan) < 100):
            if(f670 or f6600):
                valid = 'F660'
            if(f660 and (not f6600)):
                return None    

        if int(plan) >= 100:
            if(f660 and (not f6600)):
                valid = 'F670'
            if(f670 or f6600):
                return None    

        if valid == None:
            onus = get_onus_alocadas(username)
            #Novo
            if(len(onus) <= 1):
                if(f670):
                    valid = 'F660'
            #Antigo
            else:
                anterior = onus[0]['descricao']
                if(f670):
                    if '660' in anterior:
                        valid = 'F660'
                if(f660 and (not f6600)):
                    if '670' in anterior:
                        valid = 'F670'
                    elif not '660' in anterior:    
                        valid = 'FIBERHOME'

        return valid
    except:
        return False    