#!/usr/bin/python
# -*- coding: utf-8 -*-
from dotenv import load_dotenv
import os
import time
from database import *
from multiprocessing import Process
import time
import sys
from datetime import datetime
from ztemanager import ZTEManager
import threading
from genieacs import *
from worker_provision_queue import conn
from tasks_provision_zte import *

load_dotenv()

scriptname = 'DISCOVERY_ZTE_RQ'

def getdiscovery():
  #try:
    unauth_list = []
    
    olts = monitors('ZTE')

    '''
    olts = [
      {
        'id': 25,
        'name': 'OLT And Homologacao',
        'ip': '*************',
        'type': 'ZTE'
      }
    ]
    '''
  
    proccess = []

    for olt in olts:
        #print(olt['name'], olt['ip'])
      #try:
        manager = ZTEManager(olt['ip'], olt['name'])
      #  self.managers.append(manager) 
      #for manager in self.managers:
      #  if(not manager.connected):
      #    manager.connect()
        response = manager.list_unauth()

        manager.disconnect()
        del manager
        unauth_list = unauth_list + response
      #except:
      #  pass  
    onus=[]
    for unauth in unauth_list:

        onu = {
            'olt': unauth["olt_name"],
            'olt_ip': unauth["olt_ip"],
            'olt_model': 'ZTE',
            'chassi' : unauth['chassi'],
            'slot': unauth['slot'],
            'pon': unauth['pon'],
            'placa': unauth['slot'],
            'porta': unauth['pon'],
            'serial': unauth['serial'],
            'source': scriptname
        }

        onu['onu_type'] = unauth['type']
        if('670' in unauth['type']):
            onu['modelo'] = 'F670L' 
            onu['onu_modelo'] = 'F670L'
        if('660' in unauth['type']):
            onu['modelo'] = 'F660' 
            onu['onu_modelo'] = 'F660'
        if('6600' in unauth['type']):
            onu['modelo'] = 'F6600P' 
            onu['onu_modelo'] = 'F6600P'    

        onu['data'] = datetime.datetime.now()
        onu['mac'] = None
        onu['login'] = None 
        onu['patrimonio'] = None 
        onu['login_ativo'] = None
        onu['data_comodato'] = None
        onu['id_comodato'] = None
        onu['status'] = 'WAITING'

        patrimonio = getpatrimonio_alocado(onu['serial']) 

        if(patrimonio):
            onu['mac'] = patrimonio['mac']
            onu['patrimonio'] = patrimonio['patrimonio']
            onu['login_ativo'] = patrimonio['login_ativo']
            onu['data_comodato'] = patrimonio['data_comodato']
            onu['id_comodato'] = patrimonio['id_comodato']
            onu['username'] = patrimonio['login']  
            onu['login_ativo'] = patrimonio['login_ativo']
            onu['plano'] = patrimonio['plano']

            if('ZTE' in onu['serial'].upper()):
                onu['tr069'] = 1
            else:
                onu['tr069'] = 0  

            if((os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None) and (checkignorelist(serial=onu["serial"].upper()))):
                msg = '[{}]  ONU {} esta na lista para ignorar e nao sera autorizada'.format(scriptname, onu["serial"])
                print(msg)
            else:
              #verifica se ja existe um provisionamento em andamento 
              if(pending_provision(onu['serial'])):
                print('{} Provisionamento pendente'.format(onu['serial'])) 
              else:
                #verifica se a onu atende os requisitos para o provisionamento
                missing_params = task_checkonu(onu)

                if(len(missing_params) == 0):
                  #se for informada uma onu de testes, provisiona apenas esta onu
                  if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                    print('ONU {} sera provisionada'.format(onu['serial'].upper())) 
                    print(onu)
                    task_provision(onu)
                else:
                  current_provision= {
                    'id' : uuid.uuid4().hex, 
                    'serial': onu['serial'],
                    'username' : onu['username'],
                    'source': onu['source'],
                    'olt': onu['olt'],
                    'olt_ip': onu["olt_ip"],
                    'olt_model': 'ZTE',
                    'slot': onu['placa'],
                    'pon': onu['porta'],
                    'model': onu['modelo'],
                    'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
                    'exc_info': 'Faltando parametros: {}'.format(missing_params),
                    'enqueued_at': datetime.datetime.now()
                  }
                  if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                    provision_id = update_provision(current_provision)
                    print('{} Faltando parametros: {}'.format(onu['serial'], missing_params))
        else:
            current_provision= {
                    'id' : uuid.uuid4().hex, 
                    'serial': onu['serial'],
                    'username' : None,
                    'source': onu['source'],
                    'olt': onu['olt'],
                    'olt_ip': onu["olt_ip"],
                    'olt_model': 'ZTE',
                    'slot': onu['placa'],
                    'pon': onu['porta'],
                    'model': None,
                    'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
                    'exc_info': 'ONU sem patrimônio',
                    'enqueued_at': datetime.datetime.now()
            }
            if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                provision_id = update_provision(current_provision)
                msg = 'ONU {} sem patrimonio'.format(onu["serial"])
                print(msg)

                #se for informada uma onu de testes, provisiona apenas esta onu
                if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                    onus.append(onu)  

  #except:
  #  pass


if __name__ == '__main__':

  #try:
    getdiscovery()
  #except Exception as e:
  #  print('Ocorreu um erro em __main__: '+str(e))