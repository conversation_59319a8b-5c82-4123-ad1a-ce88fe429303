import os
import select
import time
from timeit import default_timer as timer
from database import *
from config import *
from tl1 import *
from flask import Flask, request, render_template, session
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from flask_cors import CORS
import eventlet
import json       
from datetime import datetime
from threading import Lock
from ixc_ws import *
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

eventlet.monkey_patch(socket=False)     

app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
CORS(app, support_credentials=True)
socketio = SocketIO(app, async_mode='eventlet')
socketio.init_app(app, cors_allowed_origins="*")
thread_lock = Lock()

def writelog(message):
    with open('authws_debug.log', 'a') as f:
        f.write(repr(message) + '\n')

# Aguarda por notificacoes
def notify_start():
  con = psycopg2.connect(
        host=SERVICES1DB_HOST,
        database='noc2',
        user=SERVICES1DB_USER,
        password=SERVICES1DB_PASS
  )
  con.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)

  curs = con.cursor()
  curs.execute("LISTEN auth_insert;")
  #curs.execute("LISTEN unauthorized;")

  while True:
    socketio.sleep(0)
    if select.select([con],[],[],5) != ([],[],[]):
        con.poll()
        while con.notifies:
            notify = con.notifies.pop(0)
            payload = json.loads(notify.payload)
            serial = payload["record"]["serial"]

   #         if(notify.channel == 'unauthorized'):
   #           socketio.emit('unauthorized_onus', json.loads(get_unauthorized()), namespace='/app')

            if(notify.channel == 'auth_insert'):
              if(serial):  
                socketio.emit('authlogs', payload["record"], room=serial.upper(), namespace='/app')  

@socketio.on('connect', namespace='/app')
def connected():
    session['id'] = request.sid
    socketio.emit('heartbeat', {'currentTimeUnix': time.time()}, room=session['id'], namespace='/app')
    #print(datetime.now().strftime('%Y-%m-%d %H:%M:%S') + ' [Client connected] : (Client id: ' + session['id'] + ')')
    
@socketio.on('disconnect', namespace='/app')
def disconnect():
  if('room' in session):
    if(session["room"]):
      leave_room(session["room"])
    #print(datetime.now().strftime('%Y-%m-%d %H:%M:%S') + ' [Client disconnected] : (Client id: ' + session['id'] + ')')

@socketio.on('join', namespace='/app')
def on_join(data):
    r = json.dumps(data)
    loaded_r = json.loads(r)
    serial = loaded_r['serial']
    join_room(serial.upper())
    session['room'] = serial.upper()

    logs = getauthlog(serial)
    if(logs):
      socketio.emit('authlogs', json.loads(logs), room=session['room'], namespace='/app')

@socketio.on('leave', namespace='/app')
def on_leave(data):
  r = json.dumps(data)
  loaded_r = json.loads(r)
  serial = loaded_r['serial']
  if(serial):
    leave_room(serial.upper())
  if('room' in session):
    session['room'] = None

#Atualiza dados tecnicos no IXC

@socketio.on('dados_tecnicos', namespace='/app')
def on_dados_tecnicos(data):

  r = json.dumps(data)
  loaded_r = json.loads(r)
  serial = loaded_r['serial']
  onu = findauthorizedonu(serial)

  if(onu == None):
    device = getdevice(serial)
    if(device):
        onu ={
            'olt_ip': device['olt_ip'],
            'serial': device['serial'],
            'placa': device['slot'],
            'porta': device['pon']
        }

  if(onu):
    #carrega os dados via banco para atualizar os dados tecnicos no ixc
    payload = get_dados_tecnicos(onu['serial'], onu['olt_ip'], '0-0-{}-{}'.format(onu['placa'], onu['porta']))
  
    #atualiza os dados tecnicos no ixc via api
    response = json.loads(update_dados_tecnicos(payload))
    if('type' in response):
      if(response['type'] == 'error'):
        dados = json.dumps(response)
      else:
        dados = get_transmissor(serial)
    socketio.sleep(0.5)
    socketio.emit('dados_tecnicos', dados, room=session['id'], namespace='/app')

if __name__ == '__main__':
    
    with thread_lock:
      thread = socketio.start_background_task(notify_start)
    
    socketio.run(app, port=5003,host='0.0.0.0', keyfile='/etc/letsencrypt/live/provision.telemidia.net.br/privkey.pem', certfile='/etc/letsencrypt/live/provision.telemidia.net.br/cert.pem')

    print(datetime.now().strftime('%Y-%m-%d %H:%M:%S') + ' [APP STARTED] : Listening on port 5003')