from database import *
from config import *
import requests

def removeacs(mac):
  mac = mac.replace(':', '')
  mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

  # primeiramente remove a onu filtrando pelo MAC
  url = 'http://{}:7557/devices/?query=%7B%22InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1.MACAddress%22%3A%22{}%22%7D&projection=_id,_tags'.format(ACS_IP,mac.lower())
  
  r = requests.get(url)
  r_dictionary = r.json()
   
  if(len(r_dictionary) > 0):
    onuid = r_dictionary[0]['_id']
    if('_tags') in r_dictionary[0]:
      tags = r_dictionary[0]['_tags']
    else:
      tags = []  
      # remove apenas as onus que nao possuem a tag Reauth
    if(not 'Reauth' in tags):

      url = 'http://{}:7557/devices/{}/tasks?timeout=10&connection_request'.format(ACS_IP, onuid) 
      
      pload = {'name':'reboot'}
      r = requests.post(url, data=json.dumps(pload))
      
      url = 'http://{}:7557/devices/{}'.format(ACS_IP, onuid)
      r = requests.delete(url)

      #em seguida remove todas as onus filtrando pelo username (caso existam)
      patrimonio = getpatrimonio_alocado(mac)
      if(patrimonio):
        username = patrimonio['login']
        url = 'http://{}:7557/devices/?query=%7B%22InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.2.Username%22%3A%22{}%22%7D&projection=_id'.format(ACS_IP, username)
        r = requests.get(url)
        r_dictionary = r.json()

        if(len(r_dictionary) > 0):
          for item in r_dictionary:
            onuid = item['_id']
          
            #envia um comando de reboot antes de excluir
            url = 'http://{}:7557/devices/{}/tasks?timeout=10&connection_request'.format(ACS_IP, onuid) 

            pload = {'name':'reboot'}
            r = requests.post(url, data=json.dumps(pload))

            url = 'http://{}:7557/devices/{}'.format(ACS_IP, onuid)
            r = requests.delete(url)
    else:
      removetag(mac, 'Reauth')            
  else:
    print('ONU nao localizada no ACS')

def addtag(mac, tag):
  mac = mac.replace(':', '')
  mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

  url = 'http://{}:7557/devices/?query=%7B%22InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1.MACAddress%22%3A%22{}%22%7D&projection=_id'.format(ACS_IP,mac.lower())    
  r = requests.get(url)
  r_dictionary = r.json()
       
  if(len(r_dictionary) > 0):
    onuid = r_dictionary[0]['_id']
    url = 'http://{}:7557/devices/{}/tags/{}'.format(ACS_IP, onuid, tag)
    r = requests.post(url)

def removetag(mac, tag):
  mac = mac.replace(':', '')
  mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

  url = 'http://{}:7557/devices/?query=%7B%22InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1.MACAddress%22%3A%22{}%22%7D&projection=_id'.format(ACS_IP,mac.lower())    
  r = requests.get(url)
  r_dictionary = r.json()
       
  if(len(r_dictionary) > 0):
    onuid = r_dictionary[0]['_id']
    url = 'http://{}:7557/devices/{}/tags/{}'.format(ACS_IP, onuid, tag)
    r = requests.delete(url)    

def changeinformperiod(mac, period):
  mac = mac.replace(':', '')
  mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

  url = 'http://{}:7557/devices/?query=%7B%22InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1.MACAddress%22%3A%22{}%22%7D&projection=_id'.format(ACS_IP,mac.lower())    
  r = requests.get(url)
  r_dictionary = r.json()
       
  if(len(r_dictionary) > 0):
    onuid = r_dictionary[0]['_id']
    
    url = 'http://{}:7557/devices/{}/tasks?timeout=10&connection_request'.format(ACS_IP, onuid) 
    
    pload = {
            "name": "setParameterValues",
            "parameterValues": [
                ["InternetGatewayDevice.ManagementServer.PeriodicInformEnable", "true"],  
                ["InternetGatewayDevice.ManagementServer.PeriodicInformInterval", period]
            ]}
    r = requests.post(url, data=json.dumps(pload))

def changepppoevlanid(mac, vlan):
  mac = mac.replace(':', '')
  mac = ':'.join(mac[i:i+2] for i in range(0,12,2))

  url = 'http://{}:7557/devices/?query=%7B%22InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1.MACAddress%22%3A%22{}%22%7D&projection=_id'.format(ACS_IP,mac.lower())    
  r = requests.get(url)
  r_dictionary = r.json()
       
  if(len(r_dictionary) > 0):
    onuid = r_dictionary[0]['_id']
    
    url = 'http://{}:7557/devices/{}/tasks?timeout=10&connection_request'.format(ACS_IP, onuid) 
    
    pload = {
            "name": "setParameterValues",
            "parameterValues": [
                ["InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.2.X_ZTE-COM_VLANID", vlan]
            ]}
    r = requests.post(url, data=json.dumps(pload))
