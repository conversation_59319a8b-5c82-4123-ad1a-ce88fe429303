from genieclient import GenieClient

class TestGenieClient:
    def test_registered(self):
        client = GenieClient()
        
        #mac_valido = "40:0e:f3:0d:35:9e"
        mac_valido = 'E01954DA1A18' 
        
        onu = client.registered(mac_valido)
        assert onu.keys() >= {"_id", "_tags"}

        mac_invalido = "40:0e:f3:0d:35:9f"
        assert client.registered(mac_invalido) is False

    '''

    def test_addtag(self):
        client = GenieClient()
        
        mac_valido = "40:0e:f3:0d:35:9e"
        tag = 'PYTEST'
        client.addtag(mac_valido, tag)
        onu = client.registered(mac_valido)
        assert tag in onu['_tags']

    def test_removetag(self):
        client = GenieClient()
        
        mac_valido = "40:0e:f3:0d:35:9e"
        tag = 'PYTEST'
        client.removetag(mac_valido, tag)
        onu = client.registered(mac_valido)
        assert tag not in onu['_tags']
    '''