
from flask import request, jsonify, render_template, Blueprint
from db import *
from models import token_required
import json

ixc_route = Blueprint('ixc_route', __name__)

@ixc_route.route('/dadostecnicos', methods=['POST'])
@token_required
def get_dados_tecnicos():

    if(request.form):
        params = request.form
    else:
        if(request.data):
            params = json.loads(request.data.decode())
        else:
            params = []
    
    required_params = ("serial",)
    
    if all (k in params for k in required_params):
      serial = params['serial']

      onu = findauthorizedonu(serial)

      if(onu == None):
        device = getdevice(serial)
        if(device):
          onu ={
            'olt_ip': device['olt_ip'],
            'serial': device['serial'],
            'placa': device['slot'],
            'porta': device['pon']
          }
      if(onu):
        return jsonify(onu)            
      else:
        return jsonify({'status' : 'ERROR', 'msg': "ONU not found"})
    else:
      return jsonify({'status' : 'ERROR', 'msg': "Missing params: serial"})
