from tl1 import *
from database import *
from tasks_fiberhome import *

onu = {}

onu['source'] = 'TESTE UNM'
onu['serial'] = 'ZTEGc4b347cb'
onu['olt'] = '***********'
onu['olt_ip'] = '***********'
onu['olt_model'] = 'FBT'
onu['placa'] = 12
onu['porta'] = 16
onu['mac'] = None
onu['username'] = None 
onu['modelo'] = None 
onu['patrimonio'] = None 
onu['login_ativo'] = None
onu['data_comodato'] = None
onu['id_comodato'] = None
onu['status'] = 'WAITING'

patrimonio = getpatrimonio_alocado(onu['serial'])  

if(patrimonio):
    onu['mac'] = patrimonio['mac']
    onu['modelo'] = patrimonio['modelo']
    onu['patrimonio'] = patrimonio['patrimonio']
    onu['login_ativo'] = patrimonio['login_ativo']
    onu['data_comodato'] = patrimonio['data_comodato']
    onu['id_comodato'] = patrimonio['id_comodato']
    onu['username'] = patrimonio['login']  
    onu['nome'] = patrimonio['login']  
    onu['login_ativo'] = patrimonio['login_ativo']
    onu['plano'] = patrimonio['plano']

    if('ZTE' in onu['serial'].upper()):
        onu['tr069'] = 1
    else:
        onu['tr069'] = 0  


item = onu

if('ZTE' in item['serial'].upper()):
    item['modelo'] = 'GPON CURRENCY SFU'
else:
    patrimonio = getpatrimonio_alocado(item['serial'])  
    if(patrimonio):
        item['modelo'] = patrimonio['modelo']



#verifica se a onu ja esta autorizada
#check = task_checkauthorization(**item) #VALIDADO
#print(check)

response = onuauthorize(item)
print(response)






#if('ZTE' in item['serial'].upper()):
#    item['modelo'] = 'GPON CURRENCY SFU'
#else:
#    patrimonio = getpatrimonio_alocado(item['serial'])  
#    if(patrimonio):
#        item['modelo'] = patrimonio['modelo']

#response = onuauthorize(item)
