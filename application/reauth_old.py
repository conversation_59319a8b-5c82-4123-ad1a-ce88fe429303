#########################
#   REPROVISIONAMENTO   #
#########################

import select
import psycopg2
import psycopg2.extensions
import requests
from dotenv import load_dotenv
from database import *
from cli import *
from tl1 import *
from genieacs import *
import json

scriptname = 'REAUTH[************]'

load_dotenv()

list = []

# Aguarda por notificacoes de insercao na tabela authservice.reauth


def reauth_notify_start():
    con = psycopg2.connect(
        host=os.getenv('SERVICES1DB_HOST'),
        database=os.getenv('SERVICES1DB_DATABASE'),
        user=os.getenv('SERVICES1DB_USER'),
        password=os.getenv('SERVICES1DB_PASS')
    )
    con.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)

    curs = con.cursor()
    curs.execute("LISTEN reauth_insert;")

    print("Processo iniciado")

    while True:
        if select.select([con], [], [], 5) != ([], [], []):
            con.poll()
            while con.notifies:
                notify = con.notifies.pop(0)
                print("Notificacao :", notify.pid,
                      notify.channel, notify.payload)
                payload = json.loads(notify.payload)
                process_reauth(payload["record"])       

def process_reauth(item):

            if((os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None) and (checkignorelist(serial=item["serial"].upper()))):
                msg = '[{}]  ONU {} esta na lista para ignorar e nao sera reprovisionada'.format(
                    scriptname, item['serial'])
                print(msg)
                return False
            else:
                onu = findauthorizedonu(item['serial'])
                # remove da lista de onus bloqueadas
                blocksave(item['serial'])
                if(onu):
                    patrimonio = getpatrimonio_alocado(item['serial'])
                    # Adiciona a tag Reauth no servidor ACS caso suporte tr069
                    if('ZTE' in item['serial'].upper() and patrimonio and 'mac' in patrimonio):
                        addtag(patrimonio['mac'], 'Reauth')
                        #removeacs(patrimonio['mac'])

                    log = {}
                    log['serial'] = item['serial']
                    log['patrimonio'] = patrimonio['patrimonio']
                    log['olt'] = onu['olt_nome']
                    log['olt_ip'] = onu['olt_ip']
                    log['placa'] = onu['placa']
                    log['porta'] = onu['porta']
                    log['mac'] = patrimonio['mac']
                    log['username'] = onu['nome']
                    log["script"] = scriptname
                    log['msg'] = "Reprovisionamento ONU {}..Operador: {} (Motivo: {})".format(
                        item['serial'], item["operador"], item["motivo"].encode('utf-8'))
			

                    log["erro"] = 0
                    log["task"] = "REAUTH"
                    log["vlan_pppoe"] = None
                    log["vlan_iptv"] = None 
                    log["vlan_dhcp"] = None 
                    log["porta_onu"] = None 
                    log["onu_id"] = None

                    insertlog(log)

                    # Se a data do provisionamento da onu for anterior
                    # a implantacao da checagem de patrimonio
                    # insere um registro na tabela ignore para ignorar este campo
                    # no provisionamento
                    if(onu['data_provisionamento'] < REAUTH_IGNORE_INVENTORY_NUMBER):
                        addignorelist(
                            serial=item['serial'], comodato=1, motivo='Data provisionamento anterior a regra de comodato')

                    # Se a data do provisionamento da onu for anterior
                    # a implantacao da checagem de plano
                    # insere um registro na tabela ignore para ignorar este campo
                    # no provisionamento
                    if(onu['data_provisionamento'] < REAUTH_IGNORE_PLAN):
                        addignorelist(
                            serial=item['serial'], plano=1, motivo='Data provisionamento anterior a regra de plano')

                    # Remove da whitelist de todas OLTs
                    olts = unm_oltlist()
                    for olt in olts:
                        try:
                            if(removewhitelist(olt["ip"], item['serial'], False) == True):
                                print("ONU {} removida da whitelist em {}".format(
                                    item['serial'], olt["ip"]))
                            else:
                                print("ONU {} nao esta na whitelist em {}".format(
                                    item['serial'], olt["ip"]))
                        except:
                            print(
                                "Falha ao tentar remover a onu da OLT {}".format(olt['ip']))
                    

if __name__ == '__main__':
    reauth_notify_start()
