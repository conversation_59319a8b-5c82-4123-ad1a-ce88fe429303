---
- hosts: provision
  remote_user: root
  tasks:
  # caso ocorra algum problema para ativar o venv, remover a versao padrao do pip
  # - name: Remove a versao anterior do pip
  #   shell: |
  #     yum remove python36-pip
  #   args:
  #     executable: /bin/bash
  #   become: yes
  #   become_user: root
  - name: Instalar Python e dependencias
    ansible.builtin.yum:
      name:
        - git
        - python2-pip
        - python3
        - python3-devel
        - python3-pip
        - postgresql-devel
        - gcc
        - supervisor
        - nginx
        - cronie
        - telnet
        - snapd
      state: present 
    become: yes
    become_user: root  
  - name: Remove a aplicacao anterior
    shell: |
      rm -fr /var/www/{{container}}
    args:
      executable: /bin/bash
    become: yes
    become_user: root  
  - name: Clonar o repositorio git da aplicacao
    git:
      repo: "http://{{git_username|urlencode()}}:{{git_password|urlencode()}}@{{git_repo}}"
      dest: "/var/www/{{container}}"
      update: yes
    become: yes
    become_user: root    
  - name: <PERSON><PERSON>r um ambiente virtual do Python e instalar as dependencias
    shell: |
      python3 -m venv /var/www/{{container}}/api/venv
      python3 -m venv /var/www/{{container}}/application/venv
      python3 -m venv /var/www/{{container}}/monitor/venv
      source /var/www/{{container}}/api/venv/bin/activate
      pip install --upgrade pip
      pip install setuptools-rust
      pip install -r /var/www/{{container}}/api/requirements.txt
      deactivate
      source /var/www/{{container}}/application/venv/bin/activate
      pip install --upgrade pip
      pip install -r /var/www/{{container}}/application/requirements.txt
      deactivate
      source /var/www/{{container}}/monitor/venv/bin/activate
      pip install --upgrade pip
      pip install -r /var/www/{{container}}/monitor/requirements.txt
      deactivate
      cd /var/www/{{container}}/authservice
      python2 -m pip install pip==20.3.4
      pip2 install -r requirements.txt
      source /var/www/{{container}}/authservice/venv/bin/activate
      pip install -r requirements3.txt

    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Criar arquivo de variaveis de ambiente da api e applicacao
    shell: |
      cat > /var/www/{{container}}/api/.env << EOF 
      ONU_TESTES="ZTEGC4B347CB"
      UNAUTHORIZED_TABLE="authservice.unauthorized_test"
      APP_SECRET="c3ab8ff13720e8ad9047dd39466b3c8974e592c2fa383d4a3960714caef0c4f2"
      API_USER='provisionapi'
      API_PASSWORD='*.saudet3l'
      SERVICES1DB_USER = "provision"
      SERVICES1DB_PASS = "MzIyZmI3ZDczMGYyZTU5MDhiNzQxMWJh"
      SERVICES1DB_HOST = "pgsql-services1.pocos-net.com.br"
      SERVICES1DB_PORT = 5432
      SERVICES1DB_DATABASE = "noc2"
      IXC_HOST="*************"
      IXC_DATABASE="ixcprovedor"
      IXC_USER="leitura"
      IXC_PASS="RuskINHanaKeTATRaNaT"
      IXC_TOKEN="2:53634997c36c23d451158d695685b2a821b20e8748c868fa6c7a29f0fcf4d632"
      IXC_WEBSERVICE="central.telemidia.net.br"
      UNMDB_USER = "fdw_user"
      UNMDB_PASS = "vislecaina"
      UNMDB_HOST = "************"
      UNMDB_DATABASE = "integratecfgdb"
      UNMDB_CHARSET = "utf8mb4"
      UNMTL1_HOST = "************"
      UNMTL1_PORT = 3337
      UNMTL1_USER = "ftthtl1"
      UNMTL1_PASS = "ftthtl1123*"
      CLI_USER = "GEPON"
      CLI_PASS = "*Cass1ambapre-ta#"
      OLT_ZTE_PORT = 22
      OLT_ZTE_USERNAME="telemidia"
      OLT_ZTE_PASSWORD="@Cass1ambapre-ta!"
      OLT_ZTE_WAN_PROFILE="TELEMIDIA-NAVEGACAO"
      OLT_ZTE_IPTV_PROFILE="TELEMIDIA-IPTV"
      ZTE_API="https://api.telemidia.net.br:8000"
      ZTE_API_TOKEN="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NDI3ODAzNzIsImV4cCI6MzMxNjgxNTMxNzJ9.OgEbhlHCXVUhzT8DZ5amCpzJSCswODxSgCDYoDRllz0"
      PROVISION_MODE=MANAGER
      REDIS_HOST={{redis_host}}
      REDIS_PORT=6379
      REDIS_USER="admin"
      REDIS_PASS="Yjk4NGM0N2UzYTQ5NmI4ZTBkYTJmM2I1"
      RQ_USER="telrq"
      RQ_PASS="NmU4Y2FmMTJjNmNiMzk0NjQ4YjhiOTdj"
      JOB_MAX_RETRY=5
      PEXPECT_TIMEOUT=20
      TR069_DHCP_VLANID = 10
      TR069_PROFILEID = 2
      ACS_IP="*************"
      EOF
      cp /var/www/{{container}}/api/.env /var/www/{{container}}/application/.env
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Habilita o socket do snapd e instala o certbot
    shell: |
      systemctl enable --now snapd.socket
      ln -s /var/lib/snapd/snap /snap
      snap install --classic certbot
      ln -s /snap/bin/certbot /usr/bin/certbot
    args:
      executable: /bin/bash
    become: yes
    become_user: root    
  - name: Copia o certificado
    shell: |
      cp -r /var/www/{{container}}/letsencrypt/ /etc/letsencrypt/live/provision.telemidia.net.br/
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Habilita o supervisor e cria os arquivos de configuração dos serviços
    shell: |
       cp /var/www/provision/confs/supervisord.conf /etc/supervisord.conf 
       systemctl enable supervisord
       systemctl start supervisord
       cat > /etc/supervisord.d/{{container}}.ini << EOF 
        [program:provision-api]
        directory=/var/www/provision/api
        command=/var/www/provision/api/venv/bin/gunicorn --workers 4 --bind localhost:8000 --access-logfile /var/www/provision/api/provision-api-access.log --error-logfile /var/www/provision/api/provision-api-error.log --capture-output --certfile=/etc/letsencrypt/live/provision.telemidia.net.br/cert.pem --keyfile=/etc/letsencrypt/live/provision.telemidia.net.br/privkey.pem wsgi:application
        process_name=%(program_name)s
        numprocs=1
        stopsignal=TERM
        autostart=true
        autorestart=true

        [program:provision-application]
        directory=/var/www/provision/application
        command=/var/www/provision/application/venv/bin/python /var/www/provision/application/app.py
        process_name=%(program_name)s
        numprocs=1
        stopsignal=TERM
        autostart=true
        autorestart=true

        [program:worker-config-queue]
          directory=/var/www/provision/api
          command=/var/www/provision/api/venv/bin/python /var/www/provision/api/worker_config_queue.py
          process_name=%(program_name)s_%(process_num)02d
          numprocs=3
          stopsignal=TERM
          autostart=true
          autorestart=true

        [program:worker-provision-queue]
          directory=/var/www/provision/application
          command=/var/www/provision/application/venv/bin/python /var/www/provision/application/worker_provision_queue.py
          process_name=%(program_name)s_%(process_num)02d
          numprocs=6
          stopsignal=TERM
          autostart=true
          autorestart=true  
       EOF  
       supervisorctl update
    args:
      executable: /bin/bash
    become: yes
    become_user: root  
  - name: Cria os arquivos de configuração do nginx (proxy reverso)
    shell: |
       cat > /etc/nginx/conf.d/virtual.conf << EOF 
        server {
          listen       80;
          server_name  provision.telemidia.net.br;
          return 301 https://\$server_name$request_uri; 	
        }

        server {
          listen 443 ssl;
          listen [::]:443 ssl;
    
          server_name  provision.telemidia.net.br;

          ssl_certificate /etc/letsencrypt/live/provision.telemidia.net.br/fullchain.pem;
          ssl_certificate_key /etc/letsencrypt/live/provision.telemidia.net.br/privkey.pem;
          ssl_trusted_certificate /etc/letsencrypt/live/provision.telemidia.net.br/cert.pem;

          location / {
            proxy_set_header   X-Forwarded-For \$remote_addr;
            proxy_set_header   Host \$http_host;
            proxy_pass https://127.0.0.1:8000;
          }
        }
       EOF
       systemctl enable nginx
       systemctl stop nginx
       systemctl start nginx
    args:
      executable: /bin/bash
    become: yes
    become_user: root   
  - name: Cria os arquivos da cron do monitoramento de onus nao autorizadas e inicia o serviçco crond
    shell: |
       systemctl stop crond
       systemctl enable crond
       cat > /var/spool/cron/root << EOF 
        * * * * *       /usr/bin/env bash -c 'cd /var/www/provision/application && source /var/www/provision/application/venv/bin/activate && python ./alarms.py' > /dev/null 2>&1
        * * * * *       ( sleep 30 ; /usr/bin/env bash -c 'cd /var/www/provision/application && source /var/www/provision/application/venv/bin/activate && python ./alarms.py' > /dev/null 2>&1 )
        * * * * *       /usr/bin/env bash -c 'cd /var/www/provision/application && source /var/www/provision/application/venv/bin/activate && python ./alarms_rq.py' > /dev/null 2>&1
        * * * * *       ( sleep 30 ; /usr/bin/env bash -c 'cd /var/www/provision/application && source /var/www/provision/application/venv/bin/activate && python ./alarms_rq.py' > /dev/null 2>&1 )
        */2 * * * *     /usr/bin/env bash -c 'cd /var/www/provision/application && source /var/www/provision/application/venv/bin/activate && python ./discovery_fh.py' > /dev/null 2>&1
        */2 * * * *     /usr/bin/env bash -c 'cd /var/www/provision/application && source /var/www/provision/application/venv/bin/activate && python ./discovery_fh_rq.py' > /dev/null 2>&1
        * * * * *       /usr/bin/env bash -c 'cd /var/www/provision/application && source /var/www/provision/application/venv/bin/activate && python ./discovery_zte.py' > /dev/null 2>&1
       EOF
       systemctl start crond
    args:
      executable: /bin/bash
    become: yes
    become_user: root   

  - name: Criar arquivo systemd do serviço de provisionamento antigo
    shell: |
       cat > /etc/systemd/system/provision.service << EOF 
       [Unit]
       Description=Provision Service
       After=network.target

       [Service]
       Restart=on-failure
       RestartSec=30s
       ExecStart=/usr/bin/python /var/www/provision/authservice/provision.py

       [Install]
       WantedBy=multi-user.target
       EOF
       systemctl enable provision
       systemctl stop provision
       systemctl start provision
    args:
       executable: /bin/bash
    become: yes
    become_user: root    

  - name: Criar arquivo systemd do serviço de provisionamento zte antigo
    shell: |
       cat > /etc/systemd/system/provision-zte.service << EOF 
       [Unit]
       Description=Provision ZTE Service
       After=network.target

       [Service]
       User=root
       Group=root
       Type=simple
       WorkingDirectory=/var/www/provision/authservice/
       Restart=on-failure
       RestartSec=30s
       ExecStart=/var/www/provision/authservice/venv/bin/python3 provision_zte.py
       EOF
       systemctl enable provision-zte
       systemctl stop provision-zte
       systemctl start provision-zte
    args:
       executable: /bin/bash
    become: yes
    become_user: root    

  - name: Criar arquivo systemd do serviço de monitoramento do provisionamento
    shell: |
       cat > /etc/systemd/system/auth-monitor.service << EOF 
       [Unit]
       Description=Authservice Monitor
       After=network.target

       [Service]
       Restart=on-failure
       RestartSec=30s
       WorkingDirectory=/var/www/{{container}}/monitor
       Environment="PATH=/var/www/{{container}}/monitor/venv/bin"
       ExecStart=/var/www/{{container}}/monitor/venv/bin/python /var/www/{{container}}/monitor/app.py
       [Install]
       WantedBy=multi-user.target
       EOF
       systemctl enable auth-monitor
       systemctl stop auth-monitor
       systemctl start auth-monitor
    args:
       executable: /bin/bash
    become: yes
    become_user: root     

  - name: Criar arquivo systemd do serviço websocket dados tecnicos
    shell: |
       cat > /etc/systemd/system/authws.service << EOF 
       [Unit]
       Description=AuthWS
       After=network.target

       [Service]
       Restart=on-failure
       RestartSec=30s
       ExecStart=/usr/bin/python /var/www/provision/authservice/authws.py

       [Install]
       WantedBy=multi-user.target
       EOF
       systemctl enable authws
       systemctl stop authws
       systemctl start authws
    args:
       executable: /bin/bash
    become: yes
    become_user: root  