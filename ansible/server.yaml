---
- hosts: srv-provision
  remote_user: root
  vars:
    server:
      mac0:
        device: macger
        devicetype: macvlan
        type: macvlan
        bootproto: static
        macvlan_mode: bridge
        ipaddr: "************"
        prefix: 24
        gateway: "***********"
        dns1: "***********"
        dns2: "***********"
    
    provision:
      memory: 4g
      base: 
        image: docker-registry.telemidia.net.br/pnetbase7-4
        tag: latest
      eth0:
        device: eth0
        ipaddr: "************"
        gateway: "***********"
        prefix: 24
        macaddr: "02:46:34:ff:14:3e"
      eth1:
        device: eth1
        ipaddr: "************"
        gateway: "***********"
        prefix: 24
        macaddr: "02:35:ac:28:31:86"
      git_repo: "git.telemidia.net.br/Telemidia/provision.git"

    redis:
      memory: 4g
      base: 
        image: docker-registry.telemidia.net.br/pnetbase7-4
        tag: latest
      eth0:
        device: eth0
        ipaddr: "************"
        gateway: "***********"
        prefix: 24
        macaddr: "02:73:66:0d:8b:1a"
      eth1:
        device: eth1
        ipaddr: 
        gateway: 
        prefix: 
        macaddr: "02:39:5a:0a:bb:c8"    

  tasks:
  
  - name: Install a list of packages (suitable replacement for 2.11 loop deprecation warning)
    ansible.builtin.yum:
      name:
        - wget
        - epel-release
        - nss-pam-ldapd
        - python3
        - python3-pip
      state: present 
    become: yes
    become_user: root
  - name: Download Docker 18
    get_url:
      url: https://download.docker.com/linux/centos/7/x86_64/stable/Packages/docker-ce-18.06.1.ce-3.el7.x86_64.rpm
      dest: /opt/
    become: yes
  - name: Instalar Docker 18 (LOCAL)
    ansible.builtin.yum:
      name: /opt/docker-ce-18.06.1.ce-3.el7.x86_64.rpm
    become: yes  
  - name: Configura NSLCD
    shell: |
      cat > /etc/nslcd.conf << EOF
      uid nslcd
      gid ldap
      uri ldap://*************/ ldap://*************/
      base dc=telemidia,dc=int,dc=br
      binddn cn=system,ou=Roles,dc=telemidia,dc=int,dc=br
      bindpw BKzUGpeGYUJN9PnGd8HOrfk8JmZqyVqtuwhngVhEgq.S4f4qSS
      base   group  ou=Groups,dc=telemidia,dc=int,dc=br
      base   passwd ou=Users,dc=telemidia,dc=int,dc=br
      EOF
      chmod 600 /etc/nslcd.conf
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Configura NSSWITCH
    shell: |
      cat > /etc/nsswitch.conf << EOF
      passwd:     files ldap sss
      shadow:     files sss
      group:      files ldap sss
      hosts:      files dns myhostname
      bootparams: nisplus [NOTFOUND=return] files
      ethers:     files
      netmasks:   files
      networks:   files
      protocols:  files
      rpc:        files
      services:   files sss
      netgroup:   nisplus sss
      publickey:  nisplus
      automount:  files nisplus sss
      aliases:    files nisplus
      EOF
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Configura PAM.D_password
    shell: |
      cat > /etc/pam.d/password-auth << EOF
      auth        required      pam_env.so
      auth        sufficient    pam_unix.so try_first_pass nullok
      auth        sufficient    pam_ldap.so minimum_uid=1500 use_first_pass
      auth        required      pam_deny.so
      account     required      pam_unix.so
      password    requisite     pam_pwquality.so try_first_pass local_users_only retry=3 authtok_type=
      password    sufficient    pam_unix.so try_first_pass use_authtok nullok sha512 shadow
      password    sufficient    pam_ldap.so minimum_uid=1500 try_first_pass
      password    required      pam_deny.so
      session     optional      pam_keyinit.so revoke
      session     required      pam_limits.so
      -session     optional      pam_systemd.so
      session     [success=1 default=ignore] pam_succeed_if.so service in crond quiet use_uid
      session     required      pam_unix.so
      session     sufficient    pam_mkhomedir.so skel=/etc/skel umask=022
      EOF
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Configura PAM.D_password
    shell: |
      cat > /etc/pam.d/system-auth << EOF
      auth        required      pam_env.so
      auth        sufficient    pam_unix.so try_first_pass nullok
      auth        sufficient    pam_ldap.so minimum_uid=1500 use_first_pass
      auth        required      pam_deny.so
      account     required      pam_unix.so
      password    requisite     pam_pwquality.so try_first_pass local_users_only retry=3 authtok_type=
      password    sufficient    pam_unix.so try_first_pass use_authtok nullok sha512 shadow
      password    sufficient    pam_ldap.so minimum_uid=1500 try_first_pass
      password    required      pam_deny.so
      session     optional      pam_keyinit.so revoke
      session     required      pam_limits.so
      -session     optional      pam_systemd.so
      session     [success=1 default=ignore] pam_succeed_if.so service in crond quiet use_uid
      session     required      pam_unix.so
      session     sufficient    pam_mkhomedir.so skel=/etc/skel umask=022
      EOF
    args:
      executable: /bin/bash
    become: yes
    become_user: root 
  - name: "Adiciona grupo no sudoers"
    lineinfile:
      dest: /etc/sudoers
      state: present
      regexp: '^%cor'
      line: '%cor	ALL=(ALL)	ALL'
      validate: visudo -cf %s
    become: yes
    become_user: root
  - name: Start / Inicializa Serviço NSLCD
    ansible.builtin.systemd:
      state: started
      enabled: yes
      name: nslcd
    become: yes
    become_user: root
  - name: Inicializa DOCKER
    ansible.builtin.systemd:
      state: started
      enabled: yes
      name: docker
    become: yes
    become_user: root
  - name: Cria Interface Docker-GER
    shell:
      docker network create -d macvlan -o parent=em3 -o macvlan_mode=bridge docker_ger
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Cria Interface Docker-PUB
    shell:
      docker network create -d macvlan -o parent=em1 -o macvlan_mode=bridge docker_pub
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Gerar arquivo ifcfg-mac0 em network scripts
    shell: |
      cat > /etc/sysconfig/network-scripts/ifcfg-mac0 << EOF
      DEVICE={{server.mac0.device}}
      DEVICETYPE={{server.mac0.devicetype}}
      TYPE={{server.mac0.type}}
      BOOTPROTO={{server.mac0.bootproto}}
      ONBOOT=yes
      MACVLAN_PARENT=
      MACVLAN_MODE={{server.mac0.macvlan_mode}}
      IPADDR={{server.mac0.ipaddr}}
      PREFIX={{server.mac0.prefix}}
      GATEWAY={{server.mac0.gateway}}
      DNS1={{server.mac0.dns1}}
      DNS2={{server.mac0.dns2}}
      EOF
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Gerar ifdown-macvlan
    shell: |
      cat > /etc/sysconfig/network-scripts/ifdown-macvlan <<EOF
      #!/bin/bash
      #
      # initscripts-macvlan
      # Copyright (C) 2014 Lars Kellogg-Stedman
      # 
      # This program is free software: you can redistribute it and/or modify
      # it under the terms of the GNU General Public License as published by
      # the Free Software Foundation, either version 3 of the License, or
      # (at your option) any later version.
      # 
      # This program is distributed in the hope that it will be useful,
      # but WITHOUT ANY WARRANTY; without even the implied warranty of
      # MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
      # GNU General Public License for more details.
      # 
      # You should have received a copy of the GNU General Public License
      # along with this program.  If not, see <http://www.gnu.org/licenses/>.
      
      . /etc/init.d/functions
      
      cd /etc/sysconfig/network-scripts
      . ./network-functions
      
      [ -f ../network ] && . ../network
            
      CONFIG=${1}
      
      need_config ${CONFIG}
      
      source_config
      
      OTHERSCRIPT="/etc/sysconfig/network-scripts/ifdown-${REAL_DEVICETYPE}"
      
      if [ ! -x ${OTHERSCRIPT} ]; then
      OTHERSCRIPT="/etc/sysconfig/network-scripts/ifdown-eth"
      fi
      
      ${OTHERSCRIPT} ${CONFIG}
      
      ip link del ${DEVICE} type ${TYPE:-macvlan}
      EOF
      chmod 0755 /etc/sysconfig/network-scripts/ifdown-macvlan
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Gerar ifup-macvlan
    shell: |
      cat > /etc/sysconfig/network-scripts/ifup-macvlan <<EOF
      #!/bin/bash
      #
      # initscripts-macvlan
      # Copyright (C) 2014 Lars Kellogg-Stedman
      # 
      # This program is free software: you can redistribute it and/or modify
      # it under the terms of the GNU General Public License as published by
      # the Free Software Foundation, either version 3 of the License, or
      # (at your option) any later version.
      # 
      # This program is distributed in the hope that it will be useful,
      # but WITHOUT ANY WARRANTY; without even the implied warranty of
      # MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
      # GNU General Public License for more details.
      # 
      # You should have received a copy of the GNU General Public License
      # along with this program.  If not, see <http://www.gnu.org/licenses/>.
      
      . /etc/init.d/functions
      
      cd /etc/sysconfig/network-scripts
      . ./network-functions
      
      [ -f ../network ] && . ../network
            
      CONFIG=${1}
      
      need_config ${CONFIG}
      
      source_config
      
      OTHERSCRIPT="/etc/sysconfig/network-scripts/ifup-${REAL_DEVICETYPE}"
      
      if [ ! -x ${OTHERSCRIPT} ]; then
      OTHERSCRIPT="/etc/sysconfig/network-scripts/ifup-eth"
      fi
      
      ${OTHERSCRIPT} ${CONFIG}
      
      ip link add \
      link ${MACVLAN_PARENT} \
      name ${DEVICE} \
      type ${TYPE:-macvlan} mode ${MACVLAN_MODE:-private}
      
      ${OTHERSCRIPT} ${CONFIG}
      EOF
      chmod 0755 /etc/sysconfig/network-scripts/ifup-macvlan
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Instala Pnet-Container
    copy: 
      src: ./pnet-container/manpage
      dest: /usr/local/share/man/man8/pnet-container.8
      owner: root
      group: root
      mode: u=rw,g=r,o=r
    become: yes
    become_user: root
  - name: Compacta MANPAGE
    shell: |
      gzip -f /usr/local/share/man/man8/pnet-container.8
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Copia executavel PNET-CONTAINER
    copy:
      src: ./pnet-container/pnet-container
      dest: /usr/bin/
      owner: root
      group: root
      mode: u=rwx,g=rx,o=rx
    become: yes
    become_user: root
  - name: Copia executavel PNET-GENERATE-MAC
    copy:
      src: ./pnet-container/pnet-generate-mac
      dest: /usr/bin/
      owner: root
      group: root
      mode: u=rwx,g=rx,o=rx
    become: yes
    become_user: root
  - name: Exclui pnet-container.service 
    shell: |
      rm -rf /lib/systemd/system/pnet-container.service
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  - name: Desabilita SE Linux
    copy: 
      src: ./selinux
      dest: /etc/selinux/config
      owner: root
      owner: root
      mode: u=rwx,g=rwx,o=rwx
    become: yes
    become_user: root
  - name: Desabilita Firewalld
    ansible.builtin.systemd:
      state: stopped
      enabled: false
      name: firewalld
    become: yes
    become_user: root
  - name: Instala Certificado Docker Registry
    shell: |
      update-ca-trust force-enable
      cat > /etc/pki/ca-trust/source/anchors/docker.crt <<EOF
      -----BEGIN CERTIFICATE-----
      MIIENzCCAx+gAwIBAgIJANehZai2vvDzMA0GCSqGSIb3DQEBCwUAMIGwMQswCQYD
      VQQGEwJCUjEVMBMGA1UECAwMTWluYXMgR2VyYWlzMRgwFgYDVQQHDA9Qb2NvcyBk
      ZSBDYWxkYXMxEjAQBgNVBAoMCVRlbGVtaWRpYTEMMAoGA1UECwwDQ09SMSkwJwYD
      VQQDDCBkb2NrZXItcmVnaXN0cnkudGVsZW1pZGlhLm5ldC5icjEjMCEGCSqGSIb3
      ****************************************************************
      MjAzMDExMjU5MTRaMIGwMQswCQYDVQQGEwJCUjEVMBMGA1UECAwMTWluYXMgR2Vy
      YWlzMRgwFgYDVQQHDA9Qb2NvcyBkZSBDYWxkYXMxEjAQBgNVBAoMCVRlbGVtaWRp
      YTEMMAoGA1UECwwDQ09SMSkwJwYDVQQDDCBkb2NrZXItcmVnaXN0cnkudGVsZW1p
      ZGlhLm5ldC5icjEjMCEGCSqGSIb3DQEJARYUY29yQHRlbGVtaWRpYS5uZXQuYnIw
      ggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCv+5lFAnV9Qt7JrcYY9qRv
      P1jEZ9u3Fk4I0B8r3ANIBo2IgpOIV85/RQmpntQD21Ql4bhWD4umpckrKYtmXU5R
      d2Bo5BRcTDbLwEIEB2z9bhCjxE5NxOqG7/3zCBZh1IBXK9RGTDBbSHmiLtuitprK
      Ez7ctHsdj4L3FwBz4VZbIj6PFnck4WbDJfn8EcvZkAAm8P8btL86PZ7yEKeIhmad
      wb04e7t15CMzdr/AUMIc6n67s0TKd9efEHTI6nN14ldU0vu1nF2/OL/IPkYz/7um
      cYWmzdE7tsJYkKeYDarnsuyY89F0xIpHjMKfIQeGXN0O2ax20m06axcSLqajHojL
      AgMBAAGjUDBOMB0GA1UdDgQWBBRTWdydiHPIX4BMRT6ELfDiMflXOjAfBgNVHSME
      GDAWgBRTWdydiHPIX4BMRT6ELfDiMflXOjAMBgNVHRMEBTADAQH/MA0GCSqGSIb3
      DQEBCwUAA4IBAQBTyscNHRhBJujdkEia50yIUxJV9du1zW5iWreIUgmnwfzpKMKb
      NU5tleFgO4/Oy2mowoL+r9NG+TU/ulX98DuF6Y0nmsiLOhnoH1qJWlZfXQPBiMAT
      SxAfOJgDEQGO21hDL6NctVqQNyUwAgNGfyIujX+1d+XkcqtZyiIl8rSjcA5BDZO5
      oF8NJL/G/QBzhEuj0Kcii6ufRnYZtccl7jSTIqB82js0t9EmSId/kM0QKxNACd3p
      MlIwXgwGJokA6h5IcQcDBdxuBkTJCDCuxBlZ5VRJHmXRrCSSksuBW0B+831cZnhj
      0+7QtxNPMXOn8FdgwG5ZCAcKrxRZ+GgW6aL2
      -----END CERTIFICATE-----
      EOF
      update-ca-trust extract
      systemctl restart docker
    args:
      executable: /bin/bash
    become: yes
    become_user: root
  