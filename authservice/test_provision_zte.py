import select
from database import *
from log import *
from ixc_ws import *
import time
import sys
from datetime import datetime as dt
import datetime
import traceback
from config import *
from genieacs import *
import urllib3
from test_ztemanager import ZTEManager
import os
from dotenv import load_dotenv
import requests
import json
from cli import *

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

scriptname = 'provision-zte.service'

'''
# Aguarda por notificacoes de insert na tabela unauthorized e authlogs
def notify_start():
  try:
    con = psycopg2.connect(
          host=SERVICES1DB_HOST,
          database='noc2',
          user=SERVICES1DB_USER,
          password=SERVICES1DB_PASS
    )
    con.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)

    curs = con.cursor()
    curs.execute("LISTEN unauthorized;") #onus aguardando autorizacao
    curs.execute("LISTEN auth_insert;") #logs de provisionamento (checagem de configuracoes)
    #verifica se existem onus para provisionamento
    check_onus(get_unauthorized('ZTE'))

    while True:
      if select.select([con],[],[],5) != ([],[],[]):
          con.poll()
          while con.notifies:
              notify = con.notifies.pop(0)
              #print('channel: {} pid: {} payload: {}'.format(notify.channel, notify.pid, notify.payload))
              payload = json.loads(notify.payload)
              if(notify.channel == 'unauthorized'):
                  check_onus(findunauthorized(payload["record"]["serial"])) #tenta provisionar apenas a onu que foi inserida na tabela

              if(notify.channel == 'auth_insert'):
                #faz a checagem da configuracao apenas se ainda nao foi verificada
                # e o numero de tentativas nao excedeu o limite
                if(payload['record']['verified'] == 0):
                  if(payload['record']['retries'] < PROVISION_CHECK_RETRIES):
                    check_task(payload['record'])  
  except Exception as e:
    errorlog(traceback.format_exc())
    raise  
    #errorlog('Falha em notify_start: '+ str(e))                  
'''

#seleciona o tipo de checagem de configuracao
def check_task(record):
  try:
    #para todas as onus caso nao esteja em modo teste e apenas se existirem onus no modo teste
    if(((len(ONUS_TESTE) > 0) and (record['serial'].upper() in ONUS_TESTE) and (TEST_MODE)) or (TEST_MODE == False)):
      #lista de checagens possiveis
      switcher = {
          'AUTH_ZTE': check_authorization,
          'CONFIG_ZTE': check_config,
      }
      task = record['task']
      if(task in switcher):
        switcher[task](record)
      else:
        record["verified"] = 1
        updatelog(record)
        print("Invalid task: {}".format(task))
  except Exception as e:
    errorlog('Falha em check_task: '+ str(e))        

#nao implementado
def check_authorization(record):
    record["verified"] = 1
    updatelog(record)

#nao implementado
def check_config(record):
    record["verified"] = 1
    updatelog(record)


def plan_rule(model, plan, username):

    '''
    Regras ZTE				
    ==========
    Cliente	    Plano	    ONU Nova	ONU Antiga	Acao
    Novo*	    50	        F660	    null	    Provisionar
    Antigo**	50	        F660	    F660	    Provisionar
    Todos	    >50 & <100	F660	    Todos	    Provisionar
    Todos	    >=100	    F670	    Todos	    Provisionar
    
    * uma alocacao
    ** mais de uma alocacao
    '''
    valid = None
    f660 = '660' in model
    f670 = '670' in model

    if(not f660 and not f670):
        return None

    if(int(plan) > 50 and int(plan) < 100):
        if(f670):
            valid = 'F660'
        if(f660):
            return None    

    if int(plan) >= 100:
        if(f660):
            valid = 'F670'
        if(f670):
            return None    

    if valid == None:
        onus = get_onus_alocadas(username)
        #Novo
        if(len(onus) <= 1):
            if(f670):
                valid = 'F660'
        #Antigo
        else:
            anterior = onus[0]['descricao']
            if(f670):
                if '660' in anterior:
                    valid = 'F660'
            if(f660):
                if '670' in anterior:
                    valid = 'F670'
                elif not '660' in anterior:    
                    valid = 'FIBERHOME'

    return valid


def gravalog(item, task, msg, erro):
  try:
    fields = ['script', 'patrimonio', 'serial', 'mac', 'username', 
    'olt', 'olt_ip', 'placa', 'porta', 'vlan_dhcp', 'vlan_pppoe', 'vlan_iptv', 'porta_onu', 'onu_id']
    
    log = {}    
    
    for field in fields:
      if(field in item.keys()):
        log[field] = item[field]
      else:
        log[field] = None
    
    log["script"] = scriptname
    log['msg'] = msg
    log['erro'] = erro
    log['task'] = task
    insertlog(log)
  except Exception as e:
    errorlog('Falha em gravalog: '+ str(e))  
 

def onuauth(payload):

    url = os.getenv('ZTE_API') + '/auth'
    token = os.getenv('ZTE_API_TOKEN')

    headers = {
        'x-access-token': token,
        'Content-Type': 'application/json'
    }

    response = requests.post(url, data=json.dumps(payload), headers=headers, verify=False)

    return(response.text)

def onuconfig(payload):
    url = os.getenv('ZTE_API') + '/configonu'
    token = os.getenv('ZTE_API_TOKEN')

    headers = {
        'x-access-token': token,
        'Content-Type': 'application/json'
    }

    response = requests.post(url, data=json.dumps(payload), headers=headers, verify=False)

    return(response.text)


def auth_zte(item):
  #try:
    
    provision_mode = os.getenv('PROVISION_MODE')
    
    item["name"] = item["username"]
    item["slot"] = item["placa"]
    item["pon"] = item["porta"]
    item["type"] = item["modelo"]
    item['vlan_dhcp'] = TR069_DHCP_VLANID
    vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])

   
    item["vlan_pppoe"] = int(vlans['vlan_pppoe'])
    item["vlan_iptv"] = int(vlans['vlan_iptv'])

    device = {
            "olt": item["olt"],
            "olt_ip" : item["olt_ip"],
            "olt_modelo": "ZTE",
            "serial" : item["serial"],
            "type": item["modelo"],
            "name" : item["username"],
            "vlan_tr069" : item['vlan_dhcp'],
            "vlan_pppoe" : item['vlan_pppoe'],
            "vlan_iptv_unicast" : item['vlan_iptv'], 
            "vlan_iptv_multicast" : 2,
            "chassi": 1,
            "slot": item["placa"],
            "pon": item["porta"]
    }

    if 'mac' in item:
      device["mac"] = item["mac"]

    if 'patrimonio' in item:  
      device["patrimonio"] = item["patrimonio"]

    if 'modelo' in item:
      device["onu_modelo"] = item["modelo"]
    
    # adicionado em 19/05/2022############
    # remove a onu de todas olts fiberhome
    olts = unm_oltlist()
    
    
    for olt in olts:
      removewhitelist(olt["ip"], item["serial"], True)

    # desautoriza todas as onus nomeadas com o mesmo username, exceto a nova
    provisions = findauthorizedonu(None, item["username"])
    for provision in provisions:
      if(provision['serial'].upper() != item["serial"].upper()):
        removewhitelist(provision['olt_ip'], provision['serial'], True)
    ################################################################## 

    # se a onu ja estava autorizada em uma olt ZTE: 
    # - caso seja o mesmo usuario, altera o id da vlan pppoe

    previous_device = getdevice(item["serial"])
    #previous_device = {"name": "testes.correa", "olt_ip": "*************", "olt": "OLT And HOMOLOGACAO"}
    if(previous_device):
        msg = "Removendo provisionamento anterior da ONU"
        print(msg)
        manager = ZTEManager(previous_device['olt_ip'], previous_device['olt'])
        response = manager.deauth(None, previous_device['chassi'], previous_device['slot'], previous_device['pon'], previous_device['onuid'])
        manager.disconnect()
        del manager

        if(previous_device['name'] == device['name']):
            msg = "Alterando o ID da vlan PPPoe"
            print(msg)
            changepppoevlanid(item['mac'], device['vlan_pppoe'])
    
    #time.sleep(5)

    if(provision_mode=='API'):    
      #autoriza a onu
      msg = 'Autorizando ONU {}'.format(item["serial"])
      gravalog(item, 'AUTH_ZTE', msg, 0)
      print(msg) 
      response = onuauth(device) #via api
      print(response)
      #configura a onu
      msg = "Configurando ONU [DHCP id:{} - PPPoE id: {}]".format(item['vlan_dhcp'], item["vlan_pppoe"])
      gravalog(item, 'CONFIG_ZTE', msg, 0)
      print(msg)
      time.sleep(5)
      response = onuconfig(device) #via api
      print(response)
    else:    
      #autoriza a onu
      msg = 'Autorizando ONU {}'.format(item["serial"])
      gravalog(item, 'AUTH_ZTE', msg, 0)
      print(msg) 
      manager = ZTEManager(item['olt_ip'], item['olt'])
      response = manager.auth(item['serial'], item['modelo'], item['username']) #via objeto
      
      #ONU já foi autorizada
      if(response == 'ONU not found'):
        print("Provisionamento cancelado. ONU ja foi autorizada")
        return True

      if('onuid' in response):
        item['onuid'] = response['onuid']
        device['onuid'] = response['onuid']
      
      if('chassi' in response):
        item['chassi'] = response['chassi']  
        device['chassi'] = response['chassi']  
      #configura a onu
      msg = "Configurando ONU [DHCP id:{} - PPPoE id: {}]".format(item['vlan_dhcp'], item["vlan_pppoe"])
      gravalog(item, 'CONFIG_ZTE', msg, 0)
      print(msg)
      time.sleep(1)

      #response = manager.config_onu(item['serial'], int(item['vlan_dhcp']), int(item['vlan_pppoe']), int(item['vlan_iptv']), 2) #via objeto
      response = manager.config_onu(device['chassi'], device['slot'], device['pon'], device['onuid'], int(item['vlan_dhcp']), int(item['vlan_pppoe']), int(item['vlan_iptv']), 2) #via objeto
      print(response)
      if('script' in response):
        device['script'] = response['script']
      manager.disconnect()
    
    updatedevice(device)

    #se a onu nao estava provisionada anteriormente ou alterou o usuario,
    # remove as tags e altera o tempo de inform para a onu provisionar novamente
    if (item['tr069'] == 1):
        if(previous_device):
            
            if(previous_device['name'] != device['name']):
                removetag(item['mac'], 'Provisioned')
                removetag(item['mac'], 'PPPoE')
                removetag(item['mac'], 'Wifi')
                addtag(item['mac'], 'Provisioning')
                changeinformperiod(item['mac'], 10)
                #removeacs(item['mac'])    
        else:
            removetag(item['mac'], 'Provisioned')
            removetag(item['mac'], 'PPPoE')
            removetag(item['mac'], 'Wifi')
            addtag(item['mac'], 'Provisioning')
            changeinformperiod(item['mac'], 10)
            #removeacs(item['mac'])  

    #if (item['tr069'] == 1):
    #  removeacs(item['mac'])
    #return {"status": "SUCCESS", "result": result}

  #except Exception as e:
  #  errorlog('Falha em auth_zte: '+ str(e))        


def start_provision(item):
  #try:
    if(item['olt_modelo'] == 'FBT'):
      return False
    #atualiza o status da onu
    item["status"] = "PROVISIONING"
    update_unauthorized_item(item)
    
    print('ONU sera provisionada {}'.format(item['serial']))
    
    pausa = 1
    aviso = ""

    ############################################################################
    #carrega os dados via banco para atualizar os dados tecnicos no ixc
    try:
      payload = get_dados_tecnicos(item['serial'], item['olt_ip'], '0-0-{}-{}'.format(item["placa"], item["porta"]))
      if(payload):
        if('id' in payload and payload['id'] != None):
          #atualizar os dados tecnicos no ixc via api
          msg = '[{}] Atualizando dados tecnicos no IXC {} '.format(scriptname, item["serial"])
          print(msg)
          response = update_dados_tecnicos(payload)
          response = json.loads(response) 
          if('type' in response):
            if(response['type'] == 'error'):
              print('Atualizando dados tecnicos no IXC {}..ERRO'.format(item["serial"]))
              return False
            else:
              msg = 'Atualizando dados tecnicos no IXC'.format(item["serial"])
              gravalog(item, 'IXC', msg, 0)
          else:
            print('Atualizando dados tecnicos no IXC {}..ERRO'.format(item["serial"]))
            return False
      else:
        print('Atualizando dados tecnicos no IXC {}..ERRO'.format(item["serial"]))
        return False
    except:
      pass

    ###########################################################################    



    if('ZTE' in item['serial'].upper()):
      if(item['olt_modelo'] == 'FBT'):
        item['modelo'] = 'GPON CURRENCY SFU'
      item['tr069'] = 1
    else:
      item['tr069'] = 0  

    # autoriza a onu
    auth_zte(item)

  #except Exception as e:
  #  errorlog('Falha em start_provision: '+ str(e))    


#verifica se as onus possuem todos os dados para iniciar o provisionamento
def check_onus(onus):
  try:
    if(onus):
      onus = json.loads(onus)

      for onu in onus:

        if(((len(ONUS_TESTE) > 0) and (onu['serial'].upper() in ONUS_TESTE) and (TEST_MODE)) or (TEST_MODE == False)):
          
          #TESTE############
          #onu = dict(onu)
          #onu["patrimonio"] = 1 
          #onu["modelo"] = 'AN5506-04-F1' 
          #onu["username"] = "fibrateste2"
          #onu["login_ativo"] = "S"
          #onu["mac"] = "18:A3:E8:F2:FD:88"
          ##################
          missing_params = []

          # onu nao pode estar na lista para ignorar
          if(checkignorelist(serial=onu['serial'])):
            missing_params.append('IGNORE')

          # obrigatorio patrimonio
          if(not ('patrimonio' in onu and onu['patrimonio'])):
              missing_params.append('PATRIMONIO')

          # obrigatorio modelo
          if(not('modelo' in onu and onu['modelo'])):
              missing_params.append('MODELO')

          # obrigatorio username
          if(not('username' in onu and onu['username'])):
              missing_params.append('USERNAME')

          # obrigatorio mac
          if(not('mac' in onu and onu['mac'])):
              missing_params.append('MAC')

          # obrigatorio login ativo
          if(not('login_ativo' in onu and onu['login_ativo'] == 'S')):
              missing_params.append('LOGIN ATIVO')

          # obrigatorio pacote wifi para onus zte que nao estejam na lista para ignorar
          if('username' in onu and onu['username'] and 'ZTE' in onu['serial'].upper() and not(checkignorelist(serial=onu['serial'], pacote_wifi=1)) and not(checkignorelist(username=onu['username'], pacote_wifi=1)) and not(get_pacotewifi(onu['username']))):
            missing_params.append('PACOTE WIFI')

          # Libera apenas se tiver a alocacao do comodato e nao esteja na tabela ignore com o parametro comodato=1
          if(not('id_comodato' in onu and onu['id_comodato']) and not(checkignorelist(serial=onu['serial'], comodato=1)) and not(checkignorelist(username=onu['username'], comodato=1))):
            missing_params.append('ALOCACAO COMODATO')

          # verifica se o modelo da onu e compativel com o plano.
          if('plano' in onu and 'modelo' in onu and 'username' in onu and onu['username'] and not(checkignore(serial=onu['serial'], plano=1)) and not(checkignore(username=onu['username'], plano=1))):
            #pending = plan_rule(onu['modelo'], onu['plano'])
            pending = plan_rule(onu['modelo'], onu['plano'], onu['username'])
            if(pending):
              missing_params.append('ONU INCOMPATIVEL. NECESSARIO {}'.format(pending))


          if(len(missing_params) == 0):
              start_provision(dict(onu))
          else:
            if(IGNORE_LIST_ENABLED):
              if(onu['status'] != 'IGNORED'):
                onu['status'] = 'FALTANDO: {}'.format(
                                    ', '.join(missing_params))
                update_unauthorized_item(onu)
            else:
              onu['status'] = 'FALTANDO: {}'.format(
                                    ', '.join(missing_params))
              update_unauthorized_item(onu)
  except Exception as e:
    errorlog(traceback.format_exc())
    raise
    #errorlog('Falha em check_onus: '+ str(e))           

#load_dotenv()
#notify_start()    

item = {
    "olt_ip": "*************",
    "olt": "OLT And ClubeUva",
    "olt_modelo": "ZTE",
    "serial": "ZTEGCE4CF13F",
    "modelo": "F670L",
    "username": "joselene.guimaraes",
    "mac": "C0:94:AD:AA:4B:04",
    "placa" : 3,
    "porta" : 2
}

start_provision(item)