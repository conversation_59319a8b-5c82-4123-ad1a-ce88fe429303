import argparse
import time
import os
import redis
from rq import Worker, Queue, Connection
from dotenv import load_dotenv

load_dotenv()

parser = argparse.ArgumentParser()
parser.add_argument('--queue', required=True)
args = parser.parse_args()

listen = [args.queue]

redis_host = os.getenv('REDIS_HOST', 'localhost')
redis_port = os.getenv('REDIS_PORT', 6379)
redis_pass = os.getenv('REDIS_PASS', 'admin')

conn = redis.Redis(
    host=redis_host,
    port=redis_port,
    password=redis_pass)

if __name__ == '__main__':
    with Connection(conn):
        worker = Worker(list(map(Queue, listen)))
        worker.work(with_scheduler=True)

print(f"Iniciando worker para a fila: {args.queue}")