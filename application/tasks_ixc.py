#!/usr/bin/python
# -*- coding: utf-8 -*-
import requests
import base64
import json
import os
import urllib3
from database import *
from dotenv import load_dotenv
import time
import datetime
import re
from ixc_ws import *

load_dotenv()

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

#atualiza dados tecnicos no ixc
def task_updateixc(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs
        payload = get_dados_tecnicos(item['serial'], item['olt_ip'], '0-0-{}-{}'.format(item["placa"], item["porta"]))
        if(payload):
            if('id' in payload and payload['id'] != None):
                if("mac" in payload):
                    mac = payload["mac"]
                    if re.match("[0-9a-f]{2}([-:]?)[0-9a-f]{2}(\\1[0-9a-f]{2}){4}$", mac.lower()):
                        response = update_dados_tecnicos(payload)
                        response = json.loads(response) 
                        if('type' in response):
                            if(response['type'] == 'error'):
                                update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "Erro API IXC: {}".format(response["message"].encode('utf-8')),
                                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                                })
                                raise Exception("Erro API IXC: {}".format(response["message"].encode('utf-8'))) 
                            else:
                                update_job({
                                    'id': args[0], 
                                    'status' :'finished', 
                                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                                })         
                                return True

                        else:
                            update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "Erro API IXC",
                                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                            })       
                            raise Exception("Erro API IXC")     
                    else:
                        update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "MAC Invalido",
                                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                        })       
                        raise Exception("MAC invalido") 
                else:
                    update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "MAC Nao Informado",
                                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                    })        
                    raise Exception("MAC nao informado") 
            else:
                update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "Dados Tecnicos Nao Localizados",
                                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                })
                raise Exception("Dados tecnicos nao localizados")         
        else:
            update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': "Dados Tecnicos Nao Localizados",
                                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })        
            raise Exception("Dados tecnicos nao localizados")                 
    except Exception as e:
        update_job({
                                    'id': args[0], 
                                    'status' :'failed', 
                                    'exc_info': str(e),
                                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })        
        raise