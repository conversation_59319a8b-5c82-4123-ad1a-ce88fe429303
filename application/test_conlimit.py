import threading
import time
from ztemanager import ZTEManager
 
olt = {'ip': '*************', 'name': 'OLT And ZTE_AdsShopping_01'}

def getunauth(id):
    print(f"[Thread {id}] Iniciando")
    manager = ZTEManager(olt['ip'], olt['name'])
    #consulta onus aguardando provisionamento
    response = manager.list_unauth()
    if(len(response) > 0):
        print('{:02d} onu(s) em {} {} encaminhada(s) para a checagem'.format(len(response), olt['name'], olt['ip']))
    else:
        print('Nenhuma onu aguardando provisionamento em {} {}'.format(olt['name'], olt['ip']))

    manager.disconnect()
    del manager
    print(f"[Thread {id}] Finalizando")

# Função que cria e inicia múltiplas threads
def executar_multithread(numero_threads=20):
    threads = []

    for i in range(numero_threads):
        t = threading.Thread(target=getunauth, args=(i,))
        threads.append(t)
        t.start()

    # Espera todas as threads terminarem
    for t in threads:
        t.join()

    print("Todas as threads finalizaram.")



if __name__ == '__main__':
    try:
        # Executa
        executar_multithread()
    except Exception as e:
        print('Ocorreu um erro em __main__: '+str(e))
