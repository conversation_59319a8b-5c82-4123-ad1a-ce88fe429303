from flask import request, jsonify
from functools import wraps
import jwt
from dotenv import load_dotenv
import os

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        load_dotenv()
        token = None
        if 'x-access-token' in request.headers:
            token = request.headers['x-access-token']

        if not token:
            return jsonify({'message' : 'Token is missing !!'}), 401
  
        try:
            data = jwt.decode(token, os.getenv('APP_SECRET'), algorithms=["HS256"])


        except:
            return jsonify({
                'message' : 'Token is invalid !!'
            }), 401
        return  f(*args, **kwargs)
  
    return decorated