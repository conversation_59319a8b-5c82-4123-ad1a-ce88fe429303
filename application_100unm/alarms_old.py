#!/usr/bin/python
# -*- coding: utf-8 -*-

import pexpect
from dotenv import load_dotenv
import os
from database import *
import time
import sys
import traceback
from genieacs import *
from rq import Queue
from rq.job import Job
from worker_provision_queue import conn
from tasks_provision import *
import uuid
from datetime import date

scriptname = 'ALARMS[************]'

load_dotenv()

def start_alarm():
  try:
    alarms = getalarms()
    onus = []

    for alarm in alarms:

      str = alarm['calarmexinfo']
      #str = 'physical ID=FHTT022c3ba8;SN:LOID=;SN:password=;|physical ID=ZTEGc8cbf6d7;SN:LOID=;SN:password=;|'
      str = str[:-1]
      rows = str.split('|')
      for row in rows:
        onu = {}
        row = row[:-1]
        fields = row.split(';') 
        for field in fields:
          value = field.split('=')
          if(value[0].lower() == 'physical id'):
              #if(alarm['olt_ip'] != '***********'):
              if(alarm['olt_ip'] not in [m['ip'] for m in monitors()]):
                onu['source'] = 'ALARMS[************]'
                onu['serial'] = value[1]
                onu['olt'] = alarm['olt']
                onu['olt_ip'] = alarm['olt_ip']
                s = alarm['clocationinfo'] 
                onu['placa'] = int(s[s.find("[")+1:s.find("]")])
                onu['porta'] = int(alarm['porta'])
                onu['data'] = alarm['data']
                onu['mac'] = None
                onu['username'] = None 
                onu['modelo'] = None 
                onu['patrimonio'] = None 
                onu['login_ativo'] = None
                onu['data_comodato'] = None
                onu['id_comodato'] = None
                onu['status'] = 'WAITING'

                patrimonio = getpatrimonio_alocado(onu['serial'])  

                if(patrimonio):
                  onu['mac'] = patrimonio['mac']
                  onu['modelo'] = patrimonio['modelo']
                  onu['patrimonio'] = patrimonio['patrimonio']
                  onu['login_ativo'] = patrimonio['login_ativo']
                  onu['data_comodato'] = patrimonio['data_comodato']
                  onu['id_comodato'] = patrimonio['id_comodato']
                  onu['username'] = patrimonio['login']  
                  onu['nome'] = patrimonio['login']  
                  onu['login_ativo'] = patrimonio['login_ativo']
                  onu['plano'] = patrimonio['plano']

                  if('ZTE' in onu['serial'].upper()):
                    onu['tr069'] = 1
                  else:
                    onu['tr069'] = 0  

                if((os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None) and (checkignorelist(serial=onu["serial"].upper()))):
                  msg = 'ONU {} esta na lista para ignorar e nao sera autorizada'.format(onu["serial"])
                  print(msg)
                else:
                  #se for informada uma onu de testes, provisiona apenas esta onu
                  if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                    print('ONU sera provisionada no sistema antigo') 
                    print(onu)
                    onus.append(onu)    
     
    
    #se existir alguma onu nao provisionada em olt no sistema antigo de provisionamento adiciona na tabela unauthorized
    if(len(onus) > 0):
      print(onus)
      update_unauthorized(onus, scriptname)
  except:
    raise
  
start_alarm()
