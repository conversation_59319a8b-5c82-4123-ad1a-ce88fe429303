#!/usr/bin/env python3
"""
Teste para verificar apenas a lógica de dependências sem executar no Redis
"""

import os
import sys
import uuid
from dotenv import load_dotenv

# Adicionar o diretório atual ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_provision_tasks
import heapq

load_dotenv()

def test_dependency_logic():
    """
    Testa apenas a lógica de dependências sem executar no Redis
    """
    
    # Primeiro, vamos verificar quais OLTs estão disponíveis no banco
    from database import __nocdb
    import psycopg2.extras
    
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Verificar se há tarefas configuradas
        sql_query = """
        SELECT DISTINCT m.ip, COUNT(t.id) as task_count 
        FROM authservice.monitors m 
        LEFT JOIN authservice.tasks t ON t.task_group_id = m.task_group 
        GROUP BY m.ip 
        HAVING COUNT(t.id) > 0 
        LIMIT 1
        """
        cur.execute(sql_query)
        available_olts = cur.fetchall()
        con.close()
        
        if not available_olts:
            print("ERRO: Nenhuma OLT com tarefas configuradas encontrada")
            return False
            
        test_olt_ip = available_olts[0]['ip']
        
    except Exception as e:
        print(f"Erro ao buscar OLTs: {e}")
        return False
    
    print("=== Teste de Lógica de Dependências ===")
    print(f"Testando com OLT: {test_olt_ip}")
    
    try:
        # Buscar as tarefas
        tasks = get_provision_tasks(olt=test_olt_ip)
        
        if not tasks:
            print("ERRO: Nenhuma tarefa encontrada para esta OLT")
            return False
            
        print(f"\nTarefas encontradas ({len(tasks)}):")
        for task in tasks:
            print(f"  - ID: {task['id']}, Task: {task['task']}, Depends_on: {task['depends_on']}, Order: {task['order']}")
        
        # Criar um mapeamento de task name para task id para resolver dependências
        task_name_to_id = {task['task']: task['id'] for task in tasks}
        print(f"\nMapeamento nome -> ID:")
        for name, task_id in task_name_to_id.items():
            print(f"  '{name}' -> {task_id}")
        
        # Estruturas para ordenação topológica
        tasks_by_id = {task['id']: task for task in tasks}
        graph = {}
        in_degree = {task['id']: 0 for task in tasks}
        
        # Construir grafo de dependências
        print(f"\nConstruindo grafo de dependências:")
        for task in tasks:
            if task['depends_on'] is not None:
                # task['depends_on'] contém o nome da tarefa dependente, precisamos encontrar o ID
                parent_task_id = task_name_to_id.get(task['depends_on'])
                if parent_task_id and parent_task_id in tasks_by_id:
                    graph.setdefault(parent_task_id, []).append(task['id'])
                    in_degree[task['id']] += 1
                    print(f"  Tarefa {task['id']} ('{task['task']}') depende de {parent_task_id} ('{task['depends_on']}')")
                else:
                    print(f"  AVISO: Dependência não encontrada para tarefa {task['id']}: '{task['depends_on']}'")
        
        print(f"\nGrafo construído:")
        print(f"  Arestas: {graph}")
        print(f"  Graus de entrada: {in_degree}")
        
        # Ordenação topológica com prioridade pela ordem
        heap = []
        for task_id, degree in in_degree.items():
            if degree == 0:
                heapq.heappush(heap, (tasks_by_id[task_id]['order'], task_id))
        
        sorted_tasks = []
        print(f"\nOrdenação topológica:")
        while heap:
            order, task_id = heapq.heappop(heap)
            sorted_tasks.append(task_id)
            task = tasks_by_id[task_id]
            print(f"  {len(sorted_tasks)}. Tarefa {task_id} ('{task['task']}') - Ordem: {order}")
            
            for neighbor in graph.get(task_id, []):
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    heapq.heappush(heap, (tasks_by_id[neighbor]['order'], neighbor))
        
        # Verificar se todas as tarefas foram ordenadas
        if len(sorted_tasks) != len(tasks):
            print(f"\n❌ ERRO: Nem todas as tarefas foram ordenadas!")
            print(f"   Esperado: {len(tasks)}, Ordenado: {len(sorted_tasks)}")
            return False
        
        # Simular criação de jobs com dependências
        print(f"\nSimulação de criação de jobs:")
        job_map = {}  # Mapeia task_id -> job_id simulado
        
        for i, task_id in enumerate(sorted_tasks):
            task = tasks_by_id[task_id]
            
            # Simular job_id
            job_id = f"job_{i+1}_{task_id}"
            
            # Obter dependência
            depends_on_job_id = None
            if task['depends_on']:
                parent_task_id = task_name_to_id.get(task['depends_on'])
                if parent_task_id:
                    depends_on_job_id = job_map.get(parent_task_id)
            
            job_map[task['id']] = job_id
            
            dep_info = f"depende de {depends_on_job_id}" if depends_on_job_id else "sem dependência"
            print(f"  Job {job_id} ('{task['task']}') - {dep_info}")
        
        print(f"\n✅ Teste de lógica concluído com sucesso!")
        print(f"   - {len(tasks)} tarefas processadas")
        print(f"   - Dependências resolvidas corretamente")
        print(f"   - Ordenação topológica aplicada")
        return True
        
    except Exception as e:
        print(f"\n❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_dependency_logic()
    sys.exit(0 if success else 1)
