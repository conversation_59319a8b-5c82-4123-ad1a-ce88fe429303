#!/usr/bin/python
# -*- coding: utf-8 -*-
import requests
import base64
import json
from config import *

def update_dados_tecnicos(payload):

    host = IXC_WEBSERVICE
    url = "https://{}/webservice/v1/radusuarios/{}".format(host, payload['id'])
    token = IXC_TOKEN.encode('utf-8')

    payload['interface_transmissao_fibra'] = payload['interface_transmissao']

    '''    
    # campos obrigatorios
    payload = {
        'autenticacao_por_mac': 'P',
        'ativo': 'S',
        'id_contrato': 43615,
        'id_cliente': 20971,
        'id_grupo': 958,
        'login': 'testefibra100',
        'senha': 'testefibra',
        'ip': '**************',
        'onu_mac': '8c:dc:02:9d:b5:d4',
        'auto_preencher_ip': 'H',
        'auto_preencher_mac': 'H',
        'relacionar_ip_ao_login': 'H',
        'relacionar_mac_ao_login': 'H',
        'autenticacao': 'L',
        'login_simultaneo': '1',
        'fixar_ip': 'S',
        'senha_md5': 'N',
        'tipo_vinculo_plano': 'D',
        'id_df_projeto': 7,
        'id_transmissor': 419,
        'interface_transmissao': 1383,
        'interface_transmissao_fibra': 1383 # Novo campo
    }
    '''

    headers = {
        'ixcsoft': '',
        'Authorization': 'Basic {}'.format(base64.b64encode(token).decode('utf-8')),
        'Content-Type': 'application/json; charset=utf-8'
    }

    #corrige problema na codificacao
    payload = json.dumps(
        payload, indent=1, ensure_ascii=False)
    payload = payload.encode('utf-8').decode('latin-1')
    payload = json.loads(payload)
    #################################

    response = requests.put(url, json=payload, headers=headers, verify=False)

    return(response.text)
