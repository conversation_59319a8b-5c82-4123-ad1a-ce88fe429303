jQuery(function () {
    const monitorSocket = io('https://' + document.domain + ':' + location.port + '/monitor', { transports: ['websocket'] });

    var authlogs = [];
    var limit = 20;
    var service_status = 'VERIFICANDO...';
    var service_lastuptime = undefined
    $('#status').text(service_status);

    setInterval(function () {
        $('#status').text(service_status);
        if (service_status == 'UP') {
            $('#status').css('color', 'green');
        } else {
            $('#status').css('color', 'red');
        }
        $('#last_uptime').text('(' + service_lastuptime + ')');
        service_status = 'DOWN';
    }, 10000);


    monitorSocket.on('unauthorized', function (data) {
        var rows = $.map(data, function (item, index) {
            return '<tr><td>' + item.status + '</td>' +
                '<td>' + new Date(item.data_alarme).toLocaleString() + '</td>' +
                '<td>' + new Date(item.data_atualizacao).toLocaleString() + '</td>' +
                '<td>' + item.serial + '</td>' +
                '<td>' + item.username + '</td>' +
                '<td>' + item.olt + '</td>' +
                '<td>' + item.placa + '</td>' +
                '<td>' + item.porta + '</td>' +
                '<td>' + item.script + '</td></tr>';
        });
        $('#unauthorized tbody').html(rows.join(''));
    });

    monitorSocket.on('authlogs', function (data) {

        var index = authlogs.findIndex(x => x.id === data.id);
        if (index > -1) {
            authlogs[index] = data;
        } else {
            authlogs.push(data);
            //authlogs = authlogs.slice(authlogs.length - limit, limit + (authlogs.length - limit)).reverse();
            authlogs.sort(function (a, b) {
                var keyA = new Date(a.data),
                    keyB = new Date(b.data);
                if (keyA > keyB) return -1;
                if (keyA < keyB) return 1;
                return 0;
            });

        }

        var rows = $.map(authlogs, function (item, index) {
            var row = '<tr><td>' + item.id + '</td>' +
                '<td>' + new Date(item.data).toLocaleString() + '</td>' +
                '<td>' + item.task + '</td>' +
                '<td>' + item.log + '</td>' +
                '<td>' + item.serial + '</td>' +
                '<td>' + item.username + '</td>';
            if (item.verified == 0) {
                row += '<td><span class="badge rounded-pill bg-warning text-dark">não</span></td>';
            } else {
                row += '<td><span class="badge rounded-pill bg-success">sim</span></td>';
            }
            row += '<td>' + item.retries + '</td>' +
                '<td>' + item.last_retry + '</td></tr>';
            return row;
        });

        $('#authlogs tbody').html(rows.join(''));

    });

    monitorSocket.on('service_status', function (data) {
        service_status = 'UP';
        service_lastuptime = new Date(data.now).toLocaleString();
        $('#status').text(service_status);
        $('#status').css('color', 'green');
        $('#last_uptime').text('(' + service_lastuptime + ')');
    });

    monitorSocket.on('connect', function (data) {
        monitorSocket.emit('today');
    });

    monitorSocket.on('today', function (data) {
        $('#today').text(data.count);
    });

    monitorSocket.on('logs', function (data) {
        authlogs = data;

        var rows = $.map(data, function (item, index) {
            var row = '<tr><td>' + item.id + '</td>' +
                '<td>' + new Date(item.data).toLocaleString() + '</td>' +
                '<td>' + item.task + '</td>' +
                '<td>' + item.log + '</td>' +
                '<td>' + item.serial + '</td>' +
                '<td>' + item.username + '</td>';
            if (item.verified == 0) {
                row += '<td><span class="badge rounded-pill bg-warning text-dark">não</span></td>';
            } else {
                row += '<td><span class="badge rounded-pill bg-success">sim</span></td>';
            }
            row += '<td>' + item.retries + '</td>' +
                '<td>' + item.last_retry + '</td></tr>';
            return row;
        });

        $('#authlogs tbody').html(rows.join(''));
    });


});