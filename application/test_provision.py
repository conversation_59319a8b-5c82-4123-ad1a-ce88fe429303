import datetime
from test_tasks_provision import *

onu = {
    'olt': 'OLT Pcs Manhatan CPD 1',
    'olt_model': 'FBT',
    'olt_ip': '***********',
    'placa': 12,
    'porta': 16,
    'serial': 'ZTEGC4B347CB',
    'modelo': 'F670L',
    'data': datetime.datetime.now(),
    'source': 'DISCOVERY_RQ[************]',
    'mac': 'E01954DA1A18',
    'patrimonio': '66209', 
    'login_ativo': 'S', 
    'data_comodato': datetime.date(2020, 9, 24), 
    'id_comodato': 28465, 
    'username': 'testes.f670', 
    'nome': 'testes.f670', 
    'plano': '0.001',
    'tr069': 1,
    'vlan_pppoe': 134
}

task_provision(onu)



