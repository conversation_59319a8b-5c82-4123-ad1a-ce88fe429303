#!/usr/bin/python
# -*- coding: utf-8 -*-

from fhmanager import FHManager
from db import *
from dotenv import load_dotenv
import os
from cli import *
from tl1 import *
from ixc_ws import *

load_dotenv()

def task_addpreset(*args, **kwargs):
    try:
        item = kwargs

        
    except Exception as e:
        raise Exception("Erro ao adicionar a vlan {} no veip: {}".format(item["vlan_id"], str(e)))        
