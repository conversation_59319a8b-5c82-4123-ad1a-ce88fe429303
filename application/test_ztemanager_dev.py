from ztemanager_dev import ZTEManager

if __name__ == '__main__':
    '''
    olt_destino = {
        "name": "OLT And Homologacao",
        "ip": "***************",

    }
    '''

    olt = {
        "name": "OLT And ZTE_AdsShopping_01",
        "ip": "*************",
        "slot": "15",
        "pon": "13",
        "chassi": "1"
    }

    manager = ZTEManager(olt['ip'], olt['name'])
    #response = manager.show_detail('ZTEGD14E85A3')
    
    #response = manager.deauth(None, 1, 9, 1, [1])
    #print(response)
    
    #response = manager.deauth(None, 1, 9, 1, [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55])
    
    response = manager.list_auth(olt['chassi'], olt['slot'], olt['pon'])
    #response = manager.list_auth(1, 15, 11)

    for onu in response['onus']:
        response = manager.show_config_onu(chassi=olt['chassi'], slot=olt['slot'], pon=olt['pon'], onuid=onu['id'])
        #print(response)
        if not 'TELEMIDIA-NAVEGACAO' in response:
            print(f"ONU {onu['sn']} nao configurada corretamente")
    
    

    #response = manager.show_config_onu(chassi=1, slot=13, pon=7, onuid=62)
    #response = manager.find_auth('ZTEGD15ACB13')
    #if not 'TELEMIDIA-NAVEGACAO' in response:
    #    print("ONU not found or not authorized")
    #else:
    #    print("ONU found and authorized")

    #print(response)

    
    manager.disconnect()
    del manager

