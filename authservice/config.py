#!/usr/bin/python
# -*- coding: utf-8 -*-

from datetime import datetime

#================================== IMPORTANTE ==================================================
# MODO TESTE: Habilitar TEST_MODE. Seriais devem estar em caixa alta 
# apenas os seriais informados serao tratados nos scripts alarms, discovery, reauth e provision
# MODO PRODUCAO: Desabilitar TEST_MODE
TEST_MODE = False
ONUS_TESTE = []
IGNORE_LIST_ENABLED = True # Verifica lista de onus ignoradas

# Ignora alocacao de comodato no reprovisionamento
# em onus alocadas antes da data abaixo
REAUTH_IGNORE_INVENTORY_NUMBER = datetime(2021, 6, 27)

# Ignora verificação de plano no provisionamento
# em onus alocadas antes da data abaixo
REAUTH_IGNORE_PLAN = datetime(2021, 7, 13)

#================================================================================================


SERVICES1DB_USER="noc2"
SERVICES1DB_PASS="noc2@2015!"
SERVICES1DB_HOST="pgsql-services1.pocos-net.com.br"
SERVICES1DB_PORT=5432
UNMDB_USER="fdw_user"
UNMDB_PASS="vislecaina"
UNMDB_HOST="************"
ACS_IP="*************" 
#IXC_HOST="*************"
IXC_HOST="*************"
IXC_DATABASE="ixcprovedor"
IXC_USER = "leitura"
IXC_PASS = "RuskINHanaKeTATRaNaT"
IXC_TOKEN = "2:53634997c36c23d451158d695685b2a821b20e8748c868fa6c7a29f0fcf4d632"
#2:53634997c36c23d451158d695685b2a821b20e8748c868fa6c7a29f0fcf4d632
#IXC_WEBSERVICE="ixc.telemidia.net.br"
IXC_WEBSERVICE="central.telemidia.net.br"
UNMTL1_HOST="************"
UNMTL1_PORT=3337
UNMTL1_USER="ftthtl1"
UNMTL1_PASS="ftthtl1123*"
CLI_USER="GEPON"
CLI_PASS="*Cass1ambapre-ta#"
TR069_DHCP_VLANID = 10
TR069_PROFILEID = 2
DISCOVERY_UPDATE_TIME = 80 
AUTH_UPDATE_TIME = 20
QUERYALARM_UPDATE_TIME = 60
PROVISION_CHECK_RETRIES = 5
OLT_ZTE_PORT=22
OLT_ZTE_USERNAME="telemidia"
OLT_ZTE_PASSWORD="@Cass1ambapre-ta!"
OLT_ZTE_WAN_PROFILE="TELEMIDIA-NAVEGACAO"
OLT_ZTE_IPTV_PROFILE="TELEMIDIA-IPTV"
CENTRAL_ASSINANTE_HOST="*************"
CENTRAL_ASSINANTE_DATABASE="portal_assinante"
CENTRAL_ASSINANTE_USER = "provision"
CENTRAL_ASSINANTE_PASS = "DAsjq%7kjsa01*"
