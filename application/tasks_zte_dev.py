#!/usr/bin/python
# -*- coding: utf-8 -*-

from ztemanager_dev import ZTEManager
from database import *
from dotenv import load_dotenv
import os
from cli import *
from tl1 import *
from ixc_ws import *
import time
import datetime

load_dotenv()

"""
Modulo utilizado no pre-provisionamento de ONUs
Implementa tarefa separada para adicionar ONU no banco de dados

Ainda nao esta em producao

"""

def task_adddevicetodb(*args, **kwargs):
    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs

        device = {
            "olt": item["olt"],
            "olt_ip" : item["olt_ip"],
            "olt_modelo": "ZTE",
            "serial" : item["serial"],
            "type": item["modelo"],
            "name" : item["username"],
            "vlan_tr069" : item['vlan_dhcp'],
            "vlan_pppoe" : item['vlan_pppoe'],
            "vlan_iptv_unicast" : item['vlan_iptv'], 
            "vlan_iptv_multicast" : 2,
            "chassi": 1,
            "slot": item["placa"],
            "pon": item["porta"]
        }

        if 'mac' in item:
            device["mac"] = item["mac"]

        if 'patrimonio' in item:  
            device["patrimonio"] = item["patrimonio"]

        if 'modelo' in item:
            device["onu_modelo"] = item["modelo"]

        manager = ZTEManager(item['olt_ip'], item['olt'])
        response = manager.list_auth(device['chassi'], device['slot'], device['pon'])
        manager.disconnect()
        del manager

        if(not 'onus' in response):
            update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': "Erro ao adicionar a onu no banco de dados",
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })       
            raise Exception("Erro ao adicionar a onu no banco de dados")

        encontrada = False
        for onu in response['onus']:
            if(onu['sn'] == item['serial']):
                encontrada = onu['id']

        if(not encontrada):
            update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': "Onu não encontrada na lista de autorizadas na OLT.",
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })       
            raise Exception("Onu não encontrada na lista de autorizadas na OLT.")

        device['onuid'] = encontrada
        updatedevice(device)
        update_job({
            'id': args[0], 
            'status' :'finished', 
            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })      
  
    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })       
        raise Exception("Erro ao adicionar a onu no banco de dados")

def task_authorization(*args, **kwargs):

    try:
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        item = kwargs

        device = {
            "olt": item["olt"],
            "olt_ip" : item["olt_ip"],
            "olt_modelo": "ZTE",
            "serial" : item["serial"],
            "type": item["modelo"],
            "name" : item["username"],
            "vlan_tr069" : item['vlan_dhcp'],
            "vlan_pppoe" : item['vlan_pppoe'],
            "vlan_iptv_unicast" : item['vlan_iptv'], 
            "vlan_iptv_multicast" : 2,
            "chassi": 1,
            "slot": item["placa"],
            "pon": item["porta"]
        }

        if 'mac' in item:
            device["mac"] = item["mac"]

        if 'patrimonio' in item:  
            device["patrimonio"] = item["patrimonio"]

        if 'modelo' in item:
            device["onu_modelo"] = item["modelo"]

        manager = ZTEManager(item['olt_ip'], item['olt'])
        response = manager.auth(item, True) #bypass=True para nao verificar se a onu ja esta autorizada
        # verifica se a onu realmente foi autorizada
        if(response):
            response = manager.find_auth(device['serial'])
            if(response == False):
                update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': f"Onu não encontrada na lista de autorizadas na OLT {item['olt_ip']}",
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                })       
                raise Exception(f"Onu não encontrada na lista de autorizadas na OLT {item['olt_ip']}")
            else:
                update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                })      
                manager.disconnect()
                del manager
                return True
        else:
            update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(response),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })       
            raise Exception("Erro em task_authorization: {}".format(str(response)))   
        
    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })       
        raise Exception("Erro ao autorizar a onu em {}".format(item["olt_ip"]))


def task_deauthorizefh(*args, **kwargs):
    
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        # remove a onu de todas olts fiberhome
        olts = unm_oltlist()
    
        for olt in olts:
            removewhitelist(olt["ip"], item["serial"])

        # desautoriza todas as onus nomeadas com o mesmo username, exceto a nova
        provisions = findauthorizedonu(None, item["username"])
        for provision in provisions:
            if(provision['serial'].upper() != item["serial"].upper()):
                removewhitelist(provision['olt_ip'], provision['serial'])
        
        update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro em deauthorizefh: {}".format(str(e)))   


def task_deauthorizezte(*args, **kwargs):
    
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
       
        # se a onu ja estava autorizada em uma olt ZTE: 
        previous_device = getdevice(item["serial"])
        if(previous_device):
            manager = ZTEManager(previous_device['olt_ip'], previous_device['olt'])
            response = manager.deauth(None, previous_device['chassi'], previous_device['slot'], previous_device['pon'], previous_device['onuid'])
            manager.disconnect()
            del manager
            if(response==True):
              #remove da tabela devices
              removedevice(serial=item["serial"])

        update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro ao deauthorizezte: {}".format(str(e)))

def task_config(*args, **kwargs):

    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })

        device = getdevice(item["serial"])
        if(device):

            manager = ZTEManager(item['olt_ip'], item['olt'])
            response = manager.config_onu(item['chassi'], item['slot'], item['pon'], device['onuid'], int(item['vlan_dhcp']), int(item['vlan_pppoe']), int(item['vlan_iptv']), 2, item["fiberhome"])
            manager.disconnect()
            del manager
            if(response):
                update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                })
            '''
            #verifica se a configuracao foi bem sucedida
            if(response):
                manager2 = ZTEManager(item['olt_ip'], item['olt'])
                response = manager2.show_config_onu(item["serial"])
                if isinstance(response, dict) and "interface" in response:
                    
                    encontrada = False
                    
                    for chave, valor in response.items():
                        if isinstance(valor, list):
                            for item in valor:
                                if 'TELEMIDIA-NAVEGACAO' in item:
                                    encontrada = True

                    if(not encontrada):
                        # se a configuracao nao foi encontrada, atualiza o job e levanta uma excecao
                        # para que o provisionamento seja interrompido
                        # e o usuario seja notificado
                        update_job({
                            'id': args[0], 
                            'status' :'failed', 
                            'exc_info': f"A configuração não foi encontrada na ONU {item['serial']} na OLT {item['olt_ip']}",
                            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                        })       

                        raise Exception("A configuração não foi encontrada na ONU")
                    else:
                        update_job({
                            'id': args[0], 
                            'status' :'finished', 
                            'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                        })    
                else:
                    update_job({
                        'id': args[0], 
                        'status' :'failed', 
                        'exc_info': f"Não possui interface configurada.",
                        'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                    })       
                    raise Exception(f"Não possui interface configurada na OLT {item['olt_ip']}: {str(response)}")
            else:
                update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': "Não obteve resposta da OLT",
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
                })       
                raise Exception("Não obteve resposta da OLT")
            '''    
            # desconecta do manager
            #manager.disconnect()
            #del manager
        else:
            update_job({
                'id': args[0], 
                'status' :'failed', 
                'exc_info': f"Dispositivo {item['serial']} não encontrado no banco de dados",
                'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
            })       
            raise Exception("Dispositivo {} não encontrado no banco de dados".format(item["serial"]))

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro ao configurar a ONU: {}".format(str(e)))   