from flask import jsonify
import re
from db import get_serial_by_mac

# -----------------------------------------------------

def response_success(status='OK', msg='', data={}):
    response = {
        'status': status,
        'msg': msg,
        'data': data
    }
    return jsonify(response)

# -----------------------------------------------------

def response_error(status='ERROR', msg='', data={}):
    response = {
        'status': status,
        'msg': msg,
        'data': data
    }
    return jsonify(response)

# -----------------------------------------------------

def is_mac_address(input_str):
    """
    Verifica se o input é um endereço MAC

    Parameters
    ----------
    input_str : str
        String de entrada

    Returns
    -------
    bool
        True se for MAC, False se for serial
    """
    # Remove todos os caracteres não hexadecimais
    clean_input = re.sub(r'[^A-Fa-f0-9]', '', input_str)

    # MAC tem exatamente 12 caracteres hexadecimais
    # Serial geralmente tem mais caracteres e pode conter outros caracteres
    if len(clean_input) == 12 and re.match(r'^[A-Fa-f0-9]{12}$', clean_input):
        return True
    return False

# -----------------------------------------------------

def get_serial_from_mac_or_serial(mac_or_serial):
    """
    Obtém o serial a partir de MAC ou serial

    Parameters
    ----------
    mac_or_serial : str
        MAC ou serial da ONU

    Returns
    -------
    str or None
        Serial da ONU
    """
    if is_mac_address(mac_or_serial):
        # É um MAC, buscar o serial
        return get_serial_by_mac(mac_or_serial)
    else:
        # É um serial, retornar como está
        return mac_or_serial

# -----------------------------------------------------

def normalize_signal_data(signal_data, olt_type):
    """
    Padroniza os dados de sinal para um formato comum

    Parameters
    ----------
    signal_data : dict
        Dados brutos do sinal da OLT
    olt_type : str
        Tipo da OLT ('FBT' ou 'ZTE')

    Returns
    -------
    dict
        Dados padronizados do sinal
    """
    try:
        normalized = {
            'onu_rx_power': None,  # Sinal de recepção na ONU
            'onu_tx_power': None,  # Sinal de transmissão da ONU
            'olt_rx_power': None   # Sinal de recepção do OLT
        }

        if olt_type == 'FBT':
            # Para FiberHome
            normalized['onu_rx_power'] = signal_data.get('recvpower')
            normalized['onu_tx_power'] = signal_data.get('sendpower')
            normalized['olt_rx_power'] = signal_data.get('oltrecvpower')

        elif olt_type == 'ZTE':
            # Para ZTE, extrair valores numéricos removendo "(dbm)"
            if signal_data.get('down') and signal_data['down'].get('onu'):
                onu_rx = signal_data['down']['onu']
                # Remove "(dbm)" e extrai o número
                normalized['onu_rx_power'] = re.sub(r'\(.*?\)', '', onu_rx).strip()

            if signal_data.get('up') and signal_data['up'].get('onu'):
                onu_tx = signal_data['up']['onu']
                normalized['onu_tx_power'] = re.sub(r'\(.*?\)', '', onu_tx).strip()

            if signal_data.get('up') and signal_data['up'].get('olt'):
                olt_rx = signal_data['up']['olt']
                normalized['olt_rx_power'] = re.sub(r'\(.*?\)', '', olt_rx).strip()

        return normalized

    except Exception as e:
        print(f"Erro ao normalizar dados de sinal: {e}")
        return {
            'onu_rx_power': None,
            'onu_tx_power': None,
            'olt_rx_power': None
        }

# -----------------------------------------------------