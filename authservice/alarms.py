#!/usr/bin/python
# -*- coding: utf-8 -*-

import pexpect
from config import *
from database import *
import time
import sys
import traceback
from genieacs import *

scriptname = 'ALARMS[************]'

def start_alarm():
  try:
    alarms = getalarms()
    onus = []

    for alarm in alarms:

      str = alarm['calarmexinfo']
      #str = 'physical ID=FHTT022c3ba8;SN:LOID=;SN:password=;|physical ID=ZTEGc8cbf6d7;SN:LOID=;SN:password=;|'
      str = str[:-1]
      rows = str.split('|')
      for row in rows:
        onu = {}
        row = row[:-1]
        fields = row.split(';') 
        for field in fields:
          value = field.split('=')
          if(value[0].lower() == 'physical id'):
            if(alarm['olt_ip'] not in [m['ip'] for m in monitors()]):
              #if(alarm['olt_ip'] !== '***********'):
                onu['serial'] = value[1]
                onu['olt'] = alarm['olt']
                onu['olt_ip'] = alarm['olt_ip']
                s = alarm['clocationinfo'] 
                onu['placa'] = int(s[s.find("[")+1:s.find("]")])
                onu['porta'] = int(alarm['porta'])
                onu['data'] = alarm['data']
                
                onu['mac'] = None
                onu['username'] = None 
                onu['modelo'] = None 
                onu['patrimonio'] = None 
                onu['login_ativo'] = None
                onu['data_comodato'] = None
                onu['id_comodato'] = None
                onu['status'] = 'WAITING'

                patrimonio = getpatrimonio_alocado(onu['serial'])  

                if(patrimonio):
                  onu['mac'] = patrimonio['mac']
                  onu['modelo'] = patrimonio['modelo']
                  onu['patrimonio'] = patrimonio['patrimonio']
                  onu['login_ativo'] = patrimonio['login_ativo']
                  onu['data_comodato'] = patrimonio['data_comodato']
                  onu['id_comodato'] = patrimonio['id_comodato']
                  onu['username'] = patrimonio['login']  
                  onu['login_ativo'] = patrimonio['login_ativo']
                  onu['plano'] = patrimonio['plano']
                  
                  if('ZTE' in onu['serial'].upper()):
                    onu['tr069'] = 1
                  # onu['modelo'] = 'GPON CURRENCY SFU'
                  else:
                    onu['tr069'] = 0  
                if(((len(ONUS_TESTE) > 0) and (onu['serial'].upper() in ONUS_TESTE) and (TEST_MODE)) or (TEST_MODE == False)): 
                  if(IGNORE_LIST_ENABLED):
                    if(checkignorelist(serial=onu["serial"])):
                      msg = '[{}]  ONU {} esta na lista para ignorar e nao sera autorizada'.format(scriptname, onu["serial"])
                      print(msg)
                    else:  
                      onus.append(onu)  
                  else:
                    onus.append(onu)

    update_unauthorized(onus, scriptname)
  except:
    errorlog(traceback.format_exc())
    raise


if __name__ == '__main__':
  try:

    start_alarm()

  except Exception as e:
    print('Ocorreu um erro em __main__: '+str(e))    
