
from flask import request, jsonify, render_template, Blueprint
import json
from db import *
from cli import *
import tasks_fiberhome
import tasks_zte
from rq import Queue, Retry
from rq.job import Job
from worker_config_queue import conn
from dotenv import load_dotenv
import os
from models import token_required

vlan_route = Blueprint('preset_route', __name__, template_folder='templates')
q = Queue('preset_queue', connection=conn) 

@preset_route.route('/preset', methods=['POST'])
@token_required
def add_preset():
    if(request.form):
        params = request.form
    else:
        if(request.data):
            params = json.loads(request.data.decode())
        else:
            params = []
    
    required_params = ("olt","serial", "vlan_id", "vlan_type")
    if all (k in params for k in required_params):
        olt = params['olt']
        serial = params['serial']
        olt_type = olt_model(olt)
        
        job_preset = q.enqueue(tasks_preset.task_addpreset, kwargs=(params), retry=Retry(max=5))
        return jsonify({'status': 'OK', 'msg' : job_veip.get_id()}) 

    else:
        return jsonify({'status' : 'ERROR', 'msg': "Missing params: {}".format(', '.join(set(required_params).difference(set(params))))})

