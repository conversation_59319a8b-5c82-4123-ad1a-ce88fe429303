from ztemanager import ZTEManager

def task_addvlan(*args, **kwargs):
    try:
        item = kwargs
        manager = ZTEManager(item['olt'], item['olt'])
        response = manager.add_vlan(item['serial'], item['vlan_id'], item.get('vlan_port'), item['vlan_type'])
        manager.disconnect()
        del manager
        return response
    except Exception as e:
        raise Exception("Erro ao adicionar a vlan {} na porta {}: {}".format(item['vlan_id'], item['vlan_port'], str(e)))        

def task_removevlan(*args, **kwargs):
    try:
        item = kwargs
        manager = ZTEManager(item['olt'], item['olt'])
        response = manager.remove_vlan(item['chassi'], item['slot'], item['pon'], item['onuid'], item['port'])
        manager.disconnect()
        del manager
        return response
    except Exception as e:
        raise Exception("Erro ao remover a vlan na porta {}: {}".format(item['port'], str(e)))        

def task_auth(*args, **kwargs):
    try:
        request_data = kwargs
        if all (k in request_data for k in ("olt","serial", "type", "name")):
            olt = request_data['olt']
            serial = request_data['serial']
            type = request_data['type']
            name = request_data['name']
            device = {
                "olt" : request_data['olt'],
                "serial": request_data['serial'],
                "type" : request_data['type'],
                "name" : request_data['name']
            }

            manager = ZTEManager(olt, olt)
            response = manager.auth(serial, type, name)
            manager.disconnect()
            del manager
            if(response):
                if('onuid' in response):
                    device["onuid"] = response["onuid"]
                    device["chassi"] = response["chassi"]
                    device["slot"] = response["slot"]
                    device["pon"] = response["pon"]
                    device["olt_ip"] = response["olt_ip"]
                    device["olt_modelo"] = "ZTE"
                    updatedevice(device)
                return response
            else:
                raise Exception("Erro ao autorizar onu {} na olt {}: {}".format(device['serial'], device['olt'], str(e)))        

    except Exception as e:
        raise Exception("Erro ao autorizar onu {} na olt {}: {}".format(device['serial'], device['olt'], str(e)))        

def task_deauthorize(*args, **kwargs):
    
    try:
        item = kwargs
        # se a onu ja estava autorizada em uma olt ZTE: 
        previous_device = getdevice(item["serial"])
        if(previous_device):
            manager = ZTEManager(previous_device['olt_ip'], previous_device['olt'])
            response = manager.deauth(None, previous_device['chassi'], previous_device['slot'], previous_device['pon'], previous_device['onuid'])
            manager.disconnect()
            del manager
            if(response==True):
                #remove da tabela devices
                removedevice(serial=item["serial"])
                return True
            else:
                raise Exception("Erro ao desautorizar onu {} na olt {}: {}".format(item['serial'], item['olt'], str(e)))        
        else:
            raise Exception("Device not found")        

    except Exception as e:
        raise Exception("Erro em deauthorize: {}".format(str(e)))