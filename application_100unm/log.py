#!/usr/bin/python
# -*- coding: utf-8 -*-

import logging

# Create a custom logger
logger = logging.getLogger('authservice')

# Create handlers
c_handler = logging.StreamHandler()
f_handler = logging.FileHandler('authservice.log')
c_handler.setLevel(logging.INFO)
f_handler.setLevel(logging.ERROR)

# Create formatters and add it to handlers
c_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
f_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
c_handler.setFormatter(c_format)
f_handler.setFormatter(f_format)

# Add handlers to the logger
logger.addHandler(c_handler)
logger.addHandler(f_handler)

def errorlog(log):
    logger.error(log)

