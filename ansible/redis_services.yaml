---
- hosts: redis
  remote_user: root
  vars:
      container: redis
  tasks:
    - name: <PERSON><PERSON><PERSON> pacotes necessarios
      ansible.builtin.yum:
        name:
          - epel-release
          - redis
        state: present 
      become: yes
      become_user: root 
    - name: Configura a senha de acesso
      ansible.builtin.lineinfile:
        path: /etc/redis.conf
        search_string: '# requirepass foobared'
        line:  requirepass Yjk4NGM0N2UzYTQ5NmI4ZTBkYTJmM2I1
        owner: root
        group: root
        mode: '0644'
      become: yes
      become_user: root    
    - name: Configura o bind no ip de gerencia
      ansible.builtin.lineinfile:
        path: /etc/redis.conf
        search_string: 'bind 127.0.0.1'
        line:  bind {{ ansible_ssh_host }}
        owner: root
        group: root
        mode: '0644'
      become: yes
      become_user: root      