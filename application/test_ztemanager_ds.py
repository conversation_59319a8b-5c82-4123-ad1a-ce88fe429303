from ztemanager_ds import ZTEManager

if __name__ == '__main__':

    with ZTEManager(
        host='*************'
    ) as manager:
        # Configuração inicial
        manager.disable_paging()
        
        # ZTEGD115E271
        #response = manager.list_auth(1, 1, 1)
        response = manager.provision(chassi=1, slot=1, pon=1, serial='ZTEGD115E271', type='F670L', name='onuteste', vlan_tr069=10, vlan_pppoe=901, vlan_iptv_unicast=240, vlan_iptv_multicast=2, fiberhome=False, bridge=True)
        print(response)

    #print(manager.provision())
    #print(manager.list_auth(1, 1, 1))