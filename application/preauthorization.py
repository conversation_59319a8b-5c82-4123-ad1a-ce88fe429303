#!/usr/bin/env python
# coding: utf-8

# Pré autorização de ONUs para migração de OLTs

from database_dev import getonus, getpatrimonio_alocado, get_login, update_provision, update_job, updatedevice
from tasks_zte_dev import task_authorization, task_config, task_adddevicetodb
import datetime
import time
import uuid
import json
from worker_provision_queue import conn
from rq import Queue, Retry
from rq.job import Job

"""
Pre provisionamento de ONUs para migração de OLTs
"""

q = Queue('pre_provision_queue', connection=conn)

scriptname = 'pre-authorization'

olt_origem = {
    "name": "OLT Cps Osvaldo Cruz_1",
    "ip": "**********",
    "slot": "3",
    "pon": "16"
}    

olt_destino = {
    "name": "OLT Cps ZTE_OsvaldoCruz_01",
    "ip": "***************",
    "slot": "3",
    "pon": "16",
    "vlan_dhcp": "10",
    "vlan_pppoe": "103",
    "vlan_iptv": "234",
    "vlan_iptv_multicast": "2"
  }  


'''
olt_origem = {
    "name": "OLT Cps Osvaldo Cruz_1",
    "ip": "**********",
    "slot": "1",
    "pon": "1"
}    
'''

'''
olt_destino = {
    "name": "OLT Cps ZTE_OsvaldoCruz_01",
    "ip": "***************",
    "vlan_dhcp": "10",
    "vlan_pppoe": "101",
    "vlan_iptv": "234"
}  
'''

'''
olt_destino = {
    "name": "OLT And Homologacao",
    "ip": "*************",
    "vlan_dhcp": "10",
    "vlan_pppoe": "101",
    "vlan_iptv": "234"
}  
'''



def job_to_json(job):
    job_result = Job.fetch(job.get_id(), connection=conn)
    job_json = {
        'id' : job_result.get_id(),
        'enqueued_at' : job_result.enqueued_at,
        'status' : job_result.get_status(refresh=True),
        'started_at' : job_result.started_at,
        'ended_at' : job_result.ended_at,
        'exc_info' : job_result.exc_info,
        'worker_name' : job_result.worker_name,
        'last_heartbeat' : job_result.last_heartbeat,
        'origin': job_result.origin,
        'func_name' : job_result.func_name,
        'kwargs': json.dumps(job_result.kwargs, indent=4, sort_keys=True, default=str)
    }
    return job_json

def task_provision(item):

    current_provision= {
        'id' : str(uuid.uuid4()), 
        'serial': item['serial'],
        'username': item['username'],
        'olt_ip': item["olt_ip"],
        'olt': item["olt"],
        'olt_model': item["olt_model"],
        'slot': item['placa'],
        'pon': item['porta'],
        'model': item['modelo'],
        'source': item['source'],
        'status': 'queued' #queued, started, deferred, finished, stopped, scheduled, canceled, failed
    }

    provision_id = update_provision(current_provision)
    item['provision_id'] = provision_id
    item['vlan_dhcp'] = int(olt_destino['vlan_dhcp'])
    item["vlan_pppoe"] = int(olt_destino['vlan_pppoe'])
    item["vlan_iptv"] = int(olt_destino['vlan_iptv'])
    item["fiberhome"] = 'AN5506' in item["modelo"]

    jobs = []

    print(item)

    job_id = str(uuid.uuid4())
    job_authorization = q.enqueue(task_authorization, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id)
    job = job_to_json(job_authorization)
    job['description'] = 'AUTHORIZATION'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_authorization.get_id())

    job_id = str(uuid.uuid4())
    job_database = q.enqueue(task_adddevicetodb, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id, depends_on=job_authorization)
    job = job_to_json(job_database)
    job['description'] = 'DATABASE'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_database.get_id())

    job_id = str(uuid.uuid4())
    job_config = q.enqueue(task_config, args=([job_id]), kwargs=(item), retry=Retry(max=5, interval=5), job_id=job_id, depends_on=job_database)
    job = job_to_json(job_config)
    job['description'] = 'CONFIG ONU'
    job['provision_id'] = provision_id
    update_job(job)
    jobs.append(job_config.get_id())

   
    

    current_provision['started_at'] = datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
    update_provision(current_provision)

    return jobs

def process(unauth_list):

    for unauth in unauth_list:

        onu = {
            'olt': olt_destino['name'],
            'olt_ip': olt_destino["ip"],
            'olt_model': 'ZTE',
            'onu_type': unauth['modelo'],
            'type': unauth['modelo'],
            'chassi' : 1,
            'username': None,
            'slot': olt_destino['slot'],
            'pon': olt_destino['pon'],
            'placa': olt_destino['slot'],
            'porta': olt_destino['pon'],
            'serial': unauth['serial'],
            'source': scriptname,
            'data' : datetime.datetime.now(),
            'mac' : None,
            'login' : None, 
            'patrimonio': None,
            'login_ativo': None,
            'data_comodato': None,
            'id_comodato':  None,
            'status': 'WAITING'
        }

        # consulta se a onu esta alocada
        patrimonio = getpatrimonio_alocado(onu['serial']) 

        if(patrimonio):
            onu['mac'] = patrimonio['mac']
            onu['patrimonio'] = patrimonio['patrimonio']
            onu['login_ativo'] = patrimonio['login_ativo']
            onu['data_comodato'] = patrimonio['data_comodato']
            onu['id_comodato'] = patrimonio['id_comodato']
            #onu['username'] = patrimonio['login']  
            onu['login_ativo'] = patrimonio['login_ativo']
            onu['plano'] = patrimonio['plano']
            onu['modelo'] = patrimonio['modelo']
            onu['type'] = patrimonio['modelo']
            onu['onu_type'] = patrimonio['modelo']

            if('ZTE' in onu['serial'].upper()):
                onu['tr069'] = 1
            else:
                onu['tr069'] = 0  

            login = get_login(onu['mac'])

            if(login):
                onu['login'] = login 
                onu['username'] = login    

                # padroniza o modelo conforme os types liberados nas olts
                if('670' in onu['modelo']):
                    onu['modelo'] = 'F670L' 
                    onu['onu_modelo'] = 'F670L'
                elif('6600' in onu['modelo']):
                    onu['modelo'] = 'F6600P' 
                    onu['onu_modelo'] = 'F6600P'
                elif('660' in onu['modelo']):
                    onu['modelo'] = 'F660'
                    onu['onu_modelo'] = 'F660'
                elif('ZS' in onu['modelo']):
                    onu['modelo'] = 'AN5506-02-B'
                    onu['onu_modelo'] = 'AN5506-02-B'
                elif('WK' in onu['modelo']):
                    onu['modelo'] = 'AN5506-02-B'
                    onu['onu_modelo'] = 'AN5506-02-B'    
                elif('GJ' in onu['modelo']):
                    onu['modelo'] = 'AN5506-02-B'
                    onu['onu_modelo'] = 'AN5506-02-B'    
                elif('HX' in onu['modelo']):
                    onu['modelo'] = 'AN5506-02-B'
                    onu['onu_modelo'] = 'AN5506-02-B'    
                elif('WJ' in onu['modelo']):
                    onu['modelo'] = 'AN5506-02-B'
                    onu['onu_modelo'] = 'AN5506-02-B'
                elif('LL' in onu['modelo']):
                    onu['modelo'] = 'AN5506-02-B'
                    onu['onu_modelo'] = 'AN5506-02-B'    
                elif('DZ' in onu['modelo']):
                    onu['modelo'] = 'AN5506-04-A1'
                    onu['onu_modelo'] = 'AN5506-04-A1'
                elif('HG' in onu['modelo']):
                    onu['modelo'] = 'AN5506-04-A1'
                    onu['onu_modelo'] = 'AN5506-04-A1'        
                else:
                    onu['onu_modelo'] = onu['modelo']

                print(f"ONU {onu['serial'].upper()} sera provisionada") 
                #with open(f"placa_{unauth['placa']}_porta_{unauth['porta']}.txt", 'a', encoding='utf-8') as f:
                #    f.write(onu['serial'].upper()+'\n')
                task_provision(onu)



onus = getonus(olt=olt_origem['ip'], slot=olt_origem['slot'], pon=olt_origem['pon'])
#onus = onus[1:2]

process(onus)

#print(onus)




