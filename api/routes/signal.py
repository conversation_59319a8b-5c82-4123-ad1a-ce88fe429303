from flask import jsonify, Blueprint
from db import *
from models import token_required
from fhmanager import FHManager
from ztemanager import ZTEManager
from utils import get_serial_from_mac_or_serial, normalize_signal_data

signal_route = Blueprint('signal_route', __name__, template_folder='templates')

# url_prefix: '/onu'
@signal_route.route('/signal/<mac_or_serial>', methods=['GET'])
@token_required
def get_onu_signal(mac_or_serial):
    try:
        # Determinar se é MAC ou serial e obter o serial
        serial = get_serial_from_mac_or_serial(mac_or_serial)

        if not serial:
            return jsonify({'status': 'ERROR', 'msg': 'MAC/Serial não encontrado ou inválido'})

        # Verificar tipo e IP da OLT através do serial
        olt_info = get_olt_info_by_serial(serial)

        if not olt_info:
            return jsonify({'status': 'ERROR', 'msg': 'Serial não encontrado ou não vinculado a um OLT'})

        olt_type = olt_info['fabricante_modelo']
        olt_ip = olt_info['ip']

        if olt_type == 'FBT':
            # Para FiberHome, precisamos dos parâmetros da ONU autorizada
            onu_info = findauthorizedonu(serial=serial)

            if not onu_info:
                return jsonify({'status': 'ERROR', 'msg': 'ONU não encontrada no sistema FiberHome'})

            # Conectar ao FHManager e obter sinal
            manager = FHManager(olt_ip)
            manager.connect()

            signal_data = manager.get_signal(
                onu_info['placa'],
                onu_info['porta'],
                onu_info['onuid'],
                onu_info['serial'],
                onu_info['nome']
            )

            manager.disconnect()

            # Normalizar dados do sinal
            normalized_signal = normalize_signal_data(signal_data, 'FBT')

            return jsonify({
                'status': 'OK',
                'olt_type': 'FBT',
                'olt_ip': olt_ip,
                'signal': normalized_signal,
                'raw_signal': signal_data  # Manter dados originais para debug
            })

        elif olt_type == 'ZTE':
            # Para ZTE, a conexão é feita automaticamente no construtor
            # Instanciar ZTEManager e obter sinal
            manager = ZTEManager(olt_ip)

            signal_data = manager.show_signal(serial)

            manager.disconnect()

            # Normalizar dados do sinal
            normalized_signal = normalize_signal_data(signal_data, 'ZTE')

            return jsonify({
                'status': 'OK',
                'olt_type': 'ZTE',
                'olt_ip': olt_ip,
                'signal': normalized_signal,
                'raw_signal': signal_data  # Manter dados originais para debug
            })

        else:
            return jsonify({'status': 'ERROR', 'msg': f'Tipo de OLT não suportado: {olt_type}'})

    except Exception as e:
        return jsonify({'status': 'ERROR', 'msg': f'Erro ao consultar sinal: {str(e)}'})
