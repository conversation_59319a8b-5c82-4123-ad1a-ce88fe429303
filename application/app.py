import requests
import os
from rq import Queue
from rq.job import Job
from worker_provision_queue import conn
from flask import Flask, request, jsonify
import json
import time
import rq_dashboard
#import rqmonitor
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
redis_host = os.getenv('REDIS_HOST', 'localhost')
redis_port = os.getenv('REDIS_PORT', 6379)
redis_user = os.getenv('REDIS_USER', 'admin')
redis_pass = os.getenv('REDIS_PASS', 'admin')

app.config.from_object(rq_dashboard.default_settings)
#app.config.from_object(rqmonitor.defaults)
app.config["RQ_DASHBOARD_REDIS_URL"] = 'redis://{}:{}@{}:{}'.format(redis_user, redis_pass, redis_host, redis_port)
#app.config["RQ_MONITOR_REDIS_URL"] = 'redis://{}:{}@{}:{}'.format(redis_user, redis_pass, redis_host, redis_port)

app.config["RQ_DASHBOARD_USERNAME"] = os.getenv('RQ_USER', 'admin')
app.config["RQ_DASHBOARD_PASSWORD"] = os.getenv('RQ_PASS', 'admin')

app.register_blueprint(rq_dashboard.blueprint, url_prefix="/rq")
#app.register_blueprint(rqmonitor.monitor_blueprint, url_prefix="/rq")


if __name__ == '__main__':
	try:
		app.run(port=5005, host='0.0.0.0', debug=True) #, ssl_context=('/etc/letsencrypt/live/api.telemidia.net.br/cert.pem', '/etc/letsencrypt/live/api.telemidia.net.br/privkey.pem'))
	except Exception as e:
		app.logger.error(str(e))

