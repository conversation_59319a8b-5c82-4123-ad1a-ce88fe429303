#!/usr/bin/python
# -*- coding: utf-8 -*-

from fhmanager import FHManager
from db import *
from dotenv import load_dotenv
import os
from cli import *
from tl1 import *
from ixc_ws import *

load_dotenv()

#consulta sinal na olt
def task_getsignal(*args, **kwargs):
    item = kwargs
    manager = FHManager(item['olt'])
    manager.connect()
    onus = onu_list(olt_ip=item['olt'], placa=item['placa'], porta=item['porta'])
    for onu in onus:
        signal = manager.get_signal(
            item['placa'], item['porta'], onu['onuid'], onu['serial'], onu['nome'])
        insert_signal(signal)
    manager.disconnect()

#adiciona vlan no veip
def task_addveip(*args, **kwargs):
    try:
        item = kwargs
        #verifica se a onu esta autorizada na olt
        check = checkwhitelist(item)
        if(check):
            item['onuid'] = check['onuid']
            item['placa'] = check['placa']
            item['porta'] = check['porta']
        else:
            raise Exception("ONU não localizada na OLT")

        veip_addvlan(item, item["service_id"], item["vlan_id"], os.getenv('TR069_PROFILEID'))
        
    except Exception as e:
        raise Exception("Erro ao adicionar a vlan {} no veip: {}".format(item["vlan_id"], str(e)))        

#verifica vlan no veip
def task_checkveip(*args, **kwargs):
    try:
        item = kwargs
        #verifica se a vlan foi adicionada no veip
        check = checkveip(item, item["vlan_id"])
        if(check):
            return True
        else:
            raise Exception("Erro ao verificar a vlan {} no veip".format(item["vlan_id"]))  
    except Exception as e:
        raise Exception("Erro ao verificar a vlan {} no veip".format(item["vlan_id"]))  


def checkvlan(onu, porta, vlan):
    try:
        exists = False
        onu_vlans = onuvlans(onu,porta)
        if(onu_vlans):
            for item in onu_vlans:
                if(int(item['CVLAN']) == int(vlan)):
                    exists = True
        return exists
    except Exception as e:
        return False

def checkveip(onu, vlan):
    try:
        exists = False
        veip_vlans = check_veip_vlans(onu)
        for veip in veip_vlans:
            if(int(veip['vlan']) == int(vlan)):
                return True
        return exists
    except Exception as e:
        return False    


def task_checkauthorization(*args, **kwargs):
    item = kwargs

    auth = checkwhitelist(item)
    if checkwhitelist(item):
        return True
    else:
        return False

#remove vlans
def task_removevlans(*args, **kwargs):
    item = kwargs
    try:
        onuremovevlans(item)
    except Exception as e:
        raise Exception("Erro ao remover as vlans: {}".format(str(e)))   

#remove vlan
def task_removevlan(*args, **kwargs):
    params = kwargs
    try:
        manager = FHManager(params['olt'])
        manager.connect()
        vlan_removed = manager.remove_vlan(params['serial'], params['port'])
        manager.disconnect()
        
        if type(vlan_removed) == str:
            raise Exception(vlan_removed)
        elif vlan_removed is not True:
            raise Exception('Erro ao remover VLAN')
        
    except Exception as e:
        raise Exception("Erro ao remover a vlan: {}".format(str(e)))   

def task_addvlan(*args, **kwargs):
    try:
        item = kwargs
        #verifica se a onu esta autorizada na olt
        check = checkwhitelist(item)
        if(check):
            item['onuid'] = check['onuid']
            item['placa'] = check['placa']
            item['porta'] = check['porta']
        else:
            raise Exception("ONU não localizada na OLT")
            
        time.sleep(0.2)

        if(item['vlan_type'] == 'unicast'):
            onuaddvlanunicast(item, item['vlan_port'], item['vlan_id'])
        elif(item['vlan_type'] == 'multicast'):
            onuaddvlanmulticast(item, item['vlan_port'], item['vlan_id'])            

        time.sleep(0.2)

        #verifica se a vlan foi adicionada
        check = checkvlan(item, item['vlan_port'], item['vlan_id'])
        if(check):
            return True
        else:
            raise Exception("Erro ao verificar a vlan {} na porta {}".format(item['vlan_id'], item['vlan_port']))    
    except Exception as e:
        raise Exception("Erro ao adicionar a vlan {} na porta {}: {}".format(item['vlan_id'], item['vlan_port'], str(e)))        

def task_checkvlan(*args, **kwargs):
    try:
        item = kwargs

        #verifica se a vlan foi adicionada
        check = checkvlan(item, item['vlan_port'], item['vlan_id'])
        if(check):
            return True
        else:
            raise Exception("Erro ao verificar a vlan {} na porta {}".format(item['vlan_id'], item['vlan_port']))    
    except Exception as e:
        raise Exception("Erro ao verificar a vlan {} na porta {}".format(item['vlan_id'], item['vlan_port']))    

    
def task_authorization(*args, **kwargs):

    try:
        item = kwargs

        #verifica se a onu ja esta autorizada
        check = task_checkauthorization(**item)
        if(check):
            return True

        if('ZTE' in item['serial'].upper()):
            item['modelo'] = 'GPON CURRENCY SFU'
        else:
            patrimonio = getpatrimonio_alocado(item['serial'])  
            if(patrimonio):
                item['modelo'] = patrimonio['modelo']

        response = onuauthorize(item)

        if('error' in response):
            if(response['error'] == True):
                raise Exception("Erro ao autorizar a onu em {}: {} {}".format(item["olt"], response["data"], response["cmd"]))
            else:
                return True
        else:
            raise Exception("Erro ao autorizar a onu em {}".format(item["olt"]))
        
    except Exception as e:
        raise Exception("Erro ao autorizar a onu em {}".format(item["olt"]))

def task_deauthorize(*args, **kwargs):
    
    try:
        item = kwargs
      
        removewhitelist(item["olt"], item["serial"])

    except Exception as e:
        raise Exception("Erro ao desautorizar: {}".format(str(e)))   
        
'''
def task_deauthorize(*args, **kwargs):
    
    try:
        item = kwargs
        update_job({
            'id': args[0], 
            'status' :'started', 
            'started_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })
        # remove a onu de todas olts fiberhome
        olts = unm_oltlist()
    
        for olt in olts:
            removewhitelist(olt["ip"], item["serial"])

        # desautoriza todas as onus nomeadas com o mesmo username, exceto a nova
        provisions = findauthorizedonu(None, item["name"])
        for provision in provisions:
            if(provision['serial'].upper() != item["serial"].upper()):
                removewhitelist(provision['olt_ip'], provision['serial'])
        
        update_job({
                    'id': args[0], 
                    'status' :'finished', 
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          

    except Exception as e:
        update_job({
                    'id': args[0], 
                    'status' :'failed', 
                    'exc_info': str(e),
                    'ended_at': datetime.datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')
        })          
        raise Exception("Erro ao desautorizar: {}".format(str(e)))   
'''        