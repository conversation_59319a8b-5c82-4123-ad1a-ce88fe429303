#!/usr/bin/python
# -*- coding: utf-8 -*-

import pexpect
from dotenv import load_dotenv
import os
import sys

load_dotenv()

def placa(servico):
  try:  
    str = servico
    str = str[str.rfind('-')+1:len(str)]
    str = str.split('_')
    return str[0]
  except Exception as e:
      return str(e)
      errorlog('Falha ao executar placa: '+ str(e))  

def porta(servico):
  try:  
    str = servico
    str = str[str.rfind('-')+1:len(str)]
    str = str.split('_')
    return str[1]
  except Exception as e:
    return str(e)
    errorlog('Falha ao executar porta: '+ str(e))     

# Processa o retorno do TL1 e transforma em array ou boolean
def parsedata(data):
    try:
      data = data.split('\n')
      str = data[9].split('=')
      block_records = int(str[1])
      if(block_records == 0):
        return []
      else:    
        str = data[13].replace('\r', '')
        headers = str.split('\t')
        str = data[14:]
        records = []
        for i in range(block_records):
            x = str[i]
            x = x.replace('\r', '')
            columns = x.split('\t')
            record = {}
            for y in range(len(headers)):
                header = headers[y]
                record[header] = columns[y]
            records.append(record)    
        return records
    except:
      return False

# Executa um comando via TL1
def cmdexec(cmd, returntype):

    try:
        #tn = telnetlib.Telnet(os.getenv('UNMTL1_HOST'), int(os.getenv('UNMTL1_PORT')), 20)
        child = pexpect.spawn('telnet {} {}'.format(os.getenv('UNMTL1_HOST'), int(os.getenv('UNMTL1_PORT')), encoding='utf-8', codec_errors='ignore'))
        # Habilita o debug (retorno na tela)
        #child.logfile = sys.stdout
        #child.expect(b'', int(os.getenv('PEXPECT_TIMEOUT')))
        str = 'LOGIN:::CTAG::UN=%s,PWD=%s;' % (os.getenv('UNMTL1_USER'), os.getenv('UNMTL1_PASS'))
        child.sendline(str)
        child.expect(';', int(os.getenv('PEXPECT_TIMEOUT')))
        child.sendline('\n')
        child.expect(';', int(os.getenv('PEXPECT_TIMEOUT')))
        ret = ""
        ret += child.before.decode('utf-8')

        #print(ret)
        if(ret.find('ENDESC=No error') > 0): 
            child.sendline(cmd)
            child.expect(';', int(os.getenv('PEXPECT_TIMEOUT')))
            child.sendline('\n')
            child.expect(';', int(os.getenv('PEXPECT_TIMEOUT')))
            ret = child.before.decode('utf-8')
            child.close()
            if(returntype == 'array'):
                return parsedata(ret)
            else:
                if(ret.find('ENDESC=No error') > 0): 
                    return {
                        "error" : False
                    }           
                else:
                    raise Exception("Data: {} \n CMD: {}".format(ret, cmd))
                    #return {
                    #"error" : True,
                    #"data" : ret, #.decode('utf-8'),
                    #"cmd": cmd
                    #}    
    except Exception as e:
      raise
#        return {
#            "error" : True,
##            "data" : e
#        }
    

        
# Autoriza a onu
def onuauthorize(onu):

    try:
      cmd = "ADD-ONU::OLTID=%s,PONID=NA-NA-%s-%s:CTAG::AUTHTYPE=MAC,ONUTYPE=%s,ONUID=%s,NAME=%s;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["modelo"], onu["serial"], onu["nome"])
      ret = cmdexec(cmd, "boolean")
      return ret
    except Exception as e:
      raise
      #errorlog('Falha ao executar onuauthorize: '+ str(e))

# Remove todas as VLANs
def onuremovevlans(onu, portas=4):
    try:
      string_portas = ''
      for i in range(portas):
        string_portas += 'NA-NA-NA-{}|'.format(i+1)
      string_portas = string_portas[:-1]
      cmd = "DEL-LANPORTVLAN::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s,ONUPORT=%s:CTAG::;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], string_portas)
      ret = cmdexec(cmd, "boolean")
      return ret
    except Exception as e:
        print(str(e))    
    #  errorlog('Falha ao executar onuremovevlanunicast: '+ str(e))

# Adiciona VLAN Unicast
def onuaddvlanunicast(onu, lan, vlan):
    try:
      cmd = "CFG-LANPORTVLAN::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s,ONUPORT=NA-NA-NA-%s:CTAG::CVLAN=%s,CCOS=1;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], lan, vlan)  
      ret = cmdexec(cmd, "boolean")
      return ret
    except Exception as e:
      print(str(e))  
      #errorlog('Falha ao executar onuaddvlanunicast: '+ str(e))

# Adiciona VLAN Multicast
def onuaddvlanmulticast(onu, lan, vlan):
    try:
      cmd = "ADD-LANIPTVPORT::OLTID=%s,PONID=1-1-%s-%s,ONUIDTYPE=MAC,ONUID=%s,ONUPORT=NA-NA-NA-%s:CTAG::MVLAN=%s,CCOS=1;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], lan, vlan)  
      ret = cmdexec(cmd, "boolean")
      return ret
    except Exception as e:
      print(str(e))
      #errorlog('Falha ao executar onuaddvlanmulticast: '+ str(e))

# Configura autenticacao PPPoE na LAN
def onuppoelan(onu, lan):
    try:
      ret = cmdexec("SET-WANSERVICE::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s:CTAG::STATUS=1,MODE=2,CONNTYPE=2,VLAN=%d,NAT=1,COS=1,QOS=1,IPMODE=3,PPPOEPROXY=2,PPPOEUSER=%s,PPPOEPASSWD=%s,PPPOENAME=%s,PPPOEMODE=1,UPORT=%d;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], lan, onu["vlan"], vlan, onu["username"], onu["senha"], onu["username"], lan), "boolean")
      return ret
    except Exception as e:
      print(str(e))  
      #errorlog('Falha ao executar onupppoelan: '+ str(e))

# Configura autenticacao PPPoE no Wi-fi
def onupppoewifi(onu):
    try:
      ret = cmdexec("SET-WANSERVICE::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s:CTAG::STATUS=1,MODE=2,CONNTYPE=2,VLAN=%d,NAT=1,COS=1,QOS=1,IPMODE=3,PPPOEPROXY=2,PPPOEUSER=%s,PPPOEPASSWD=%s,PPPOENAME=%s,PPPOEMODE=1,SSID=1;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], onu["vlanid"], onu["username"], onu["senha"], onu["username"]), "boolean")
      return ret
    except Exception as e:
      print(str(e))  
      #errorlog('Falha ao executar onupppoewifi: '+ str(e))

# Configura rede Wi-fi
def onuconfigwifi(onu, idequipamento):
    try:
      ret = cmdexec("CFG-WIFISERVICE::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s:CTAG::WILESS-AREA=1,WILESS-CHANNEL=8,WILESS-STANDARD=802.11bgn,T-POWER=20,SSID=1,SSID-ENABLE=1,SSID-NAME=%s,SSID-VISIBALE=0,AUTH-MODE=WPA2PSK,ENCRYP-TYPE=AES,PRESHARED-KEY=%s;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], 'telemidia_{}'.format(idequipamento), onu["senha"]), "boolean")
      return ret
    except Exception as e:
      print(str(e))  
      #errorlog('Falha ao executar onuconfigwifi: '+ str(e))

# Configura DHCP
def onudhcp(onu):
    try:
      ret = cmdexec("CFG-USERDHCPSERVER::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s:CTAG::LANIP=***********,ENABLE=true,DHCPPOOLSTART=***********,DHCPPOOLEND=***********54,DHCPPRIDNS=**************,DHCPSECDNS=*************,DHCPGATEWAY=***********,DHCPMASK=*************;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"]), "boolean")
      return ret
    except Exception as e:
      print(str(e))  
      #errorlog('Falha ao executar onudhcp: '+ str(e))      

# Informacoes de LANs da ONU
def onulans(onu):
    try:
        #onu = findonu(serial)
        #onu = findauthorizedonu(serial)
        if(onu != None):
           lans = cmdexec("LST-ONULANINFO::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s:CTAG::;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"]), "array")
           return lans
        else:
           return False
    except:
        return False    

# Informacoes de VLANs da ONU
def onuvlans(onu, porta):
    #try:
        if(onu != None):
           vlans = cmdexec("LST-PORTVLAN::OLTID=%s,PONID=NA-NA-%s-%s,ONUIDTYPE=MAC,ONUID=%s,ONUPORT=NA-NA-NA-%s:CTAG::;" % (onu["olt_ip"], onu["placa"], onu["porta"], onu["serial"], porta), "array")
           return vlans 
        else:
           return False
    #except:
    #    return False

    